// 公用模块html
headerBar()
footerBar()
var viewModel = {
    activityList: ko.observableArray(),
    goInfo:function(data){
        if(data.leftDay<0){
            window.open('activityDetail.html?id='+data.baseId+'&end=1')
        }else{
            window.open('activityDetail.html?id='+data.baseId)
        }
    },
    monthList:ko.observableArray(),
    activityTime: ko.observable(),
    tYear: ko.observable(),
    bagnnerList: ko.observableArray(),
    attachmentList: ko.observableArray(),
    yearList: ko.observableArray(),
    tMonth: ko.observable(),
    hdmonthList:ko.observableArray(),
    hdTime: ko.observable(),
    hdList:ko.observableArray(),
    selectData: function (data, data2) { //筛选项
        changeSelectStyle(data,data2)
        switch (data) {
            case '1':
                viewModel.tYear(data2.baseName.slice(0,4))
                getMonth()
                break;
            case '2':
                viewModel.hdTime(data2.baseId)
                initHdList()
                break;
            case '3':
                initinfo(data2.baseId)
                break;
        }
    },
}
Object.assign(viewModel, viewModel1);
// 获取年
initYear()
function initYear(){
    ajaxPostDataFull("api-app/v1/sysDictData/listDict?typeNo=CYHD_ND")
    if (PostData.code == 0) {
        viewModel.yearList(PostData.obj)
        var i= PostData.obj.length-1
        viewModel.tYear(PostData.obj[i].baseName.slice(0,4))
    }
}

// 获取活动回顾
function initHdList(){ 
    var obj = {
        pageSize: 5,
        pageNum: 1,
        isHot: 0,
        verifyState: 1,
        yearMonth: viewModel.hdTime(),
    };
    ajaxgetDataFull('api-activity/v1/frontQctActivityReview/qctActivityReviewListPage',obj)
    if(getData.code==0){
        viewModel.hdList(getData.obj.content)
    }
}
//详情
function initinfo(infoid) {
    var obj = {
        baseId: infoid
    }
    ajaxgetDataFull('api-activity/v1/frontQctActivityReview/frontReviewInfo', obj)
    if (getData.code == 0 && getData.obj) {
        var data = getData.obj.sysAttachmentVoList;
        var infoid = getData.obj.baseId
        var newlist = []
        for(var i=0;i<data.length;i++){
            newlist.push({
                fullPath: data[i].fullPath,
                baseId:infoid
            })
        }
        viewModel.attachmentList(newlist)
        $(".rightBox").slide({
            mainCell: ".videoBox",
            titCell:".hd li",
            trigger:"click",
            effect:'fold',
            autoPlay:true,
            interTime:5000,
            startFun: function (i, p) { 
                if (i == 0) { 
                    $(".rightBox .sPrev").click() 
                } else if (i % 6 == 0) { 
                    $(".rightBox .sNext").click() 
                } 
            } 
        });
    } else {
        $.jBox.tip(getData.obj)
    }
}

// banner管理
function initBanner(){
    var obj={
        pageNum:1,
        pageSize:1,
        bannerModule:5
    }
    ajaxgetDataFull('api-qingdao/v1/sysBannerManager/getFrontList',obj)
    if(getData.code==0){
        if(getData.obj.content.length>0){
            viewModel.bagnnerList(getData.obj.content[0].pictureFileList)
            viewModel.bagnnerList.unshift({fullPath:'./image/new_zhdBanner.jpg'})
        }
    }
}
// initBanner()

// 获取月份
getMonth()
function getMonth(){
    var myDate = new Date();
    var tYear = myDate.getFullYear();
    var tMonth = myDate.getMonth()+1;
    viewModel.tMonth(tMonth)
    var arr=[]
    for(var i=1;i<=12;i++){
        if(i<10){
            arr.push({
                baseName:i+'月',
                baseId:viewModel.tYear()+'-0'+i,
                isShow:i<=tMonth?false:true
            })
        }else{
            arr.push({
                baseName:i+'月',
                baseId:viewModel.tYear()+'-'+i,
                isShow:i<=tMonth?false:true
            })
        }
        
    }
    viewModel.monthList(arr)
    if(tMonth<10){
        viewModel.activityTime(tYear+'-0'+tMonth)
        viewModel.hdTime(tYear+'-0'+tMonth)
    }else{
        viewModel.activityTime(tYear+'-'+tMonth)
        viewModel.hdTime(tYear+'-0'+tMonth)
    }

    // initHdList()
}
var obj = {
    pageSize: 5,
    pageNum: 1,
    isHot: 0,
    verifyState: 1,
    yearMonth: viewModel.hdTime(),
};
ajaxgetDataFull('api-activity/v1/frontQctActivityReview/qctActivityReviewListPage',obj)
if(getData.code==0){
    if(getData.obj.content.length>0){
        var data = getData.obj.content[0].sysAttachmentVoList;
        var infoid = getData.obj.content[0].baseId
        var newlist = []
        for(var i=0;i<data.length;i++){
            newlist.push({
                fullPath: data[i].fullPath,
                baseId:infoid
            })
        }
        viewModel.attachmentList(newlist)
    }else{
        var obj = {
            pageSize: 5,
            pageNum: 1,
            isHot: 0,
            verifyState: 1,
            yearMonth: '',
        };
        ajaxgetDataFull('api-activity/v1/frontQctActivityReview/qctActivityReviewListPage',obj)
         var data = getData.obj.content[0].sysAttachmentVoList;
        var infoid = getData.obj.content[0].baseId
        var newlist = []
        for(var i=0;i<data.length;i++){
            newlist.push({
                fullPath: data[i].fullPath,
                baseId:infoid
            })
        }
        viewModel.attachmentList(newlist)
    }
    
}
function activityList() {
    var obj = {
        pageSize: 2,
        pageNum: 1,
        isHot: 0,
        activityTime:viewModel.activityTime(),
        activityType:'47962f2fa0e4ab71133efb9bdd70570a'
    };
    ajaxgetData('api-app/v1/qctActivity/findQctActivityListPage', obj, function(data){
        if(data.code==0){
            if(data.obj.totalCount>=2){
                $('.activityList').show()
                $('.nodataPic2').hide()
                viewModel.activityList(data.obj.content)
            }else{
                var obj = {
                    pageSize: 2,
                    pageNum: 1,
                    isHot: 0,
                    activityTime:'',
                    activityType:'47962f2fa0e4ab71133efb9bdd70570a'
                };
                ajaxgetData('api-app/v1/qctActivity/findQctActivityListPage', obj, function(data){
                    if(data.code==0){
                        if(data.obj.totalCount>0){
                            $('.activityList').show()
                            $('.nodataPic2').hide()
                        }else{
                            $('.activityList').hide()
                            $('.nodataPic2').show()
                        }
                        viewModel.activityList(data.obj.content)
                    }
                });
            }
            
        }
    });
}
activityList()

ko.applyBindings(viewModel, document.getElementById("viewModelBox"));
$('#activityPage').addClass('on')
$(".bannerBox").slide({
    titCell:".hd ul",
    mainCell:".bannerSlide",
    autoPlay:true,
    effect:"fold",
    interTime:5000,
    autoPage:true
});
window.onload = function() {

        // $(".rightBox").slide({
        //     // titCell: ".hd",
        //     mainCell: ".videoBox",
        //     autoPage: true,
        //     effect: "left",
        //     autoPlay: true,
        //     // effect: "leftLoop",
        //     vis: 1
        // });
        $(".rightBox").slide({
            mainCell: ".videoBox",
            titCell:".hd li",
            trigger:"click",
            effect:'fold',
            autoPlay:true,
            interTime:5000,
            startFun: function (i, p) { 
                if (i == 0) { 
                    $(".rightBox .sPrev").click() 
                } else if (i % 6 == 0) { 
                    $(".rightBox .sNext").click() 
                } 
            } 
        });
        $(".dtHd").slide({
            mainCell: "ul",
            vis:6,
            scroll:6,
            effect:"left",
            autoPage: true, 
            prevCell: ".sPrev", 
            nextCell: ".sNext", 
            pnLoop: false ,
            interTime:5000,
        });
}
// 点击年
$(document).on('click','.years',function(){
    $('.monthList').addClass('none')
    $('.months').removeClass('on')
    $('.hdList').addClass('none')
    $('.hds').removeClass('on')
    if($(this).hasClass('on')){//收起
        $(this).removeClass('on')
    }else{//展开
        $('.years').removeClass('on')
        $(this).addClass('on')
        $(this).next().removeClass('none')
    }
})
// 点击月
$(document).on('click','.months',function(){
    $('.hdList').addClass('none')
    $('.hds').removeClass('on')
    if($(this).hasClass('on')){//收起
        $(this).removeClass('on')
    }else{//展开
        $('.months').removeClass('on')
        $(this).addClass('on')
        $(this).parent().find('.hdList').removeClass('none')
    }
})
// 点击活动
$(document).on('click','.hds',function(){
    if($(this).hasClass('on')){//收起
        $(this).removeClass('on')
    }else{//展开
        $('.hds').removeClass('on')
        $(this).addClass('on')
    }
})