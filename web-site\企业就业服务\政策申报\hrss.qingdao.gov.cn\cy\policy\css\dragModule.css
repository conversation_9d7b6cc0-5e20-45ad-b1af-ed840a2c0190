.box { position:fixed; /*top:130px; left:20px;*/ /*bottom:100px; right:20px; *//*min-width:200px;*//* background:#424646;*/ /*overflow: hidden;*/ border-radius: 5px;z-index: 100;}
.title{/*width: 320px;*/    margin: 0 91px;text-align: center; position: absolute;top: -130px;}
.title .headset{
	width: 38px;
	height: 33px;
	left: 50%;
	margin-left: -19px;
	top: 50px;
	z-index: 1000;
	background: url(../images/headset.png) no-repeat;
}
/*动画*/
@-webkit-keyframes move {
	0%{transform: rotate(0deg);}
	100%{transform: rotate(360deg);}
}
@-moz-keyframes move {
	0%{transform: rotate(0deg);}
	100%{transform: rotate(360deg);}
}
@-ms-keyframes move {
	0%{transform: rotate(0deg);}
	100%{transform: rotate(360deg);}
}
@-o-keyframes move {
	0%{transform: rotate(0deg);}
	100%{transform: rotate(360deg);}
}
@keyframes move {
	0%{transform: rotate(0deg);}
	100%{transform: rotate(360deg);}
}
.title .playerBox{
	display:block; 
	width: 138px;
	height: 138px; 
	border-radius: 50%; 
	margin: 0 auto; 
	overflow: hidden; 
	background: url(../images/player.png);
	-webkit-animation: move 2500ms linear infinite;
    -moz-animation: move 2500ms linear infinite;
    -ms-animation: move 2500ms linear infinite;
    animation: move 2500ms linear infinite;
}
/* .box .con .playerBTBox a{
	width: 20px;
	height: 20px;
	background: url(../images/playerCollect.png) no-repeat left center;
}
.box .con .playerBTBox .collect.on{
	background: url(../images/playerCollectOn.png) no-repeat left center;
	
}
.box .con .playerBTBox .share{
	background: url(../images/playerShare.png) no-repeat left center;
} */
.mryt_Search{
	border: none;
}