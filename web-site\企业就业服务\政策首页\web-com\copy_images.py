import os
import shutil

# 源目录和目标目录
source_dir = os.path.join('..', 'hrss.qingdao.gov.cn', 'cy', 'policy', 'images')
target_dir = os.path.join('static_files', 'images')

# 确保目标目录存在
os.makedirs(target_dir, exist_ok=True)

# 要复制的图片列表
images_to_copy = [
    'policySBanner.jpg',
    'lb_bg.jpg',
    'pic_noDetail.png',
    'policySImg2.png',
    'closeIcon.png',
    'ps_popClose.png',
    'ps_popTitleIcon.png',
    'tips.png',
    'tipson.png'
]

# 复制图片
for image in images_to_copy:
    source_path = os.path.join(source_dir, image)
    target_path = os.path.join(target_dir, image)
    
    if os.path.exists(source_path):
        shutil.copy2(source_path, target_path)
        print(f"已复制: {image}")
    else:
        print(f"未找到: {image}")

print("图片复制完成！")
