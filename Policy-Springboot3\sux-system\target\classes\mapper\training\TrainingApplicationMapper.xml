<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sux.system.mapper.TrainingApplicationMapper">
    
    <resultMap type="TrainingApplication" id="TrainingApplicationResult">
        <result property="applicationId"        column="application_id"    />
        <result property="orderId"              column="order_id"    />
        <result property="userId"               column="user_id"    />
        <result property="applicantName"        column="applicant_name"    />
        <result property="applicantPhone"       column="applicant_phone"    />
        <result property="applicantEmail"       column="applicant_email"    />
        <result property="applicantIdCard"      column="applicant_id_card"    />
        <result property="applicantGender"      column="applicant_gender"    />
        <result property="applicantAge"         column="applicant_age"    />
        <result property="applicantEducation"   column="applicant_education"    />
        <result property="applicantExperience"  column="applicant_experience"    />
        <result property="applicantAddress"     column="applicant_address"    />
        <result property="applicationStatus"    column="application_status"    />
        <result property="applicationTime"      column="application_time"    />
        <result property="reviewTime"           column="review_time"    />
        <result property="reviewer"             column="reviewer"    />
        <result property="reviewComment"        column="review_comment"    />
        <result property="applicationNote"      column="application_note"    />
        <result property="createBy"             column="create_by"    />
        <result property="createTime"           column="create_time"    />
        <result property="updateBy"             column="update_by"    />
        <result property="updateTime"           column="update_time"    />
        <result property="remark"               column="remark"    />
        <result property="delFlag"              column="del_flag"    />
        <!-- 关联字段 -->
        <result property="orderTitle"           column="order_title"    />
        <result property="trainingType"         column="training_type"    />
        <result property="startDate"            column="start_date"    />
        <result property="endDate"              column="end_date"    />
    </resultMap>

    <sql id="selectTrainingApplicationVo">
        select ta.application_id, ta.order_id, ta.user_id, ta.applicant_name, ta.applicant_phone, 
               ta.applicant_email, ta.applicant_id_card, ta.applicant_gender, ta.applicant_age, 
               ta.applicant_education, ta.applicant_experience, ta.applicant_address, 
               ta.application_status, ta.application_time, ta.review_time, ta.reviewer, 
               ta.review_comment, ta.application_note, ta.create_by, ta.create_time, 
               ta.update_by, ta.update_time, ta.remark, ta.del_flag,
               tor.order_title, tor.training_type, tor.start_date, tor.end_date
        from training_application ta
        left join training_order tor on ta.order_id = tor.order_id
    </sql>

    <select id="selectTrainingApplicationList" parameterType="TrainingApplication" resultMap="TrainingApplicationResult">
        <include refid="selectTrainingApplicationVo"/>
        <where>  
            ta.del_flag = '0'
            <if test="orderId != null "> and ta.order_id = #{orderId}</if>
            <if test="userId != null "> and ta.user_id = #{userId}</if>
            <if test="applicantName != null  and applicantName != ''"> and ta.applicant_name like concat('%', #{applicantName}, '%')</if>
            <if test="applicantPhone != null  and applicantPhone != ''"> and ta.applicant_phone = #{applicantPhone}</if>
            <if test="applicantEmail != null  and applicantEmail != ''"> and ta.applicant_email = #{applicantEmail}</if>
            <if test="applicationStatus != null  and applicationStatus != ''"> and ta.application_status = #{applicationStatus}</if>
            <if test="reviewer != null  and reviewer != ''"> and ta.reviewer like concat('%', #{reviewer}, '%')</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                and date_format(ta.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                and date_format(ta.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
        </where>
        order by ta.application_time desc
    </select>
    
    <select id="selectTrainingApplicationByApplicationId" parameterType="Long" resultMap="TrainingApplicationResult">
        <include refid="selectTrainingApplicationVo"/>
        where ta.application_id = #{applicationId} and ta.del_flag = '0'
    </select>

    <select id="selectApplicationByOrderIdAndUserId" resultMap="TrainingApplicationResult">
        <include refid="selectTrainingApplicationVo"/>
        where ta.order_id = #{orderId} and ta.user_id = #{userId} and ta.del_flag = '0'
        and ta.application_status in ('0', '1', '2')
        limit 1
    </select>

    <select id="selectApplicationByOrderIdAndPhone" resultMap="TrainingApplicationResult">
        <include refid="selectTrainingApplicationVo"/>
        where ta.order_id = #{orderId} and ta.applicant_phone = #{phone} and ta.del_flag = '0'
        and ta.application_status in ('0', '1', '2')
        limit 1
    </select>

    <select id="countApplicationsByOrderId" parameterType="Long" resultType="int">
        select count(*) from training_application 
        where order_id = #{orderId} and del_flag = '0' and application_status in ('0', '1')
    </select>

    <select id="selectApplicationsByOrderId" parameterType="Long" resultMap="TrainingApplicationResult">
        <include refid="selectTrainingApplicationVo"/>
        where ta.order_id = #{orderId} and ta.del_flag = '0'
        order by ta.application_time desc
    </select>
        
    <insert id="insertTrainingApplication" parameterType="TrainingApplication" useGeneratedKeys="true" keyProperty="applicationId">
        insert into training_application
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="applicantName != null and applicantName != ''">applicant_name,</if>
            <if test="applicantPhone != null and applicantPhone != ''">applicant_phone,</if>
            <if test="applicantEmail != null">applicant_email,</if>
            <if test="applicantIdCard != null">applicant_id_card,</if>
            <if test="applicantGender != null">applicant_gender,</if>
            <if test="applicantAge != null">applicant_age,</if>
            <if test="applicantEducation != null">applicant_education,</if>
            <if test="applicantExperience != null">applicant_experience,</if>
            <if test="applicantAddress != null">applicant_address,</if>
            <if test="applicationStatus != null">application_status,</if>
            <if test="applicationTime != null">application_time,</if>
            <if test="reviewTime != null">review_time,</if>
            <if test="reviewer != null">reviewer,</if>
            <if test="reviewComment != null">review_comment,</if>
            <if test="applicationNote != null">application_note,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            del_flag
         </trim>
         <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="applicantName != null and applicantName != ''">#{applicantName},</if>
            <if test="applicantPhone != null and applicantPhone != ''">#{applicantPhone},</if>
            <if test="applicantEmail != null">#{applicantEmail},</if>
            <if test="applicantIdCard != null">#{applicantIdCard},</if>
            <if test="applicantGender != null">#{applicantGender},</if>
            <if test="applicantAge != null">#{applicantAge},</if>
            <if test="applicantEducation != null">#{applicantEducation},</if>
            <if test="applicantExperience != null">#{applicantExperience},</if>
            <if test="applicantAddress != null">#{applicantAddress},</if>
            <if test="applicationStatus != null">#{applicationStatus},</if>
            <if test="applicationTime != null">#{applicationTime},</if>
            <if test="reviewTime != null">#{reviewTime},</if>
            <if test="reviewer != null">#{reviewer},</if>
            <if test="reviewComment != null">#{reviewComment},</if>
            <if test="applicationNote != null">#{applicationNote},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            '0'
         </trim>
    </insert>

    <update id="updateTrainingApplication" parameterType="TrainingApplication">
        update training_application
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="applicantName != null and applicantName != ''">applicant_name = #{applicantName},</if>
            <if test="applicantPhone != null and applicantPhone != ''">applicant_phone = #{applicantPhone},</if>
            <if test="applicantEmail != null">applicant_email = #{applicantEmail},</if>
            <if test="applicantIdCard != null">applicant_id_card = #{applicantIdCard},</if>
            <if test="applicantGender != null">applicant_gender = #{applicantGender},</if>
            <if test="applicantAge != null">applicant_age = #{applicantAge},</if>
            <if test="applicantEducation != null">applicant_education = #{applicantEducation},</if>
            <if test="applicantExperience != null">applicant_experience = #{applicantExperience},</if>
            <if test="applicantAddress != null">applicant_address = #{applicantAddress},</if>
            <if test="applicationStatus != null">application_status = #{applicationStatus},</if>
            <if test="applicationTime != null">application_time = #{applicationTime},</if>
            <if test="reviewTime != null">review_time = #{reviewTime},</if>
            <if test="reviewer != null">reviewer = #{reviewer},</if>
            <if test="reviewComment != null">review_comment = #{reviewComment},</if>
            <if test="applicationNote != null">application_note = #{applicationNote},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where application_id = #{applicationId}
    </update>

    <update id="reviewApplication">
        update training_application 
        set application_status = #{status}, 
            review_time = now(), 
            reviewer = #{reviewer},
            review_comment = #{reviewComment},
            update_time = now()
        where application_id = #{applicationId}
    </update>

    <delete id="deleteTrainingApplicationByApplicationId" parameterType="Long">
        update training_application set del_flag = '2' where application_id = #{applicationId}
    </delete>

    <delete id="deleteTrainingApplicationByApplicationIds" parameterType="String">
        update training_application set del_flag = '2' where application_id in 
        <foreach item="applicationId" collection="array" open="(" separator="," close=")">
            #{applicationId}
        </foreach>
    </delete>
</mapper>
