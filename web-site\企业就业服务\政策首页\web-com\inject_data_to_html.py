import json
import re
import os

def load_mock_data():
    """加载模拟数据"""
    with open('api_data/mock_data.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def generate_region_options(regions_data):
    """生成地域选项HTML"""
    html = ""
    
    # 添加"全部"选项
    html += '<li class="chooseLi fl ml10 mt20 transi tc text-gray6" data-id="">全部</li>\n'
    
    # 添加中央
    place = regions_data['obj']['place']
    html += f'<li class="chooseLi fl ml10 mt20 transi tc text-gray6" data-id="{place["baseId"]}">{place["baseName"]}</li>\n'
    
    # 添加省
    province = regions_data['obj']['province']
    html += f'<li class="chooseLi fl ml10 mt20 transi tc text-gray6" data-id="{province["baseId"]}">{province["baseName"]}</li>\n'
    
    # 添加市
    city = regions_data['obj']['city']
    html += f'<li class="chooseLi fl ml10 mt20 transi tc text-gray6" data-id="{city["baseId"]}">{city["baseName"]}</li>\n'
    
    # 添加区县
    for district in regions_data['obj']['district']:
        html += f'<li class="chooseLi fl ml10 mt20 transi tc text-gray6" data-id="{district["baseId"]}">{district["baseName"]}</li>\n'
    
    return html

def generate_policy_type_options(policy_types_data):
    """生成政策类型选项HTML"""
    html = ""
    
    # 添加"全部"选项
    html += '<li class="chooseLi fl ml10 mt20 transi tc text-gray6" data-id="">全部</li>\n'
    
    for item in policy_types_data['obj']:
        html += f'<li class="chooseLi fl ml10 mt20 transi tc text-gray6" data-id="{item["baseId"]}">{item["baseName"]}</li>\n'
    
    return html

def generate_support_mode_options(support_modes_data):
    """生成政策支持选项HTML"""
    html = ""
    
    # 添加"全部"选项
    html += '<li class="chooseLi fl ml10 mt20 transi tc text-gray6" data-id="">全部</li>\n'
    
    for item in support_modes_data['obj']:
        html += f'<li class="chooseLi fl ml10 mt20 transi tc text-gray6" data-id="{item["baseId"]}">{item["baseName"]}</li>\n'
    
    return html

def generate_file_list_html(file_list_data):
    """生成政策文件列表HTML"""
    html = ""
    
    for item in file_list_data['obj']['content']:
        html += f'''
        <li class="fl pr transi pr">
            <a class="li pa transi block" target="_blank" href="javascript:;" title="{item['title']}">
                <p class="titleA textoverflow transi f18 block">{item['title']}</p>
                <div class="iconDiv clearfix width100">
                    <p class="icon2 fl f16 text-gray9 textoverflow">{item['policyTypeName']}</p>
                    <p class="icon2 fl f16 text-gray9 textoverflow">{item['supportmodeName']}</p>
                    <p class="icon3 fl f16 text-gray9">{item['publishTime']}</p>
                </div>
            </a>
        </li>
        '''
    
    return html

def generate_calendar_list_html(calendar_list_data):
    """生成政策申报列表HTML"""
    html = ""
    
    for item in calendar_list_data['obj']['content']:
        status_class = ""
        if item['statusText'] == '1':
            status_class = "icon1"
            status_text = "申请中"
        elif item['statusText'] == '5':
            status_class = "icon2"
            status_text = "待开始"
        else:
            status_class = "icon3"
            status_text = "已结束"
        
        reward_html = ""
        if 'rewardMoney' in item and item['rewardMoney']:
            reward_html = f'''
            <p class="tc width100 text-gray9"><span class="fb f24 text-ff8a00">{item['rewardMoney']}</span><span>万元</span></p>
            <p class="tc width100 text-gray9 mt5">最高扶持</p>
            '''
        elif 'methodName' in item and item['methodName']:
            reward_html = f'''
            <p class="tc width100 text-gray9"><span class="fb f24 text-ff8a00">{item['methodName']}</span></p>
            <p class="tc width100 text-gray9 mt5">支持方式</p>
            '''
        
        html += f'''
        <li class="fl transi">
            <a href="javascript:;" target="_blank" class="topBox width100 block">
                <div class="clearfix">
                    <span class="sqSpan {status_class} fl text-white tc">{status_text}</span>
                    <p class="linkA fl ml10 textoverflow transi f20" title="{item['sname']}">{item['sname']}</p>
                </div>
                <div class="clearfix">
                    <div class="lBox fl">
                        <p class="fwP width100 text-gray6 textoverflow">{item['competentDepartment']}</p>
                        <p class="timeP width100 text-gray6 textoverflow">{item['applytime']}</p>
                        <div class="spanBox clearfix mt15 width100 textoverflow">
                            <span class="fl textoverflow">{item.get('projectTypeName', '')}</span>
                            <span class="fl textoverflow">{item.get('areaName', '')}</span>
                        </div>
                    </div>
                    <div class="rBox fr tc">
                        {reward_html}
                    </div>
                    <div class="inlineblock detailStatusName">{item.get('detailStatusName', '已结束')}</div>
                </div>
            </a>
            <div class="bottomBox width100 tc">
                <a href="javascript:;" class="inlineblock f15 text-0050ff" onclick="showFinanceMeasures('{item['baseId']}')">奖励措施</a>
                <span class="inlineblock f15 text-grayB ml10 mr10">|</span>
                <a target="_blank" href="javascript:;" class="inlineblock f15 text-0050ff">查看申报通知</a>
            </div>
        </li>
        '''
    
    return html

def generate_read_list_html(read_list_data):
    """生成政策解读列表HTML"""
    html = ""
    
    for item in read_list_data['obj']['content']:
        html += f'''
        <a class="fl transi" href="javascript:;">
            <div class="imgSize width100 pr">
                <p class="pa paraoverflow3" title="{item['title']}">{item['title']}</p>
                <img class="transi pa" src="{item['picUrl']}">
            </div>
            <p class="liTitle mt15 f18 paraoverflow2 transi" title="{item['title']}">{item['title']}</p>
            <div class="clearfix mt15">
                <p class="linkP fl f15 text-gray9 textoverflow">{item['policy']['source']}</p>
                <p class="timeP fr f15 text-gray9">{item['publishTime']}</p>
            </div>
        </a>
        '''
    
    return html

def generate_video_list_html(video_list_data):
    """生成短视频列表HTML"""
    html = ""
    
    for item in video_list_data['obj']['content']:
        html += f'''
        <a class="fl transi" target="_blank" href="javascript:;">
            <div>
                <img class="pic" src="images/pic_noDetail.png">
            </div>
            <p class="title textoverflow" title="{item['videoName']}">{item['videoName']}</p>
        </a>
        '''
    
    return html

def main():
    # 加载模拟数据
    mock_data = load_mock_data()
    
    # 读取原始HTML文件
    with open('static_files/index.html', 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    print("正在注入数据到HTML文件...")
    
    # 生成各种数据的HTML
    region_options = generate_region_options(mock_data['regions'])
    policy_type_options = generate_policy_type_options(mock_data['policy_types'])
    support_mode_options = generate_support_mode_options(mock_data['support_modes'])
    file_list_html = generate_file_list_html(mock_data['file_list'])
    calendar_list_html = generate_calendar_list_html(mock_data['calendar_list'])
    read_list_html = generate_read_list_html(mock_data['read_list'])
    video_list_html = generate_video_list_html(mock_data['video_list'])
    
    print("数据注入完成，正在保存文件...")
    
    # 保存更新后的HTML文件
    output_file = 'static_files/index_with_data.html'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"已生成包含数据的HTML文件: {output_file}")
    print("现在需要手动替换HTML中的data-bind部分")

if __name__ == "__main__":
    main()
