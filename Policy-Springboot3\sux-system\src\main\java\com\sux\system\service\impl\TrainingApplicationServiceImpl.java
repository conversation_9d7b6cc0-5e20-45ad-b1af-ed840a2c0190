package com.sux.system.service.impl;

import com.sux.common.utils.DateUtils;
import com.sux.common.utils.SecurityUtils;
import com.sux.system.domain.TrainingApplication;
import com.sux.system.mapper.TrainingApplicationMapper;
import com.sux.system.service.ITrainingApplicationService;
import com.sux.system.service.ITrainingOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 培训报名Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
public class TrainingApplicationServiceImpl implements ITrainingApplicationService 
{
    @Autowired
    private TrainingApplicationMapper trainingApplicationMapper;

    @Autowired
    private ITrainingOrderService trainingOrderService;

    /**
     * 查询培训报名
     * 
     * @param applicationId 培训报名主键
     * @return 培训报名
     */
    @Override
    public TrainingApplication selectTrainingApplicationByApplicationId(Long applicationId)
    {
        return trainingApplicationMapper.selectTrainingApplicationByApplicationId(applicationId);
    }

    /**
     * 查询培训报名列表
     * 
     * @param trainingApplication 培训报名
     * @return 培训报名
     */
    @Override
    public List<TrainingApplication> selectTrainingApplicationList(TrainingApplication trainingApplication)
    {
        return trainingApplicationMapper.selectTrainingApplicationList(trainingApplication);
    }

    /**
     * 新增培训报名
     * 
     * @param trainingApplication 培训报名
     * @return 结果
     */
    @Override
    public int insertTrainingApplication(TrainingApplication trainingApplication)
    {
        trainingApplication.setCreateTime(DateUtils.getNowDate());
        return trainingApplicationMapper.insertTrainingApplication(trainingApplication);
    }

    /**
     * 修改培训报名
     * 
     * @param trainingApplication 培训报名
     * @return 结果
     */
    @Override
    public int updateTrainingApplication(TrainingApplication trainingApplication)
    {
        trainingApplication.setUpdateTime(DateUtils.getNowDate());
        return trainingApplicationMapper.updateTrainingApplication(trainingApplication);
    }

    /**
     * 批量删除培训报名
     * 
     * @param applicationIds 需要删除的培训报名主键
     * @return 结果
     */
    @Override
    public int deleteTrainingApplicationByApplicationIds(Long[] applicationIds)
    {
        return trainingApplicationMapper.deleteTrainingApplicationByApplicationIds(applicationIds);
    }

    /**
     * 删除培训报名信息
     * 
     * @param applicationId 培训报名主键
     * @return 结果
     */
    @Override
    public int deleteTrainingApplicationByApplicationId(Long applicationId)
    {
        return trainingApplicationMapper.deleteTrainingApplicationByApplicationId(applicationId);
    }

    /**
     * 检查用户是否已报名某个培训订单
     * 
     * @param orderId 培训订单ID
     * @param userId 用户ID
     * @return 报名记录
     */
    @Override
    public TrainingApplication checkUserApplication(Long orderId, Long userId)
    {
        return trainingApplicationMapper.selectApplicationByOrderIdAndUserId(orderId, userId);
    }

    /**
     * 检查手机号是否已报名某个培训订单
     * 
     * @param orderId 培训订单ID
     * @param phone 手机号
     * @return 报名记录
     */
    @Override
    public TrainingApplication checkPhoneApplication(Long orderId, String phone)
    {
        return trainingApplicationMapper.selectApplicationByOrderIdAndPhone(orderId, phone);
    }

    /**
     * 统计某个培训订单的报名人数
     * 
     * @param orderId 培训订单ID
     * @return 报名人数
     */
    @Override
    public int countApplicationsByOrderId(Long orderId)
    {
        return trainingApplicationMapper.countApplicationsByOrderId(orderId);
    }

    /**
     * 获取某个培训订单的报名列表
     * 
     * @param orderId 培训订单ID
     * @return 报名列表
     */
    @Override
    public List<TrainingApplication> getApplicationsByOrderId(Long orderId)
    {
        return trainingApplicationMapper.selectApplicationsByOrderId(orderId);
    }

    /**
     * 审核报名
     * 
     * @param applicationId 报名ID
     * @param status 审核状态
     * @param reviewer 审核人
     * @param reviewComment 审核意见
     * @return 结果
     */
    @Override
    @Transactional
    public int reviewApplication(Long applicationId, String status, String reviewer, String reviewComment)
    {
        int result = trainingApplicationMapper.reviewApplication(applicationId, status, reviewer, reviewComment);
        
        // 如果审核通过，更新培训订单的当前报名人数
        if ("1".equals(status) && result > 0) {
            TrainingApplication application = trainingApplicationMapper.selectTrainingApplicationByApplicationId(applicationId);
            if (application != null) {
                trainingOrderService.updateCurrentParticipants(application.getOrderId());
            }
        }
        
        return result;
    }

    /**
     * 提交报名申请
     * 
     * @param trainingApplication 报名信息
     * @return 结果
     */
    @Override
    @Transactional
    public int submitApplication(TrainingApplication trainingApplication)
    {
        // 设置报名时间和状态
        trainingApplication.setApplicationTime(new Date());
        trainingApplication.setApplicationStatus("0"); // 待审核
        trainingApplication.setCreateTime(DateUtils.getNowDate());
        
        // 如果有登录用户，设置用户ID
        try {
            Long userId = SecurityUtils.getUserId();
            trainingApplication.setUserId(userId);
            trainingApplication.setCreateId(SecurityUtils.getUserId());
        } catch (Exception e) {
            // 未登录用户，不设置用户ID
        }
        
        return trainingApplicationMapper.insertTrainingApplication(trainingApplication);
    }

    /**
     * 取消报名
     * 
     * @param applicationId 报名ID
     * @return 结果
     */
    @Override
    @Transactional
    public int cancelApplication(Long applicationId)
    {
        TrainingApplication application = new TrainingApplication();
        application.setApplicationId(applicationId);
        application.setApplicationStatus("3"); // 已取消
        application.setUpdateTime(DateUtils.getNowDate());
        
        int result = trainingApplicationMapper.updateTrainingApplication(application);
        
        // 更新培训订单的当前报名人数
        if (result > 0) {
            TrainingApplication originalApplication = trainingApplicationMapper.selectTrainingApplicationByApplicationId(applicationId);
            if (originalApplication != null) {
                trainingOrderService.updateCurrentParticipants(originalApplication.getOrderId());
            }
        }
        
        return result;
    }

    /**
     * 检查报名唯一性
     * 
     * @param trainingApplication 报名信息
     * @return 结果
     */
    @Override
    public boolean checkApplicationUnique(TrainingApplication trainingApplication)
    {
        Long orderId = trainingApplication.getOrderId();
        String phone = trainingApplication.getApplicantPhone();
        Long userId = trainingApplication.getUserId();
        
        // 检查手机号是否已报名
        TrainingApplication phoneApplication = trainingApplicationMapper.selectApplicationByOrderIdAndPhone(orderId, phone);
        if (phoneApplication != null && !phoneApplication.getApplicationId().equals(trainingApplication.getApplicationId())) {
            return false;
        }
        
        // 检查用户是否已报名（如果有用户ID）
        if (userId != null) {
            TrainingApplication userApplication = trainingApplicationMapper.selectApplicationByOrderIdAndUserId(orderId, userId);
            if (userApplication != null && !userApplication.getApplicationId().equals(trainingApplication.getApplicationId())) {
                return false;
            }
        }
        
        return true;
    }
}
