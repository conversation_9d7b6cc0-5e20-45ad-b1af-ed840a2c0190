package com.sux.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sux.system.domain.JobPosting;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 招聘信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
public interface JobPostingMapper extends BaseMapper<JobPosting>
{
    /**
     * 查询招聘信息列表
     * 
     * @param jobPosting 招聘信息
     * @return 招聘信息集合
     */
    public List<JobPosting> selectJobPostingList(JobPosting jobPosting);

    /**
     * 查询招聘信息
     * 
     * @param jobId 招聘信息主键
     * @return 招聘信息
     */
    public JobPosting selectJobPostingByJobId(Long jobId);

    /**
     * 新增招聘信息
     * 
     * @param jobPosting 招聘信息
     * @return 结果
     */
    public int insertJobPosting(JobPosting jobPosting);

    /**
     * 修改招聘信息
     * 
     * @param jobPosting 招聘信息
     * @return 结果
     */
    public int updateJobPosting(JobPosting jobPosting);

    /**
     * 删除招聘信息
     * 
     * @param jobId 招聘信息主键
     * @return 结果
     */
    public int deleteJobPostingByJobId(Long jobId);

    /**
     * 批量删除招聘信息
     * 
     * @param jobIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteJobPostingByJobIds(Long[] jobIds);

    /**
     * 查询我发布的招聘信息列表
     * 
     * @param jobPosting 招聘信息
     * @param publisherUserId 发布者用户ID
     * @return 招聘信息集合
     */
    public List<JobPosting> selectMyJobPostingList(@Param("jobPosting") JobPosting jobPosting, @Param("publisherUserId") Long publisherUserId);

    /**
     * 查询已发布的招聘信息列表（公开接口）
     * 
     * @param jobPosting 招聘信息
     * @return 招聘信息集合
     */
    public List<JobPosting> selectPublishedJobPostingList(JobPosting jobPosting);

    /**
     * 根据多个条件匹配招聘信息
     * 
     * @param matchParams 匹配参数
     * @return 招聘信息集合
     */
    public List<JobPosting> selectJobPostingByMatchParams(Map<String, Object> matchParams);

    /**
     * 更新招聘信息浏览次数
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    public int updateJobPostingViewCount(Long jobId);

    /**
     * 更新招聘信息申请次数
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    public int updateJobPostingApplicationCount(Long jobId);

    /**
     * 更新招聘信息已招聘人数
     * 
     * @param jobId 招聘信息ID
     * @param positionsFilled 已招聘人数
     * @return 结果
     */
    public int updateJobPostingPositionsFilled(@Param("jobId") Long jobId, @Param("positionsFilled") Integer positionsFilled);

    /**
     * 查询热门招聘信息（按浏览次数排序）
     * 
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    public List<JobPosting> selectHotJobPostingList(@Param("limit") Integer limit);

    /**
     * 查询推荐招聘信息
     * 
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    public List<JobPosting> selectFeaturedJobPostingList(@Param("limit") Integer limit);

    /**
     * 查询紧急招聘信息
     * 
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    public List<JobPosting> selectUrgentJobPostingList(@Param("limit") Integer limit);

    /**
     * 根据工作类别统计招聘信息数量
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> selectJobPostingCountByCategory();

    /**
     * 根据工作地点统计招聘信息数量
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> selectJobPostingCountByLocation();

    /**
     * 根据薪资范围统计招聘信息数量
     * 
     * @return 统计结果
     */
    public List<Map<String, Object>> selectJobPostingCountBySalaryRange();

    /**
     * 查询即将截止的招聘信息
     * 
     * @param days 天数
     * @return 招聘信息集合
     */
    public List<JobPosting> selectJobPostingExpiringSoon(@Param("days") Integer days);

    /**
     * 查询招聘信息详情（包含发布者信息）
     * 
     * @param jobId 招聘信息ID
     * @return 招聘信息详情
     */
    public JobPosting selectJobPostingDetailByJobId(Long jobId);

    /**
     * 根据关键词搜索招聘信息
     * 
     * @param keyword 关键词
     * @return 招聘信息集合
     */
    public List<JobPosting> selectJobPostingByKeyword(@Param("keyword") String keyword);

    /**
     * 查询相似的招聘信息
     * 
     * @param jobPosting 招聘信息
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    public List<JobPosting> selectSimilarJobPostingList(@Param("jobPosting") JobPosting jobPosting, @Param("limit") Integer limit);

    /**
     * 批量更新招聘信息状态
     * 
     * @param jobIds 招聘信息ID数组
     * @param status 状态
     * @return 结果
     */
    public int batchUpdateJobPostingStatus(@Param("jobIds") Long[] jobIds, @Param("status") String status);

    /**
     * 查询招聘信息统计数据
     * 
     * @param publisherUserId 发布者用户ID（可选）
     * @return 统计数据
     */
    public Map<String, Object> selectJobPostingStatistics(@Param("publisherUserId") Long publisherUserId);
}
