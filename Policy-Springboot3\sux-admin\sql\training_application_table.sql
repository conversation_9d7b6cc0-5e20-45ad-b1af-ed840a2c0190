-- 培训报名表
DROP TABLE IF EXISTS `training_application`;
CREATE TABLE `training_application` (
  `application_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '报名ID',
  `order_id` bigint(20) NOT NULL COMMENT '培训订单ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `applicant_name` varchar(50) NOT NULL COMMENT '报名人姓名',
  `applicant_phone` varchar(20) NOT NULL COMMENT '报名人手机号',
  `applicant_email` varchar(100) DEFAULT NULL COMMENT '报名人邮箱',
  `applicant_id_card` varchar(18) DEFAULT NULL COMMENT '报名人身份证号',
  `applicant_gender` varchar(10) DEFAULT NULL COMMENT '报名人性别',
  `applicant_age` int(3) DEFAULT NULL COMMENT '报名人年龄',
  `applicant_education` varchar(20) DEFAULT NULL COMMENT '报名人学历',
  `applicant_experience` text COMMENT '报名人工作经验',
  `applicant_address` varchar(200) DEFAULT NULL COMMENT '报名人地址',
  `application_status` char(1) DEFAULT '0' COMMENT '报名状态（0待审核 1已通过 2已拒绝 3已取消）',
  `application_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '报名时间',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `reviewer` varchar(50) DEFAULT NULL COMMENT '审核人',
  `review_comment` varchar(500) DEFAULT NULL COMMENT '审核意见',
  `application_note` varchar(500) DEFAULT NULL COMMENT '报名备注',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`application_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_applicant_phone` (`applicant_phone`),
  KEY `idx_application_status` (`application_status`),
  KEY `idx_application_time` (`application_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='培训报名表';

-- 插入测试数据
INSERT INTO `training_application` VALUES 
(1, 1, 1, '张三', '13800138001', '<EMAIL>', '110101199001011234', '男', 25, '本科', '有3年Java开发经验，熟悉Spring框架', '北京市朝阳区', '1', '2025-07-20 10:00:00', '2025-07-20 14:00:00', '李老师', '符合报名条件，通过审核', '希望能参加此次培训', 'admin', '2025-07-20 10:00:00', 'admin', '2025-07-20 14:00:00', NULL, '0'),
(2, 1, 2, '李四', '13800138002', '<EMAIL>', '110101199002021234', '女', 28, '硕士', '有5年软件开发经验，精通多种编程语言', '北京市海淀区', '0', '2025-07-21 09:30:00', NULL, NULL, NULL, '对Java高级开发很感兴趣', 'admin', '2025-07-21 09:30:00', 'admin', '2025-07-21 09:30:00', NULL, '0'),
(3, 2, 3, '王五', '13800138003', '<EMAIL>', '110101199003031234', '男', 30, '大专', '有2年数据分析经验，熟悉Excel和SQL', '北京市西城区', '1', '2025-07-21 11:00:00', '2025-07-21 15:30:00', '张老师', '基础扎实，适合参加培训', '想转行做数据分析师', 'admin', '2025-07-21 11:00:00', 'admin', '2025-07-21 15:30:00', NULL, '0');
