package com.sux.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.util.Date;

/**
 * 政策申请对象 policy_application
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@TableName("policy_application")
public class PolicyApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 申请ID */
    @TableId(type = IdType.AUTO)
    private Long applicationId;

    /** 政策ID */
    @Excel(name = "政策ID")
    @NotNull(message = "政策ID不能为空")
    private Long policyId;

    /** 申请人用户ID */
    @Excel(name = "申请人用户ID")
    private Long applicantUserId;

    /** 申请人姓名 */
    @Excel(name = "申请人姓名")
    private String applicantName;

    /** 申请人手机号 */
    @Excel(name = "申请人手机号")
    private String applicantPhone;

    /** 申请状态（0待初审 1初审通过 2初审拒绝 3待终审 4终审通过 5终审拒绝 6已完成） */
    @Excel(name = "申请状态", readConverterExp = "0=待初审,1=初审通过,2=初审拒绝,3=待终审,4=终审通过,5=终审拒绝,6=已完成")
    private String applicationStatus;

    /** 所需材料JSON数据 */
    @Excel(name = "所需材料")
    private String requiredMaterials;

    /** 提交时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;

    /** 完成时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

    /** 删除标志（0代表存在 2代表删除） */
    @TableField("del_flag")
    private String delFlag;

    // 关联查询字段
    /** 政策名称 */
    @TableField(exist = false)
    private String policyName;

    /** 政策类型 */
    @TableField(exist = false)
    private String policyType;

    /** 申请人用户名 */
    @TableField(exist = false)
    private String applicantUserName;

    /** 申请人昵称 */
    @TableField(exist = false)
    private String applicantNickName;

    public void setApplicationId(Long applicationId) 
    {
        this.applicationId = applicationId;
    }

    public Long getApplicationId() 
    {
        return applicationId;
    }

    public void setPolicyId(Long policyId) 
    {
        this.policyId = policyId;
    }

    public Long getPolicyId() 
    {
        return policyId;
    }

    public void setApplicantUserId(Long applicantUserId) 
    {
        this.applicantUserId = applicantUserId;
    }

    public Long getApplicantUserId() 
    {
        return applicantUserId;
    }

    public void setApplicationStatus(String applicationStatus) 
    {
        this.applicationStatus = applicationStatus;
    }

    public String getApplicationStatus() 
    {
        return applicationStatus;
    }

    public void setRequiredMaterials(String requiredMaterials) 
    {
        this.requiredMaterials = requiredMaterials;
    }

    public String getRequiredMaterials() 
    {
        return requiredMaterials;
    }

    public void setSubmitTime(Date submitTime) 
    {
        this.submitTime = submitTime;
    }

    public Date getSubmitTime() 
    {
        return submitTime;
    }

    public void setCompleteTime(Date completeTime) 
    {
        this.completeTime = completeTime;
    }

    public Date getCompleteTime() 
    {
        return completeTime;
    }

    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public String getPolicyName() {
        return policyName;
    }

    public void setPolicyName(String policyName) {
        this.policyName = policyName;
    }

    public String getPolicyType() {
        return policyType;
    }

    public void setPolicyType(String policyType) {
        this.policyType = policyType;
    }

    public String getApplicantUserName() {
        return applicantUserName;
    }

    public void setApplicantUserName(String applicantUserName) {
        this.applicantUserName = applicantUserName;
    }

    public String getApplicantNickName() {
        return applicantNickName;
    }

    public void setApplicantNickName(String applicantNickName) {
        this.applicantNickName = applicantNickName;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public String getApplicantPhone() {
        return applicantPhone;
    }

    public void setApplicantPhone(String applicantPhone) {
        this.applicantPhone = applicantPhone;
    }

    @Override
    public String toString() {
        return "PolicyApplication{" +
                "applicationId=" + applicationId +
                ", policyId=" + policyId +
                ", applicantUserId=" + applicantUserId +
                ", applicantName='" + applicantName + '\'' +
                ", applicantPhone='" + applicantPhone + '\'' +
                ", applicationStatus='" + applicationStatus + '\'' +
                ", requiredMaterials='" + requiredMaterials + '\'' +
                ", submitTime=" + submitTime +
                ", completeTime=" + completeTime +
                ", delFlag='" + delFlag + '\'' +
                ", policyName='" + policyName + '\'' +
                ", policyType='" + policyType + '\'' +
                ", applicantUserName='" + applicantUserName + '\'' +
                ", applicantNickName='" + applicantNickName + '\'' +
                '}';
    }
}
