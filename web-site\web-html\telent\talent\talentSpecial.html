<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 青岛市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>找人才-青创通 · 青岛市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!--分页 css-->
    <link rel="stylesheet" type="text/css" href="../public/plugins/pagination/pagination.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <link rel="stylesheet" type="text/css" href="../public/css/commonNew.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/talentSpecial.css?v=202505221517" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />
</head>

<body id="viewModelBox">
    <div id="headerBar"></div>
    <!-- banner start -->
    <div class="bannerBox pr">
        <div class="bannerSlide" >
            <img src="./images/ts_bannerBg.jpg" >
        </div>
    </div>
    <!-- banner end -->
    <!-- box1 start -->
    <div class="box1 width100">
        <div class="conAuto1400 clearfix">
            <a  href="./calendar.html" class="numBox numBox1 fl transi clearfix upward shadowHover">
                <div class="numRight fr">
                    <p class="f24 fb">招聘信息</p>
                            <img src="images/talentSpecial_arrowIcon.png" >
                </div>
            </a>
            <a  href="./enterpriseRecruitment.html" class="numBox numBox2 fl transi clearfix upward shadowHover">
                <div class="numRight fr">
                    <p class="f24 fb">岗位超市</p>
                            <img src="images/talentSpecial_arrowIcon.png" >
                </div>
            </a>
            <!-- <a target="_blank" href="https://fw.rc.qingdao.gov.cn/qdzhrcww/work/f60050102/showGw.action" class="numBox numBox2 fl transi clearfix upward shadowHover">
                <div class="numRight fr">
                    <p class="f24 fb">岗位超市</p>
                            <img src="images/talentSpecial_arrowIcon.png" >
                </div>
            </a> -->
            <a  href="./answersList.html?jobfairType=1" class="numBox numBox4 fl transi clearfix upward shadowHover">
                <div class="numRight fr">
                    <p class="f24 fb">线上招聘会</p>
                            <img src="images/talentSpecial_arrowIcon.png" >
                </div>
            </a>
            <a  href="./answersList.html?jobfairType=2" class="numBox numBox3 fl transi clearfix upward shadowHover">
                <div class="numRight fr">
                    <p class="f24 fb">现场招聘会</p>
                            <img src="images/talentSpecial_arrowIcon.png" >
                </div>
            </a>
        </div>
    </div>
    <!-- box1 end -->
    <!-- box3 start -->
    <div class="box3 conAuto1400">
        <div class="clearfix">
            <p class="titleP fl fb">人才<em>超市</em></p>
            <a  href="./calendar.html" class="moreBtn fr transi">
                <span class="inlineblock text-white">更多</span>
            </a>
        </div>
        <ul class="clearfix rccsList" data-bind="foreach:calendarList">
            <li class="fl transi" data-bind="click:$root.goTorccs">
                <p class="name" data-bind="text:name"></p>
                <p class="infop textoverflow"><span data-bind="text:sex+'/'+education+'/'+matrimony+'/'+expectProfession"></p>
            </li>
        </ul>
        <!-- <iframe style="width: 1400px; height: 180px;" src="https://fw.rc.qingdao.gov.cn/qdzhrcww/work/f60050114/queryPersonIndex.action" frameborder="0">
           
        </iframe> -->
    </div>
    <!-- box3 end -->
    <div class="enterOut clearfix conAuto1400">
        <p class="fl enter1"><a target="_blank" href="https://fw.rc.qingdao.gov.cn/jeecms/htmlrc/zq/zcyzmxx/index.html">立即查看</a></p>
        <p class="fr enter2"><a target="_blank" href="https://fw.rc.qingdao.gov.cn/qdzhrcww/pages/f10/f6005/f600501/f60050107/bsx.jsp">立即查看</a></p>
    </div>
    <!-- box5 start -->
    <div class="box5 width100">
        <div class="conAuto1400">
            <div class="clearfix">
                <p class="titleP fl fb">招聘会<em>专场</em></p>
                <ul class="sxUl clearfix fl ml40" data-bind="foreach:typeVo()">
                    <li class="fl transi f16 text-gray9 mr20"
                        data-bind="text:baseName,css:{on:baseId==$root.jobfairType()},event: { mouseover: $root.linkFun}">
                    </li>
                </ul>
                <a  href="javascript:;" class="moreBtn fr transi"
                    data-bind="attr:{href:'./answersList.html?jobfairType='+jobfairType()}">
                    <span class="inlineblock text-white">更多</span>
                </a>
            </div>
            <!-- 现场招聘会 -->
            <div class="xcZph">
                <!-- ko if:answersList().length > 0 -->
                <ul class="zphList clearfix" data-bind="foreach:answersList">
                    <li class="fl pr" data-bind="click:$root.goAnswersDetail">
                        <div class="li pa transi">
                            <p class="jobfairName textoverflow" data-bind="text:jobfairName,attr:{href:'./answersDetail.html?id='+baseId + '&type='+jobfairType}"></p>
                            <p class="time textoverflow"><span data-bind="text:holdingBeginTime"></span>至<span data-bind="text:holdingEndTime"></span></p>
                            <p class="add textoverflow" data-bind="text:remark?remark:'--'"></p>
                            <p class="btns transi">立即查看</p>
                        </div>
                        
                    </li>
                </ul>
                <!-- /ko -->
                <!-- ko if:answersList().length == 0 -->
                <div class="nodataPic" style="margin: 0 auto;"></div>
                <!-- /ko -->
            </div>
            <!-- <iframe style="width: 1400px; height: 210px;" class="xcZph" src="https://fw.rc.qingdao.gov.cn/qdzhrcww/work/f60050114/queryHyzpIndex.action" frameborder="0"></iframe> -->
            <!-- 现场招聘会 -->
            <!-- 线上招聘会 -->
            <div class="xsZph none">
                <!-- ko if:answersList().length > 0 -->
                <ul class="zphList clearfix" data-bind="foreach:answersList">
                    <li class="fl pr" data-bind="click:$root.goAnswersDetail">
                        <div class="li pa transi">
                            <p class="jobfairName textoverflow" data-bind="text:jobfairName,attr:{href:'./answersDetail.html?id='+baseId + '&type='+jobfairType}"></p>
                            <p class="time textoverflow"><span data-bind="text:holdingBeginTime"></span>至<span data-bind="text:holdingEndTime"></span></p>
                            <p class="add textoverflow" data-bind="text:remark?remark:'--'"></p>
                            <p class="btns transi">立即查看</p>
                        </div>
                        
                    </li>
                </ul>
                <!-- /ko -->
                <!-- ko if:answersList().length == 0 -->
                <div class="nodataPic" style="margin: 0 auto;"></div>
                <!-- /ko -->
            </div>
            <!-- <iframe style="width: 1400px; height: 210px;" class="xsZph none" src="https://fw.rc.qingdao.gov.cn/qdzhrcww/work/f60050114/queryZphIndex.action" frameborder="0"></iframe> -->
            <!-- 线上招聘会 -->
            
            

           
        </div>
    </div>
    <!-- box5 end -->
      <!-- box6-岗位超市 start -->
      
    <div class="box6 conAuto1400">
        <div class="clearfix">
            <p class="titleP fl fb">岗位<em>超市</em></p>
            <a href="enterpriseRecruitment.html"  class="moreBtn fr transi">
                <span class="inlineblock text-white">更多</span>
            </a>
        </div>
        <!-- ko if:dataList().length>0  -->
        <ul class="zpgwList clearfix" data-bind="foreach:dataList">
            <li class="transi">
                <a href="javascript:;" class="intDetBox" target="_blank"  data-bind="attr:{href:'./enterpriseRecruitmentDetail.html?id='+baseId}">
                    <div class="topBox width100">
                        <p class="linkA clearfix f20 transi block width100 textoverflow" data-bind="text:jobName"></p>
                        <div class="clearfix mt15">
                            <p class="iconP1 textoverflow fl text-gray6" data-bind="text:workExperienceName+' | '+educationName,attr:{title:workExperienceName+' | '+educationName}"></p>
                            <p class="moneyP fr textoverflow  f24 text-ff6000 " data-bind="text:payMinMoney+'~'+payMaxMoney,attr:{title:payMinMoney+'~'+payMaxMoney}"></p>
                        </div>
                    </div>
                    <div class="bottomBox width100 clearfix">
                        <p class="fl text-0052d9"><em data-bind="text:unitName"></em></p>
                        <p class="timeP fr text-gray6" data-bind="text:baseCreateTime"></p>
                    </div>
                </a>
                
            </li>
        </ul>
         <!-- /ko  -->
         <!-- ko if:dataList().length==0  -->
          <div class="nodataPic"></div>
         <!-- /ko  -->
    </div>
    
    <!-- box6 end -->
    <div id="footerBar"></div>
    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" type="text/javascript"
        charset="utf-8"></script>
    <script src="../public/js/words.js" type="text/javascript" charset="utf-8"></script>
    <!--common js-->
    <!-- <script src="../public/js/api.js"></script> -->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/mapping.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="../public/plugins/jbox/jquery.jBox.js"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>
    <!--this page js  -->
    <script src="js/talentSpecial.js?v=202505271645" type="text/javascript" charset="utf-8"></script>
</body>

</html>