<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 青岛市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>找人才-青创通 · 青岛市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!--分页 css-->
    <link rel="stylesheet" type="text/css" href="../public/plugins/pagination/pagination.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <link rel="stylesheet" type="text/css" href="../public/css/commonNew.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/talentSpecial.css?v=202505221517" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />
</head>

<body id="viewModelBox">
    <div id="headerBar"></div>
    <!-- banner start -->
    <div class="bannerBox pr">
        <div class="bannerSlide" >
            <img src="./images/ts_bannerBg.jpg" >
        </div>
    </div>
    <!-- banner end -->
    <!-- box1 start -->
    <div class="box1 width100">
        <div class="conAuto1400 clearfix">
            <a  href="./calendar.html" class="numBox numBox1 fl transi clearfix upward shadowHover">
                <div class="numRight fr">
                    <p class="f24 fb">招聘信息</p>
                            <img src="images/talentSpecial_arrowIcon.png" >
                </div>
            </a>
        </div>
    </div>
    <!-- box1 end -->
    <!-- box5 start -->
    <div class="box5 width100">
        <div class="conAuto1400">
            <div class="clearfix">
                <p class="titleP fl fb">招聘会<em>专场</em></p>
                <ul class="sxUl clearfix fl ml40" data-bind="foreach:typeVo()">
                    <li class="fl transi f16 text-gray9 mr20"
                        data-bind="text:baseName,css:{on:baseId==$root.jobfairType()},event: { mouseover: $root.linkFun}">
                    </li>
                </ul>
                <a  href="javascript:;" class="moreBtn fr transi"
                    data-bind="attr:{href:'./answersList.html?jobfairType='+jobfairType()}">
                    <span class="inlineblock text-white">更多</span>
                </a>
            </div>
            <!-- 现场招聘会 -->
            <div class="xcZph">
                <!-- ko if:answersList().length > 0 -->
                <ul class="zphList clearfix" data-bind="foreach:answersList">
                    <li class="fl pr" data-bind="click:$root.goAnswersDetail">
                        <div class="li pa transi">
                            <p class="jobfairName textoverflow" data-bind="text:jobfairName,attr:{href:'./answersDetail.html?id='+baseId + '&type='+jobfairType}"></p>
                            <p class="time textoverflow"><span data-bind="text:holdingBeginTime"></span>至<span data-bind="text:holdingEndTime"></span></p>
                            <p class="add textoverflow" data-bind="text:remark?remark:'--'"></p>
                            <p class="btns transi">立即查看</p>
                        </div>
                        
                    </li>
                </ul>
                <!-- /ko -->
                <!-- ko if:answersList().length == 0 -->
                <div class="nodataPic" style="margin: 0 auto;"></div>
                <!-- /ko -->
            </div>
            <!-- <iframe style="width: 1400px; height: 210px;" class="xcZph" src="https://fw.rc.qingdao.gov.cn/qdzhrcww/work/f60050114/queryHyzpIndex.action" frameborder="0"></iframe> -->
            <!-- 现场招聘会 -->
            <!-- 线上招聘会 -->
            <div class="xsZph none">
                <!-- ko if:answersList().length > 0 -->
                <ul class="zphList clearfix" data-bind="foreach:answersList">
                    <li class="fl pr" data-bind="click:$root.goAnswersDetail">
                        <div class="li pa transi">
                            <p class="jobfairName textoverflow" data-bind="text:jobfairName,attr:{href:'./answersDetail.html?id='+baseId + '&type='+jobfairType}"></p>
                            <p class="time textoverflow"><span data-bind="text:holdingBeginTime"></span>至<span data-bind="text:holdingEndTime"></span></p>
                            <p class="add textoverflow" data-bind="text:remark?remark:'--'"></p>
                            <p class="btns transi">立即查看</p>
                        </div>
                        
                    </li>
                </ul>
                <!-- /ko -->
                <!-- ko if:answersList().length == 0 -->
                <div class="nodataPic" style="margin: 0 auto;"></div>
                <!-- /ko -->
            </div>
            <!-- <iframe style="width: 1400px; height: 210px;" class="xsZph none" src="https://fw.rc.qingdao.gov.cn/qdzhrcww/work/f60050114/queryZphIndex.action" frameborder="0"></iframe> -->
            <!-- 线上招聘会 -->

            <!-- 招聘信息列表 -->
            <div class="jobListSection">
                <div class="clearfix mb20">
                    <p class="titleP fl fb">招聘信息<em>列表</em></p>
                    <div class="searchBox fr">
                        <input type="text" placeholder="搜索招聘信息..." id="jobSearchInput" class="searchInput">
                        <button class="searchBtn" onclick="searchJobs()">搜索</button>
                    </div>
                </div>

                <!-- 筛选条件 -->
                <div class="filterBox mb20">
                    <div class="filterItem">
                        <label>工作类型：</label>
                        <select id="jobTypeFilter" onchange="filterJobs()">
                            <option value="">全部</option>
                            <option value="全职">全职</option>
                            <option value="兼职">兼职</option>
                            <option value="临时工">临时工</option>
                            <option value="小时工">小时工</option>
                        </select>
                    </div>
                    <div class="filterItem">
                        <label>工作地点：</label>
                        <select id="locationFilter" onchange="filterJobs()">
                            <option value="">全部</option>
                            <option value="市南区">市南区</option>
                            <option value="市北区">市北区</option>
                            <option value="李沧区">李沧区</option>
                            <option value="崂山区">崂山区</option>
                            <option value="城阳区">城阳区</option>
                            <option value="黄岛区">黄岛区</option>
                        </select>
                    </div>
                    <div class="filterItem">
                        <label>薪资范围：</label>
                        <select id="salaryFilter" onchange="filterJobs()">
                            <option value="">全部</option>
                            <option value="0-3000">3000以下</option>
                            <option value="3000-5000">3000-5000</option>
                            <option value="5000-8000">5000-8000</option>
                            <option value="8000-12000">8000-12000</option>
                            <option value="12000-99999">12000以上</option>
                        </select>
                    </div>
                </div>

                <!-- 招聘信息列表 -->
                <div class="jobPostingsList" id="jobPostingsList">
                    <!-- 调试信息 -->
                    <div class="debug-info" style="background: #f0f0f0; padding: 10px; margin-bottom: 10px; font-size: 12px;">
                        <p>调试信息 - 招聘信息数量: <span data-bind="text: jobPostings().length"></span></p>
                        <p>页面加载时间: <span id="loadTime"></span></p>
                        <p>数据加载状态: <span id="loadStatus">等待加载...</span></p>
                    </div>

                    <!-- ko if:jobPostings().length > 0 -->
                    <div class="jobList" data-bind="foreach:jobPostings">
                        <div class="jobCard" data-bind="click:$root.viewJobDetail">
                            <div class="jobHeader">
                                <h3 class="jobTitle" data-bind="text:jobTitle"></h3>
                                <div class="jobSalary" data-bind="text:salaryRange"></div>
                            </div>
                            <div class="jobInfo">
                                <span class="jobType" data-bind="text:jobType"></span>
                                <span class="jobLocation" data-bind="text:workLocation"></span>
                                <span class="jobCategory" data-bind="text:jobCategory"></span>
                            </div>
                            <div class="jobMeta">
                                <span class="publishTime" data-bind="text:publishTimeText"></span>
                                <span class="viewCount" data-bind="text:'浏览 ' + (viewCount || 0) + ' 次'"></span>
                                <button class="matchBtn" data-bind="click:$root.matchWorkers">匹配零工</button>
                            </div>
                        </div>
                    </div>
                    <!-- /ko -->
                    <!-- ko if:jobPostings().length == 0 -->
                    <div class="nodataPic" style="margin: 0 auto;">
                        <p>暂无招聘信息</p>
                        <p style="font-size: 12px; color: #666;">如果您看到这条消息，说明数据加载可能有问题。请检查浏览器控制台的错误信息。</p>
                    </div>
                    <!-- /ko -->
                </div>

                <!-- 分页 -->
                <div class="pagination-container" id="jobPagination"></div>
            </div>
            <!-- 招聘信息列表 end -->


        </div>
    </div>
    <!-- box6 end -->
    <div id="footerBar"></div>
    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" type="text/javascript"
        charset="utf-8"></script>
    <script src="../public/js/words.js" type="text/javascript" charset="utf-8"></script>
    <!--common js-->
    <!-- <script src="../public/js/api.js"></script> -->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/mapping.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="../public/plugins/jbox/jquery.jBox.js"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>
    <!--this page js  -->
    <script src="js/talentSpecial.js?v=202507231930" type="text/javascript" charset="utf-8"></script>

    <script>
        // 全局变量
        var jobPostingList = [];
        var searchParams = {
            keyword: '',
            jobType: '',
            location: '',
            salaryRange: ''
        };
        var isPageReady = false;

        // 原生Ajax请求函数
        function jobPostingAjaxRequest(url, params, callback) {
            var baseUrl = 'http://localhost:80/sux-admin/';

            // 构建查询参数
            var queryString = '';
            if (params && typeof params === 'object') {
                var paramArray = [];
                for (var key in params) {
                    if (params.hasOwnProperty(key) && params[key] !== null && params[key] !== undefined && params[key] !== '') {
                        paramArray.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
                    }
                }
                queryString = paramArray.length > 0 ? '?' + paramArray.join('&') : '';
            }

            var xhr = new XMLHttpRequest();
            xhr.open('GET', baseUrl + url + queryString, true);
            xhr.timeout = 30000;
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (callback && typeof callback === 'function') {
                                callback(response);
                            }
                        } catch (e) {
                            console.error('解析响应数据失败:', e);
                            if (callback && typeof callback === 'function') {
                                callback({
                                    code: -1,
                                    msg: '解析响应数据失败',
                                    rows: [],
                                    total: 0
                                });
                            }
                        }
                    } else {
                        console.error('招聘信息请求失败:', xhr.status, xhr.statusText);
                        if (callback && typeof callback === 'function') {
                            callback({
                                code: -1,
                                msg: '请求失败: ' + xhr.status + ' ' + xhr.statusText,
                                rows: [],
                                total: 0
                            });
                        }
                    }
                }
            };

            xhr.ontimeout = function() {
                console.error('招聘信息请求超时');
                if (callback && typeof callback === 'function') {
                    callback({
                        code: -1,
                        msg: '请求超时',
                        rows: [],
                        total: 0
                    });
                }
            };

            xhr.onerror = function() {
                console.error('招聘信息请求发生错误');
                if (callback && typeof callback === 'function') {
                    callback({
                        code: -1,
                        msg: '网络错误',
                        rows: [],
                        total: 0
                    });
                }
            };

            xhr.send();
        }

        // 渲染招聘信息列表
        function renderJobPostingList(jobData) {
            if (!jobData || jobData.length === 0) {
                viewModel.jobPostings([]);
                return;
            }

            // 处理数据格式
            jobData.forEach(function(job) {
                // 格式化薪资显示
                if (job.salaryMin && job.salaryMax) {
                    job.salaryRange = '￥' + job.salaryMin + '-' + job.salaryMax + '/' + (job.salaryType || '月');
                } else if (job.salaryMin) {
                    job.salaryRange = '￥' + job.salaryMin + '+/' + (job.salaryType || '月');
                } else {
                    job.salaryRange = '面议';
                }

                // 格式化发布时间
                if (job.createTime) {
                    var createDate = new Date(job.createTime);
                    var now = new Date();
                    var diffTime = now - createDate;
                    var diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

                    if (diffDays === 0) {
                        job.publishTimeText = '今天发布';
                    } else if (diffDays === 1) {
                        job.publishTimeText = '昨天发布';
                    } else if (diffDays < 7) {
                        job.publishTimeText = diffDays + '天前发布';
                    } else {
                        job.publishTimeText = createDate.toLocaleDateString();
                    }
                } else {
                    job.publishTimeText = '发布时间未知';
                }
            });

            viewModel.jobPostings(jobData);
            console.log('招聘信息列表渲染完成，共', jobData.length, '条数据');
        }

        // 加载招聘信息列表
        function loadJobPostingList() {
            // 更新状态
            if (document.getElementById('loadStatus')) {
                document.getElementById('loadStatus').textContent = '正在加载...';
            }

            var obj = {
                pageSize: 10,
                pageNum: 1
            };

            // 添加筛选条件
            if (searchParams.keyword) {
                obj.keyword = searchParams.keyword;
            }
            if (searchParams.jobType) {
                obj.jobType = searchParams.jobType;
            }
            if (searchParams.location) {
                obj.workLocation = searchParams.location;
            }

            console.log('招聘信息请求参数:', obj);

            jobPostingAjaxRequest('public/job/postings', obj, function(data){
                console.log('招聘信息API响应:', data);

                if(data.code == 0 || data.code == 200) {
                    var rows = data.rows || data.data || [];
                    console.log('获取到的招聘信息数据:', rows);

                    // 存储数据并渲染
                    jobPostingList = rows;
                    renderJobPostingList(rows);

                    // 更新状态
                    if (document.getElementById('loadStatus')) {
                        document.getElementById('loadStatus').textContent = 'API加载成功 (' + rows.length + '条)';
                    }
                } else {
                    console.error('获取招聘信息列表失败：', data.msg || data.message);

                    // 如果API失败，加载模拟数据用于测试
                    loadMockJobPostings();

                    // 更新状态
                    if (document.getElementById('loadStatus')) {
                        document.getElementById('loadStatus').textContent = 'API失败，使用模拟数据';
                    }
                }
            });
        }

        // 模拟招聘信息数据用于测试
        function loadMockJobPostings() {
            console.log('加载模拟招聘信息数据');
            var mockData = [
                {
                    jobId: 1,
                    jobTitle: 'Java高级开发工程师',
                    jobDescription: '负责公司核心业务系统的开发和维护，要求熟练掌握Spring Boot、MyBatis等技术栈',
                    jobType: '全职',
                    jobCategory: 'IT技术',
                    workLocation: '青岛市市南区',
                    salaryMin: 8000,
                    salaryMax: 15000,
                    salaryType: '月',
                    viewCount: 156,
                    createTime: new Date().toISOString(),
                    skillsRequired: 'Java,Spring Boot,MySQL,Redis',
                    workExperienceRequired: '3-5年',
                    educationRequired: '本科',
                    companyName: '青岛科技有限公司',
                    contactPerson: '张经理',
                    contactPhone: '13800138001'
                },
                {
                    jobId: 2,
                    jobTitle: '前端开发工程师',
                    jobDescription: '负责公司前端页面开发，熟练使用Vue.js、React等前端框架',
                    jobType: '全职',
                    jobCategory: 'IT技术',
                    workLocation: '青岛市崂山区',
                    salaryMin: 6000,
                    salaryMax: 12000,
                    salaryType: '月',
                    viewCount: 89,
                    createTime: new Date(Date.now() - 24*60*60*1000).toISOString(),
                    skillsRequired: 'Vue.js,React,JavaScript,CSS3,HTML5',
                    workExperienceRequired: '2-4年',
                    educationRequired: '大专',
                    companyName: '青岛互联网公司',
                    contactPerson: '李经理',
                    contactPhone: '13800138002'
                },
                {
                    jobId: 3,
                    jobTitle: '餐厅服务员',
                    jobDescription: '负责餐厅日常服务工作，包括点餐、上菜、收银等',
                    jobType: '兼职',
                    jobCategory: '餐饮服务',
                    workLocation: '青岛市市北区',
                    salaryMin: 20,
                    salaryMax: 25,
                    salaryType: '小时',
                    viewCount: 234,
                    createTime: new Date(Date.now() - 2*24*60*60*1000).toISOString(),
                    skillsRequired: '服务意识,沟通能力,责任心',
                    workExperienceRequired: '无要求',
                    educationRequired: '不限',
                    companyName: '青岛美食餐厅',
                    contactPerson: '王店长',
                    contactPhone: '13800138003'
                },
                {
                    jobId: 4,
                    jobTitle: '保洁员',
                    jobDescription: '负责办公楼日常清洁工作，包括地面清洁、垃圾清理等',
                    jobType: '临时工',
                    jobCategory: '保洁清洁',
                    workLocation: '青岛市李沧区',
                    salaryMin: 150,
                    salaryMax: 200,
                    salaryType: '日',
                    viewCount: 67,
                    createTime: new Date(Date.now() - 3*24*60*60*1000).toISOString(),
                    skillsRequired: '细心,勤劳,责任心',
                    workExperienceRequired: '无要求',
                    educationRequired: '不限',
                    companyName: '青岛物业管理公司',
                    contactPerson: '陈主管',
                    contactPhone: '13800138004'
                },
                {
                    jobId: 5,
                    jobTitle: '销售代表',
                    jobDescription: '负责公司产品销售，开发新客户，维护老客户关系',
                    jobType: '全职',
                    jobCategory: '销售',
                    workLocation: '青岛市城阳区',
                    salaryMin: 4000,
                    salaryMax: 8000,
                    salaryType: '月',
                    viewCount: 123,
                    createTime: new Date(Date.now() - 5*24*60*60*1000).toISOString(),
                    skillsRequired: '销售技巧,沟通能力,客户维护',
                    workExperienceRequired: '1-3年',
                    educationRequired: '大专',
                    companyName: '青岛贸易有限公司',
                    contactPerson: '赵经理',
                    contactPhone: '13800138005'
                }
            ];

            // 存储模拟数据并渲染
            jobPostingList = mockData;
            renderJobPostingList(mockData);
            console.log('模拟招聘信息数据已设置并渲染，共', mockData.length, '条数据');
        }

        // 搜索招聘信息
        function searchJobs() {
            var keyword = document.getElementById('jobSearchInput').value.trim();
            searchParams.keyword = keyword;
            loadJobPostingList();
        }

        // 筛选招聘信息
        function filterJobs() {
            var jobType = document.getElementById('jobTypeFilter').value;
            var location = document.getElementById('locationFilter').value;
            var salary = document.getElementById('salaryFilter').value;

            searchParams.jobType = jobType;
            searchParams.location = location;
            searchParams.salaryRange = salary;

            loadJobPostingList();
        }

        // 刷新招聘信息数据
        function refreshJobPostings() {
            console.log('刷新招聘信息数据...');

            // 清空筛选条件
            searchParams = {
                keyword: '',
                jobType: '',
                location: '',
                salaryRange: ''
            };

            // 清空输入框
            if (document.getElementById('jobSearchInput')) {
                document.getElementById('jobSearchInput').value = '';
            }
            if (document.getElementById('jobTypeFilter')) {
                document.getElementById('jobTypeFilter').value = '';
            }
            if (document.getElementById('locationFilter')) {
                document.getElementById('locationFilter').value = '';
            }
            if (document.getElementById('salaryFilter')) {
                document.getElementById('salaryFilter').value = '';
            }

            // 重新加载数据
            loadJobPostingList();
        }

        // 初始化招聘信息页面
        function initJobPostingsPage() {
            console.log('开始初始化招聘信息页面...');

            // 显示页面加载时间
            if (document.getElementById('loadTime')) {
                document.getElementById('loadTime').textContent = new Date().toLocaleTimeString();
            }

            // 标记页面已准备好
            isPageReady = true;

            console.log('招聘信息页面初始化完成，开始加载数据...');
            // 加载招聘信息列表
            loadJobPostingList();
        }

        // 确保在所有资源加载完成后初始化
        window.addEventListener('load', function() {
            console.log('Window load event triggered for talent special page');

            // 初始化招聘信息页面
            initJobPostingsPage();
        });

    </script>
</body>

</html>