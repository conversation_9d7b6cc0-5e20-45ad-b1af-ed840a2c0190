<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 青岛市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>找人才-青创通 · 青岛市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!--分页 css-->
    <link rel="stylesheet" type="text/css" href="../public/plugins/pagination/pagination.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <link rel="stylesheet" type="text/css" href="../public/css/commonNew.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/talentSpecial.css?v=202505221517" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />
</head>

<body id="viewModelBox">
    <div id="headerBar"></div>
    <!-- banner start -->
    <div class="bannerBox pr">
        <div class="bannerSlide" >
            <img src="./images/ts_bannerBg.jpg" >
        </div>
    </div>
    <!-- banner end -->
    <!-- box1 start -->
    <div class="box1 width100">
        <div class="conAuto1400 clearfix">
            <a  href="./calendar.html" class="numBox numBox1 fl transi clearfix upward shadowHover">
                <div class="numRight fr">
                    <p class="f24 fb">招聘信息</p>
                            <img src="images/talentSpecial_arrowIcon.png" >
                </div>
            </a>
        </div>
    </div>
    <!-- box1 end -->
    <!-- box5 start -->
    <div class="box5 width100">
        <div class="conAuto1400">
            <div class="clearfix">
                <p class="titleP fl fb">招聘会<em>专场</em></p>
                <ul class="sxUl clearfix fl ml40" data-bind="foreach:typeVo()">
                    <li class="fl transi f16 text-gray9 mr20"
                        data-bind="text:baseName,css:{on:baseId==$root.jobfairType()},event: { mouseover: $root.linkFun}">
                    </li>
                </ul>
                <a  href="javascript:;" class="moreBtn fr transi"
                    data-bind="attr:{href:'./answersList.html?jobfairType='+jobfairType()}">
                    <span class="inlineblock text-white">更多</span>
                </a>
            </div>
            <!-- 现场招聘会 -->
            <div class="xcZph">
                <!-- ko if:answersList().length > 0 -->
                <ul class="zphList clearfix" data-bind="foreach:answersList">
                    <li class="fl pr" data-bind="click:$root.goAnswersDetail">
                        <div class="li pa transi">
                            <p class="jobfairName textoverflow" data-bind="text:jobfairName,attr:{href:'./answersDetail.html?id='+baseId + '&type='+jobfairType}"></p>
                            <p class="time textoverflow"><span data-bind="text:holdingBeginTime"></span>至<span data-bind="text:holdingEndTime"></span></p>
                            <p class="add textoverflow" data-bind="text:remark?remark:'--'"></p>
                            <p class="btns transi">立即查看</p>
                        </div>
                        
                    </li>
                </ul>
                <!-- /ko -->
                <!-- ko if:answersList().length == 0 -->
                <div class="nodataPic" style="margin: 0 auto;"></div>
                <!-- /ko -->
            </div>
            <!-- <iframe style="width: 1400px; height: 210px;" class="xcZph" src="https://fw.rc.qingdao.gov.cn/qdzhrcww/work/f60050114/queryHyzpIndex.action" frameborder="0"></iframe> -->
            <!-- 现场招聘会 -->
            <!-- 线上招聘会 -->
            <div class="xsZph none">
                <!-- ko if:answersList().length > 0 -->
                <ul class="zphList clearfix" data-bind="foreach:answersList">
                    <li class="fl pr" data-bind="click:$root.goAnswersDetail">
                        <div class="li pa transi">
                            <p class="jobfairName textoverflow" data-bind="text:jobfairName,attr:{href:'./answersDetail.html?id='+baseId + '&type='+jobfairType}"></p>
                            <p class="time textoverflow"><span data-bind="text:holdingBeginTime"></span>至<span data-bind="text:holdingEndTime"></span></p>
                            <p class="add textoverflow" data-bind="text:remark?remark:'--'"></p>
                            <p class="btns transi">立即查看</p>
                        </div>
                        
                    </li>
                </ul>
                <!-- /ko -->
                <!-- ko if:answersList().length == 0 -->
                <div class="nodataPic" style="margin: 0 auto;"></div>
                <!-- /ko -->
            </div>
            <!-- <iframe style="width: 1400px; height: 210px;" class="xsZph none" src="https://fw.rc.qingdao.gov.cn/qdzhrcww/work/f60050114/queryZphIndex.action" frameborder="0"></iframe> -->
            <!-- 线上招聘会 -->

            <!-- 招聘信息列表 -->
            <div class="jobListSection">
                <div class="clearfix mb20">
                    <p class="titleP fl fb">招聘信息<em>列表</em></p>
                    <div class="searchBox fr">
                        <input type="text" placeholder="搜索招聘信息..." id="jobSearchInput" class="searchInput">
                        <button class="searchBtn" onclick="searchJobs()">搜索</button>
                    </div>
                </div>

                <!-- 筛选条件 -->
                <div class="filterBox mb20">
                    <div class="filterItem">
                        <label>工作类型：</label>
                        <select id="jobTypeFilter" onchange="filterJobs()">
                            <option value="">全部</option>
                            <option value="全职">全职</option>
                            <option value="兼职">兼职</option>
                            <option value="临时工">临时工</option>
                            <option value="小时工">小时工</option>
                        </select>
                    </div>
                    <div class="filterItem">
                        <label>工作地点：</label>
                        <select id="locationFilter" onchange="filterJobs()">
                            <option value="">全部</option>
                            <option value="市南区">市南区</option>
                            <option value="市北区">市北区</option>
                            <option value="李沧区">李沧区</option>
                            <option value="崂山区">崂山区</option>
                            <option value="城阳区">城阳区</option>
                            <option value="黄岛区">黄岛区</option>
                        </select>
                    </div>
                    <div class="filterItem">
                        <label>薪资范围：</label>
                        <select id="salaryFilter" onchange="filterJobs()">
                            <option value="">全部</option>
                            <option value="0-3000">3000以下</option>
                            <option value="3000-5000">3000-5000</option>
                            <option value="5000-8000">5000-8000</option>
                            <option value="8000-12000">8000-12000</option>
                            <option value="12000-99999">12000以上</option>
                        </select>
                    </div>
                </div>

                <!-- 招聘信息列表 -->
                <div class="jobPostingsList" id="jobPostingsList">
                    <!-- 调试信息 -->
                    <div class="debug-info" style="background: #f0f0f0; padding: 10px; margin-bottom: 10px; font-size: 12px;">
                        <p>调试信息 - 招聘信息数量: <span data-bind="text: jobPostings().length"></span></p>
                        <p>页面加载时间: <span id="loadTime"></span></p>
                    </div>

                    <!-- ko if:jobPostings().length > 0 -->
                    <div class="jobList" data-bind="foreach:jobPostings">
                        <div class="jobCard" data-bind="click:$root.viewJobDetail">
                            <div class="jobHeader">
                                <h3 class="jobTitle" data-bind="text:jobTitle"></h3>
                                <div class="jobSalary" data-bind="text:salaryRange"></div>
                            </div>
                            <div class="jobInfo">
                                <span class="jobType" data-bind="text:jobType"></span>
                                <span class="jobLocation" data-bind="text:workLocation"></span>
                                <span class="jobCategory" data-bind="text:jobCategory"></span>
                            </div>
                            <div class="jobMeta">
                                <span class="publishTime" data-bind="text:publishTimeText"></span>
                                <span class="viewCount" data-bind="text:'浏览 ' + (viewCount || 0) + ' 次'"></span>
                                <button class="matchBtn" data-bind="click:$root.matchWorkers">匹配零工</button>
                            </div>
                        </div>
                    </div>
                    <!-- /ko -->
                    <!-- ko if:jobPostings().length == 0 -->
                    <div class="nodataPic" style="margin: 0 auto;">
                        <p>暂无招聘信息</p>
                        <p style="font-size: 12px; color: #666;">如果您看到这条消息，说明数据加载可能有问题。请检查浏览器控制台的错误信息。</p>
                    </div>
                    <!-- /ko -->
                </div>

                <!-- 分页 -->
                <div class="pagination-container" id="jobPagination"></div>
            </div>
            <!-- 招聘信息列表 end -->


        </div>
    </div>
    <!-- box6 end -->
    <div id="footerBar"></div>
    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" type="text/javascript"
        charset="utf-8"></script>
    <script src="../public/js/words.js" type="text/javascript" charset="utf-8"></script>
    <!--common js-->
    <!-- <script src="../public/js/api.js"></script> -->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/mapping.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="../public/plugins/jbox/jquery.jBox.js"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>
    <!--this page js  -->
    <script src="js/talentSpecial.js?v=202507231900" type="text/javascript" charset="utf-8"></script>
</body>

</html>