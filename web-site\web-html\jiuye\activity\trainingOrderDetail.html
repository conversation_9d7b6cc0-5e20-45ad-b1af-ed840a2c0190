<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 青岛市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>培训订单详情-青创通 · 青岛市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!--分页 css-->
    <link rel="stylesheet" type="text/css" href="../public/plugins/pagination/pagination.css" />
    <!-- swiper css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/swiper/swiper.min.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/index.css?v=202505271645" />
    <link rel="stylesheet" type="text/css" href="css/activityDetail.css?v=202502281010" />
    <link rel="stylesheet" type="text/css" href="css/layout-fix.css?v=202507221200" />

    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />
</head>

<body id="viewModelBox">
    <div id="headerBar"></div>
    <!-- main start -->
    <!-- banner -->
    <div class="bannerBox pr">
        <!-- ko if:bagnnerList().length>0 -->
        <div class="bannerSlide" data-bind="foreach:bagnnerList">
            <img src="./image/new_zhdBanner.jpg" data-bind="attr:{src:fullPath}">
        </div>
        <div class="hd" data-bind="visible:bagnnerList().length>1">
            <ul></ul>
        </div>
        <!-- /ko -->
        <!-- ko if:bagnnerList().length==0 -->
        <div class="bannerSlide">
            <img src="./image/new_zhdBanner.jpg">
        </div>
        <!-- /ko -->
    </div>
    <div class="pageBg">
        <div class="bgBox">
            <!-- 浏览路径 start -->
            <div class="pathbox2">
            </div>
            <!-- 浏览路径 start -->
            <!-- 主题内容 start -->
            <div class="conAuto3 mb30">
                <div class="main-layout-container">
                    <!-- 左侧主要内容区域 -->
                    <div class="left-main-content">
                        <div class="leftBox pa">
                            <!-- 状态标签 -->
                            <div class="status-tag-new" id="statusTagNew">
                                <span class="status-text-new" id="statusTextNew">已发布</span>
                            </div>

                            <!-- 标题区域 -->
                            <div class="title-section-new">
                                <h1 class="training-title-new" id="orderTitle">培训订单详情</h1>
                                <div class="title-meta-new">
                                    <span class="meta-tag-new type-tag-new" id="trainingType">技术培训</span>
                                    <span class="meta-tag-new level-tag-new" id="trainingLevel">高级</span>
                                    <span class="meta-tag-new category-tag-new" id="orderStatus">已发布</span>
                                </div>
                            </div>

                            <!-- 核心信息网格 -->
                            <div class="info-grid-new">
                                <div class="info-item-new">
                                    <div class="info-icon-new">🕒</div>
                                    <div class="info-content-new">
                                        <div class="info-label-new">培训时间</div>
                                        <div class="info-value-new" id="trainingTime">2025-08-01 09:00 至 2025-08-15
                                            18:00</div>
                                    </div>
                                </div>

                                <div class="info-item-new">
                                    <div class="info-icon-new">⏱️</div>
                                    <div class="info-content-new">
                                        <div class="info-label-new">培训时长</div>
                                        <div class="info-value-new" id="trainingDuration">120小时</div>
                                    </div>
                                </div>

                                <div class="info-item-new">
                                    <div class="info-icon-new">💰</div>
                                    <div class="info-content-new">
                                        <div class="info-label-new">培训费用</div>
                                        <div class="info-value-new price-highlight-new" id="trainingFee">￥5,800.00
                                        </div>
                                    </div>
                                </div>

                                <div class="info-item-new">
                                    <div class="info-icon-new">📍</div>
                                    <div class="info-content-new">
                                        <div class="info-label-new">培训地址</div>
                                        <div class="info-value-new" id="trainingAddress">青岛市市南区香港中路10号</div>
                                    </div>
                                </div>

                                <div class="info-item-new">
                                    <div class="info-icon-new">👥</div>
                                    <div class="info-content-new">
                                        <div class="info-label-new">参与人数</div>
                                        <div class="info-value-new" id="participantsInfo">15/30人</div>
                                    </div>
                                </div>

                                <div class="info-item-new">
                                    <div class="info-icon-new">📞</div>
                                    <div class="info-content-new">
                                        <div class="info-label-new">联系方式</div>
                                        <div class="info-value-new">
                                            <span id="contactPerson">张老师</span>
                                            <span id="contactPhone">0532-12345678</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 报名进度条 -->
                            <div class="progress-section-new">
                                <div class="progress-header-new">
                                    <span class="progress-label-new">报名进度</span>
                                    <span class="progress-stats-new">
                                        <span id="currentParticipants">15</span>/<span
                                            id="maxParticipants">30</span>人
                                    </span>
                                </div>
                                <div class="progress-bar-container-new">
                                    <div class="progress-bar-bg-new">
                                        <div class="progress-bar-fill-new" id="progressBarFillNew"
                                            style="width: 50%"></div>
                                    </div>
                                    <div class="progress-percentage-new" id="progressPercentageNew">50%</div>
                                </div>
                            </div>

                            <!-- 操作按钮区域 -->
                            <div class="action-section-new">
                                <div class="primary-actions-new">
                                    <button class="btn-primary-new" id="signupBtn" onclick="handleSignup()">
                                        <span class="btn-icon-new">📝</span>
                                        <span class="btn-text-new">立即报名</span>
                                    </button>
                                    <div class="price-display-new">
                                        <span class="price-label-new">费用</span>
                                        <span class="price-value-new" id="feeDisplay">￥5,800.00</span>
                                    </div>
                                </div>

                                <div class="secondary-actions-new">
                                    <button class="btn-secondary-new" onclick="addToFavorites()">
                                        <span class="btn-icon-new">❤️</span>
                                        <span class="btn-text-new">收藏</span>
                                    </button>
                                    <div class="share-tip-new" onclick="copyShareLink()" title="点击复制分享链接">
                                        <span class="tip-icon-new">📤</span>
                                        <span class="tip-text-new">点击复制分享链接</span>
                                    </div>
                                    <button class="btn-secondary-new" onclick="goBack()">
                                        <span class="btn-icon-new">↩️</span>
                                        <span class="btn-text-new">返回</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧相关推荐 -->
                    <div class="right-sidebar-content">
                        <!-- 相关培训推荐 -->
                        <div class="content-section-new sidebar-recommendations">
                            <div class="section-header-new">
                                <h2 class="section-title-new">
                                    <span class="title-icon">🎯</span>
                                    相关培训推荐
                                </h2>
                                <a class="refresh-btn-new" href="javascript:;"
                                    onclick="refreshRecommendations()" title="刷新推荐">
                                    <span>🔄</span> 换一换
                                </a>
                            </div>
                            <div class="section-content-new">
                                <div class="recommendations-list-new" id="recommendationsList">
                                    <div class="recommendation-item-new" onclick="goOrderDetail(2)">
                                        <div class="recommendation-content-new">
                                            <h3 class="recommendation-title-new">Python数据分析师培训</h3>
                                            <div class="recommendation-meta-new">
                                                <span class="recommendation-type-new">技术培训</span>
                                                <span class="recommendation-level-new">初级</span>
                                            </div>
                                            <div class="recommendation-price-new">￥3,800.00</div>
                                        </div>
                                    </div>
                                    <div class="recommendation-item-new" onclick="goOrderDetail(3)">
                                        <div class="recommendation-content-new">
                                            <h3 class="recommendation-title-new">企业管理与领导力提升</h3>
                                            <div class="recommendation-meta-new">
                                                <span class="recommendation-type-new">管理培训</span>
                                                <span class="recommendation-level-new">中级</span>
                                            </div>
                                            <div class="recommendation-price-new">￥4,500.00</div>
                                        </div>
                                    </div>
                                    <div class="recommendation-item-new" onclick="goOrderDetail(4)">
                                        <div class="recommendation-content-new">
                                            <h3 class="recommendation-title-new">财务分析与预算管理</h3>
                                            <div class="recommendation-meta-new">
                                                <span class="recommendation-type-new">管理培训</span>
                                                <span class="recommendation-level-new">中级</span>
                                            </div>
                                            <div class="recommendation-price-new">￥3,500.00</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 培训详细内容区域 -->
            <div class="conAuto2 mb30">
                <div class="content-layout-new">
                    <div class="main-content-new">
                        <!-- 培训描述 -->
                        <div class="content-section-new">
                            <h2 class="section-title-new">培训描述</h2>
                            <div class="section-content-new" id="trainingDescription">
                                <div class="loading-content">
                                    <div class="loading-spinner"></div>
                                    <p>正在加载培训详情...</p>
                                </div>
                            </div>
                        </div>

                        <!-- 报名要求 -->
                        <div class="content-section-new" id="requirementsSection" style="display: none;">
                            <h2 class="section-title-new">报名要求</h2>
                            <div class="section-content-new" id="trainingRequirements">
                            </div>
                        </div>

                        <!-- 证书信息 -->
                        <div class="content-section-new" id="certificateSection" style="display: none;">
                            <h2 class="section-title-new">证书信息</h2>
                            <div class="section-content-new" id="certificateInfo">
                            </div>
                        </div>

                        <!-- 培训安排 -->
                        <div class="content-section-new">
                            <h2 class="section-title-new">培训安排</h2>
                            <div class="section-content-new" id="trainingSchedule">
                                <div class="schedule-grid-new">
                                    <div class="schedule-item-new">
                                        <div class="schedule-icon-new">🕒</div>
                                        <div class="schedule-details-new">
                                            <div class="schedule-label-new">培训时间</div>
                                            <div class="schedule-value-new" id="scheduleTime">待确定</div>
                                        </div>
                                    </div>
                                    <div class="schedule-item-new">
                                        <div class="schedule-icon-new">📍</div>
                                        <div class="schedule-details-new">
                                            <div class="schedule-label-new">培训地点</div>
                                            <div class="schedule-value-new" id="scheduleLocation">待确定</div>
                                        </div>
                                    </div>
                                    <div class="schedule-item-new">
                                        <div class="schedule-icon-new">⏱️</div>
                                        <div class="schedule-details-new">
                                            <div class="schedule-label-new">培训时长</div>
                                            <div class="schedule-value-new" id="scheduleDuration">待确定</div>
                                        </div>
                                    </div>
                                    <div class="schedule-item-new">
                                        <div class="schedule-icon-new">📞</div>
                                        <div class="schedule-details-new">
                                            <div class="schedule-label-new">联系方式</div>
                                            <div class="schedule-value-new" id="scheduleContact">待确定</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <!-- 主题内容 end -->
        </div>
    </div>

    <!-- 底部信息条 -->
    <div class="bottomBar">
        <div class="conAuto1400">
            <div class="bottomContent">
                <div class="bottomLeft">
                    <div class="bottomLogo">
                        <span class="logoText">就业培训平台</span>
                        <span class="logoSubtext">Employment Training Platform</span>
                    </div>
                    <div class="bottomInfo">
                        <p class="info-text">致力于为求职者提供专业的就业培训服务</p>
                        <p class="info-text">打造高质量的职业技能提升平台</p>
                    </div>
                </div>
                <div class="bottomRight">
                    <div class="contact-info">
                        <p class="contact-title">联系我们</p>
                        <p class="contact-item">服务热线：400-123-4567</p>
                        <p class="contact-item">邮箱：<EMAIL></p>
                    </div>
                    <div class="service-info">
                        <p class="service-title">服务时间</p>
                        <p class="service-item">周一至周五 9:00-18:00</p>
                        <p class="service-item">周六至周日 9:00-17:00</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- main end -->
    <!-- 底部 开始 -->
    <!-- 底部 开始 -->
    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <!--jquery js-->
    <script src="../public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" type="text/javascript" charset="utf-8">
    </script>
    <!--分页 js-->
    <script type="text/javascript" src="../public/plugins/pagination/jquery.pagination.js"></script>
    <script src="../public/plugins/swiper/swiper.min5.js" type="text/javascript" charset="utf-8"></script>
    <!--common js-->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/mapping.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="../public/plugins/jbox/jquery.jBox.js"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>
    <!--this page js  -->
    <script src="js/indexs.js?v=202505280848" type="text/javascript" charset="utf-8"></script>

    <script>
        // 全局变量
        var orderDetail = {};
        var orderId = getUrlParam('id') || '1';

        // 获取URL参数
        function getUrlParam(name) {
            var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            var r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }

        // 获取状态文本
        function getStatusText(status) {
            var statusMap = {
                '0': '草稿',
                '1': '已发布',
                '2': '进行中',
                '3': '已完成',
                '4': '已取消'
            };
            return statusMap[status] || '未知';
        }

        // 获取状态样式类
        function getStatusClass(status) {
            var classMap = {
                '0': 'status-draft',
                '1': 'status-published',
                '2': 'status-ongoing',
                '3': 'status-completed',
                '4': 'status-cancelled'
            };
            return classMap[status] || 'status-unknown';
        }

        // 返回列表页
        function goBack() {
            window.location.href = 'trainingOrderList.html';
        }

        // 跳转到订单详情
        function goOrderDetail(id) {
            window.location.href = 'trainingOrderDetail.html?id=' + id;
        }

        // 处理报名
        function handleSignup() {
            if (!orderDetail.orderId) {
                showToast('订单信息加载中，请稍后再试', 'warning');
                return;
            }

            // 检查报名条件
            var current = orderDetail.currentParticipants || 0;
            var max = orderDetail.maxParticipants || 0;
            var now = new Date();
            var registrationDeadline = orderDetail.registrationDeadline ? new Date(orderDetail.registrationDeadline) : null;

            if (registrationDeadline && now > registrationDeadline) {
                showToast('报名已截止', 'error');
                return;
            }

            if (orderDetail.orderStatus === '4') {
                showToast('培训已取消', 'error');
                return;
            }

            if (orderDetail.orderStatus === '3') {
                showToast('培训已完成', 'info');
                return;
            }

            if (orderDetail.orderStatus === '0') {
                showToast('培训尚未发布', 'warning');
                return;
            }

            if (current >= max && max > 0) {
                showToast('报名人数已满', 'warning');
                return;
            }

            // 跳转到后台登录页面，登录后进行报名
            redirectToBackendSignup();
        }

        // 跳转到后台申请页面
        function redirectToBackendSignup() {
            // 构建后台申请页面URL
            var backendUrl = 'http://localhost:80/'; // 后台系统地址
            var signupUrl = backendUrl + '#/signup';

            // 在新窗口打开后台申请页面
            window.open(signupUrl, '_blank');

            showToast('正在跳转到申请页面...', 'info');
        }

        // 提交报名（保留原有函数，用于其他地方可能的调用）
        function submitSignup() {
            showToast('正在提交报名...', 'info');

            // 模拟API调用
            setTimeout(function () {
                // 这里应该调用真实的报名API
                // var result = callSignupAPI(orderDetail.orderId);

                // 模拟成功
                showToast('报名成功！', 'success');

                // 更新报名人数
                if (orderDetail.currentParticipants < orderDetail.maxParticipants) {
                    orderDetail.currentParticipants = (orderDetail.currentParticipants || 0) + 1;
                    updateParticipantsDisplay();
                    updateSignupButton();
                }
            }, 1000);
        }

        // 添加到收藏
        function addToFavorites() {
            showToast('已添加到收藏', 'success');
        }

        // 复制链接功能（点击分享提示时触发）
        function copyShareLink() {
            try {
                // 尝试使用现代API
                if (navigator.clipboard && window.isSecureContext) {
                    navigator.clipboard.writeText(window.location.href).then(function () {
                        showToast('链接已复制到剪贴板', 'success');
                    }).catch(function () {
                        fallbackCopyTextToClipboard();
                    });
                } else {
                    fallbackCopyTextToClipboard();
                }
            } catch (err) {
                fallbackCopyTextToClipboard();
            }
        }

        // 备用复制方法
        function fallbackCopyTextToClipboard() {
            var textArea = document.createElement("textarea");
            textArea.value = window.location.href;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                var successful = document.execCommand('copy');
                if (successful) {
                    showToast('链接已复制到剪贴板', 'success');
                } else {
                    showToast('复制失败，请手动复制链接', 'error');
                }
            } catch (err) {
                showToast('复制失败，请手动复制链接', 'error');
            }

            document.body.removeChild(textArea);
        }

        // 刷新推荐
        function refreshRecommendations() {
            showToast('推荐列表已刷新', 'info');
            loadRecommendedOrders();
        }

        // 更新推荐列表
        function updateRecommendationsList() {
            // 这里可以调用API获取推荐数据，现在使用模拟数据
            var recommendations = [
                {
                    orderId: 2,
                    orderTitle: 'Python数据分析师培训',
                    trainingType: '技术培训',
                    trainingLevel: '初级',
                    trainingFee: 3800.00
                },
                {
                    orderId: 3,
                    orderTitle: '企业管理与领导力提升',
                    trainingType: '管理培训',
                    trainingLevel: '中级',
                    trainingFee: 4500.00
                },
                {
                    orderId: 4,
                    orderTitle: '财务分析与预算管理',
                    trainingType: '管理培训',
                    trainingLevel: '中级',
                    trainingFee: 3500.00
                }
            ];

            renderRecommendationsList(recommendations);
        }

        // 渲染推荐列表
        function renderRecommendationsList(recommendations) {
            var container = document.getElementById('recommendationsList');
            if (!container) return;

            var html = '';
            recommendations.forEach(function (item) {
                if (item.orderId != orderDetail.orderId) { // 不显示当前订单
                    var feeText = item.trainingFee && item.trainingFee > 0 ?
                        '￥' + parseFloat(item.trainingFee).toFixed(2) : '免费';

                    html += `
                        <div class="recommendation-item-new" onclick="goOrderDetail(${item.orderId})">
                            <div class="recommendation-content-new">
                                <h3 class="recommendation-title-new">${item.orderTitle || '--'}</h3>
                                <div class="recommendation-meta-new">
                                    <span class="recommendation-type-new">${item.trainingType || '--'}</span>
                                    <span class="recommendation-level-new">${item.trainingLevel || '--'}</span>
                                </div>
                                <div class="recommendation-price-new">${feeText}</div>
                            </div>
                        </div>
                    `;
                }
            });

            container.innerHTML = html;
        }

        // 渲染订单详情
        function renderOrderDetail(order) {
            if (!order) {
                console.error('订单数据为空');
                return;
            }

            orderDetail = order;

            // 更新页面标题
            document.title = (order.orderTitle || '培训订单详情') + '-青创通 · 青岛市创业服务云平台';

            // 更新基本信息
            document.getElementById('orderTitle').textContent = order.orderTitle || '--';
            document.getElementById('trainingType').textContent = order.trainingType || '--';
            document.getElementById('trainingLevel').textContent = order.trainingLevel || '--';
            document.getElementById('orderStatus').textContent = getStatusText(order.orderStatus);

            // 更新时间和时长
            var timeText = '--';
            if (order.startDate && order.endDate) {
                timeText = formatDateTime(order.startDate) + ' 至 ' + formatDateTime(order.endDate);
            } else if (order.startDate) {
                timeText = formatDateTime(order.startDate) + ' 开始';
            }
            document.getElementById('trainingTime').textContent = timeText;
            document.getElementById('trainingDuration').textContent = order.trainingDuration ? order.trainingDuration + '小时' : '--';

            // 更新费用信息
            var feeElement = document.getElementById('trainingFee');
            var feeDisplayElement = document.getElementById('feeDisplay');
            if (order.trainingFee && order.trainingFee > 0) {
                var feeText = '￥' + parseFloat(order.trainingFee).toFixed(2);
                feeElement.textContent = feeText;
                feeDisplayElement.textContent = feeText;
                feeElement.className = 'fee-text';
            } else {
                feeElement.textContent = '免费';
                feeDisplayElement.textContent = '免费';
                feeElement.className = 'fee-text free';
            }

            // 更新地址
            document.getElementById('trainingAddress').textContent = order.trainingAddress || '--';

            // 更新联系信息
            document.getElementById('contactPerson').textContent = order.contactPerson || '--';
            document.getElementById('contactPhone').textContent = order.contactPhone || '--';

            // 更新参与人数
            updateParticipantsDisplay();

            // 更新状态显示
            updateStatusDisplay();

            // 更新倒计时
            updateCountdown();

            // 更新培训描述
            if (order.orderDescription) {
                document.getElementById('trainingDescription').innerHTML = order.orderDescription;
            } else {
                document.getElementById('trainingDescription').innerHTML = '<p>暂无详细描述</p>';
            }

            // 更新报名要求
            if (order.requirements) {
                document.getElementById('requirementsSection').style.display = 'block';
                document.getElementById('trainingRequirements').innerHTML = order.requirements;
            }

            // 更新证书信息
            if (order.certificateInfo) {
                document.getElementById('certificateSection').style.display = 'block';
                document.getElementById('certificateInfo').innerHTML = order.certificateInfo;
            }

            // 更新培训安排信息
            document.getElementById('scheduleTime').textContent = timeText;
            document.getElementById('scheduleLocation').textContent = order.trainingAddress || '--';
            document.getElementById('scheduleDuration').textContent = order.trainingDuration ? order.trainingDuration + '小时' : '--';
            document.getElementById('scheduleContact').textContent = (order.contactPerson || '--') +
                (order.contactPhone ? ' (' + order.contactPhone + ')' : '');

            // 更新推荐列表
            updateRecommendationsList();
            // 更新报名按钮状态
            updateSignupButton();
        }

        // 格式化日期时间
        function formatDateTime(dateStr) {
            if (!dateStr) return '--';
            var date = new Date(dateStr);
            if (isNaN(date.getTime())) return dateStr;

            var year = date.getFullYear();
            var month = String(date.getMonth() + 1).padStart(2, '0');
            var day = String(date.getDate()).padStart(2, '0');
            var hours = String(date.getHours()).padStart(2, '0');
            var minutes = String(date.getMinutes()).padStart(2, '0');

            return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
        }

        // 更新参与人数显示
        function updateParticipantsDisplay() {
            var current = orderDetail.currentParticipants || 0;
            var max = orderDetail.maxParticipants || 0;
            var participantsText = current + '/' + max + '人';

            document.getElementById('participantsInfo').textContent = participantsText;
            document.getElementById('currentParticipants').textContent = current;
            document.getElementById('maxParticipants').textContent = max;

            // 更新进度条
            var progressPercent = max > 0 ? (current / max * 100) : 0;
            var progressBarFill = document.getElementById('progressBarFillNew');
            var progressPercentage = document.getElementById('progressPercentageNew');

            if (progressBarFill) {
                progressBarFill.style.width = progressPercent + '%';
            }
            if (progressPercentage) {
                progressPercentage.textContent = Math.round(progressPercent) + '%';
            }
        }

        // 更新状态显示
        function updateStatusDisplay() {
            var statusText = getStatusText(orderDetail.orderStatus);
            var statusTextElement = document.getElementById('statusTextNew');

            if (statusTextElement) {
                statusTextElement.textContent = statusText;
                statusTextElement.className = 'status-text-new ' + getStatusClass(orderDetail.orderStatus);
            }

            // 更新快速状态指示器
            var quickStatusIndicator = document.getElementById('quickStatusIndicatorNew');
            if (quickStatusIndicator) {
                var statusIcon = '🟢'; // 默认绿色
                if (orderDetail.orderStatus === '0') statusIcon = '🔴'; // 草稿
                else if (orderDetail.orderStatus === '2') statusIcon = '🟡'; // 进行中
                else if (orderDetail.orderStatus === '3') statusIcon = '🔵'; // 已完成
                else if (orderDetail.orderStatus === '4') statusIcon = '⚫'; // 已取消

                quickStatusIndicator.textContent = statusIcon;
            }
        }

        // 更新倒计时
        function updateCountdown() {
            if (!orderDetail.registrationDeadline) return;

            var deadline = new Date(orderDetail.registrationDeadline);
            var now = new Date();
            var timeDiff = deadline - now;

            if (timeDiff <= 0) {
                // 已截止
                var countdownSection = document.getElementById('countdownSectionNew');
                if (countdownSection) {
                    countdownSection.innerHTML = '<div class="countdown-label-new">报名已截止</div>';
                }
                return;
            }

            var days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
            var hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            var minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));

            var daysElement = document.getElementById('daysLeftNew');
            var hoursElement = document.getElementById('hoursLeftNew');
            var minutesElement = document.getElementById('minutesLeftNew');

            if (daysElement) daysElement.textContent = days;
            if (hoursElement) hoursElement.textContent = hours;
            if (minutesElement) minutesElement.textContent = minutes;
        }

        // 更新报名按钮状态
        function updateSignupButton() {
            var signupBtn = document.getElementById('signupBtn');
            var quickSignupBtn = document.getElementById('quickSignupBtnNew');
            var current = orderDetail.currentParticipants || 0;
            var max = orderDetail.maxParticipants || 0;
            var now = new Date();
            var registrationDeadline = orderDetail.registrationDeadline ? new Date(orderDetail.registrationDeadline) : null;

            var buttonText = '立即报名';
            var buttonIcon = '📝';
            var isDisabled = false;

            // 检查报名截止时间
            if (registrationDeadline && now > registrationDeadline) {
                buttonText = '报名已截止';
                buttonIcon = '⏰';
                isDisabled = true;
            } else if (orderDetail.orderStatus === '4') {
                buttonText = '已取消';
                buttonIcon = '❌';
                isDisabled = true;
            } else if (orderDetail.orderStatus === '3') {
                buttonText = '已完成';
                buttonIcon = '✅';
                isDisabled = true;
            } else if (orderDetail.orderStatus === '0') {
                buttonText = '未发布';
                buttonIcon = '📝';
                isDisabled = true;
            } else if (current >= max && max > 0) {
                buttonText = '报名已满';
                buttonIcon = '👥';
                isDisabled = true;
            }

            // 更新主要报名按钮
            if (signupBtn) {
                signupBtn.innerHTML = '<span class="btn-icon-new">' + buttonIcon + '</span><span class="btn-text-new">' + buttonText + '</span>';
                signupBtn.className = 'btn-primary-new';
                signupBtn.onclick = isDisabled ? null : handleSignup;
                signupBtn.style.cursor = isDisabled ? 'not-allowed' : 'pointer';
                if (isDisabled) {
                    signupBtn.style.opacity = '0.6';
                } else {
                    signupBtn.style.opacity = '1';
                }
            }

            // 更新快速报名按钮
            if (quickSignupBtn) {
                quickSignupBtn.innerHTML = '<span class="btn-icon-new">⚡</span><span class="btn-text-new">' + (isDisabled ? buttonText : '快速报名') + '</span>';
                quickSignupBtn.onclick = isDisabled ? null : handleSignup;
                quickSignupBtn.style.cursor = isDisabled ? 'not-allowed' : 'pointer';
                if (isDisabled) {
                    quickSignupBtn.style.opacity = '0.6';
                    quickSignupBtn.style.background = '#ccc';
                } else {
                    quickSignupBtn.style.opacity = '1';
                    quickSignupBtn.style.background = 'linear-gradient(135deg, #ff6b35 0%, #ff8c42 100%)';
                }
            }
        }

        // 更新主要报名按钮状态
        function updateMainSignupButton() {
            var signupBtn = document.getElementById('signupBtnMain');
            var current = orderDetail.currentParticipants || 0;
            var max = orderDetail.maxParticipants || 0;
            var now = new Date();
            var registrationDeadline = orderDetail.registrationDeadline ? new Date(orderDetail.registrationDeadline) : null;

            // 检查报名截止时间
            if (registrationDeadline && now > registrationDeadline) {
                signupBtn.innerHTML = '<span class="btn-icon">⏰</span><span class="btn-text">报名已截止</span>';
                signupBtn.style.background = '#ff4d4f';
                signupBtn.style.color = '#fff';
                signupBtn.onclick = null;
                signupBtn.style.cursor = 'not-allowed';
                return;
            }

            if (orderDetail.orderStatus === '4') {
                signupBtn.innerHTML = '<span class="btn-icon">❌</span><span class="btn-text">已取消</span>';
                signupBtn.style.background = '#ff4d4f';
                signupBtn.style.color = '#fff';
                signupBtn.onclick = null;
                signupBtn.style.cursor = 'not-allowed';
            } else if (orderDetail.orderStatus === '3') {
                signupBtn.innerHTML = '<span class="btn-icon">✅</span><span class="btn-text">已完成</span>';
                signupBtn.style.background = '#52c41a';
                signupBtn.style.color = '#fff';
                signupBtn.onclick = null;
                signupBtn.style.cursor = 'not-allowed';
            } else if (orderDetail.orderStatus === '0') {
                signupBtn.innerHTML = '<span class="btn-icon">📝</span><span class="btn-text">未发布</span>';
                signupBtn.style.background = '#d9d9d9';
                signupBtn.style.color = '#999';
                signupBtn.onclick = null;
                signupBtn.style.cursor = 'not-allowed';
            } else if (current >= max && max > 0) {
                signupBtn.innerHTML = '<span class="btn-icon">👥</span><span class="btn-text">报名已满</span>';
                signupBtn.style.background = '#ff4d4f';
                signupBtn.style.color = '#fff';
                signupBtn.onclick = null;
                signupBtn.style.cursor = 'not-allowed';
            } else {
                signupBtn.innerHTML = '<span class="btn-icon">📝</span><span class="btn-text">立即报名</span>';
                signupBtn.style.background = '#fff';
                signupBtn.style.color = '#667eea';
                signupBtn.onclick = handleSignup;
                signupBtn.style.cursor = 'pointer';
            }
        }

        // 更新报名按钮状态
        function updateSignupButton() {
            var signupBtn = document.getElementById('signupBtn');
            var current = orderDetail.currentParticipants || 0;
            var max = orderDetail.maxParticipants || 0;

            if (orderDetail.orderStatus === '4') {
                signupBtn.textContent = '已取消';
                signupBtn.className = 'fl btnCont4';
                signupBtn.onclick = null;
            } else if (orderDetail.orderStatus === '3') {
                signupBtn.textContent = '已完成';
                signupBtn.className = 'fl btnCont3';
                signupBtn.onclick = null;
            } else if (current >= max) {
                signupBtn.textContent = '报名已满';
                signupBtn.className = 'fl btnCont4';
                signupBtn.onclick = null;
            } else {
                signupBtn.textContent = '立即报名';
                signupBtn.className = 'fl btnCont1 signup';
                signupBtn.onclick = handleSignup;
            }
        }

        // 原生Ajax请求函数
        function customAjaxRequest(url, params, callback) {
            var baseUrl = 'http://localhost:80/sux-admin/';

            // 构建查询参数
            var queryString = '';
            if (params && typeof params === 'object') {
                var paramArray = [];
                for (var key in params) {
                    if (params.hasOwnProperty(key) && params[key] !== null && params[key] !== undefined && params[key] !== '') {
                        paramArray.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
                    }
                }
                queryString = paramArray.length > 0 ? '?' + paramArray.join('&') : '';
            }

            var xhr = new XMLHttpRequest();
            xhr.open('GET', baseUrl + url + queryString, true);
            xhr.timeout = 30000;
            xhr.setRequestHeader('Content-Type', 'application/json');

            xhr.onreadystatechange = function () {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            if (callback && typeof callback === 'function') {
                                callback(response);
                            }
                        } catch (e) {
                            console.error('解析响应数据失败:', e);
                            if (callback && typeof callback === 'function') {
                                callback({
                                    code: -1,
                                    msg: '解析响应数据失败',
                                    data: null
                                });
                            }
                        }
                    } else {
                        console.error('请求失败:', xhr.status, xhr.statusText);
                        if (callback && typeof callback === 'function') {
                            callback({
                                code: -1,
                                msg: '请求失败: ' + xhr.status + ' ' + xhr.statusText,
                                data: null
                            });
                        }
                    }
                }
            };

            xhr.ontimeout = function () {
                console.error('请求超时');
                if (callback && typeof callback === 'function') {
                    callback({
                        code: -1,
                        msg: '请求超时',
                        data: null
                    });
                }
            };

            xhr.onerror = function () {
                console.error('请求发生错误');
                if (callback && typeof callback === 'function') {
                    callback({
                        code: -1,
                        msg: '网络错误',
                        data: null
                    });
                }
            };

            xhr.send();
        }

        // 加载订单详情
        function loadOrderDetail() {
            console.log('加载订单详情，ID:', orderId);

            // 使用正确的API路径
            customAjaxRequest('public/training/order/' + orderId, {}, function (data) {
                console.log('API响应:', data);

                if (data.code == 0 || data.code == 200) {
                    var orderData = data.data;
                    if (orderData) {
                        renderOrderDetail(orderData);
                        loadRecommendedOrders();
                    } else {
                        console.error('订单数据为空');
                        showToast('订单不存在', 'error');
                        loadMockOrderDetail();
                    }
                } else {
                    console.error('获取订单详情失败：', data.msg || data.message);
                    showToast(data.msg || '获取订单详情失败', 'error');
                    // 如果API失败，加载模拟数据用于测试
                    loadMockOrderDetail();
                }
            });
        }

        // 加载推荐订单
        function loadRecommendedOrders() {
            customAjaxRequest('public/training/order/featured', { limit: 3 }, function (data) {
                if (data.code == 0 || data.code == 200) {
                    var orders = data.data || [];
                    renderRecommendedOrders(orders);
                }
            });
        }

        // 模拟订单详情数据
        function loadMockOrderDetail() {
            console.log('加载模拟订单详情数据，ID:', orderId);

            var mockOrders = {
                '1': {
                    orderId: 1,
                    orderTitle: 'Java高级开发工程师培训',
                    orderDescription: `
                        <p>本次Java高级开发工程师培训课程专为有一定Java基础的开发人员设计，旨在提升学员的高级编程技能和项目实战能力。</p>
                        <p><strong>课程目标：</strong></p>
                        <ul>
                            <li>掌握Spring Boot高级特性和最佳实践</li>
                            <li>理解微服务架构的设计原理和实现方法</li>
                            <li>具备高并发系统的设计和优化能力</li>
                            <li>提升代码质量和项目管理能力</li>
                        </ul>
                        <p><strong>课程内容：</strong></p>
                        <ul>
                            <li>Spring Boot高级特性与微服务架构</li>
                            <li>分布式系统设计与实现</li>
                            <li>高并发编程与性能优化</li>
                            <li>数据库设计与优化</li>
                            <li>项目实战与代码规范</li>
                        </ul>
                    `,
                    trainingType: '技术培训',
                    trainingCategory: 'IT技术',
                    trainingLevel: '高级',
                    trainingDuration: 120,
                    maxParticipants: 30,
                    currentParticipants: 15,
                    trainingFee: 5800.00,
                    trainingAddress: '青岛市市南区香港中路10号青岛软件园',
                    contactPerson: '张老师',
                    contactPhone: '0532-12345678',
                    contactEmail: '<EMAIL>',
                    startDate: '2025-08-01 09:00:00',
                    endDate: '2025-08-15 18:00:00',
                    registrationDeadline: '2025-07-25 23:59:59',
                    orderStatus: '1',
                    isFeatured: '1',
                    requirements: `
                        <ul>
                            <li>具备2年以上Java开发经验</li>
                            <li>熟悉Spring框架基础知识</li>
                            <li>了解基本的数据库操作</li>
                            <li>有团队协作经验优先</li>
                        </ul>
                    `,
                    certificateInfo: `
                        <p>培训结束后，通过考核的学员将获得：</p>
                        <ul>
                            <li>青岛市人力资源和社会保障局颁发的培训结业证书</li>
                            <li>Java高级开发工程师技能认证证书</li>
                            <li>优秀学员推荐就业机会</li>
                        </ul>
                    `
                },
                '2': {
                    orderId: 2,
                    orderTitle: 'Python数据分析师培训',
                    orderDescription: `
                        <p>Python数据分析师培训课程面向数据分析初学者，帮助学员掌握Python数据分析的核心技能。</p>
                        <p><strong>适合人群：</strong></p>
                        <ul>
                            <li>对数据分析感兴趣的初学者</li>
                            <li>希望转行数据分析的从业者</li>
                            <li>需要数据分析技能的业务人员</li>
                        </ul>
                        <p><strong>课程内容：</strong></p>
                        <ul>
                            <li>Python基础语法与数据结构</li>
                            <li>NumPy与Pandas数据处理</li>
                            <li>Matplotlib与Seaborn数据可视化</li>
                            <li>统计分析与机器学习入门</li>
                            <li>实际项目案例分析</li>
                        </ul>
                    `,
                    trainingType: '技术培训',
                    trainingCategory: '数据分析',
                    trainingLevel: '初级',
                    trainingDuration: 80,
                    maxParticipants: 25,
                    currentParticipants: 8,
                    trainingFee: 3800.00,
                    trainingAddress: '青岛市崂山区海尔路178号创新大厦',
                    contactPerson: '李老师',
                    contactPhone: '0532-87654321',
                    contactEmail: '<EMAIL>',
                    startDate: '2025-08-05 09:00:00',
                    endDate: '2025-08-19 18:00:00',
                    registrationDeadline: '2025-07-30 23:59:59',
                    orderStatus: '1',
                    isFeatured: '1',
                    requirements: `
                        <ul>
                            <li>具备基本的计算机操作能力</li>
                            <li>有一定的数学基础</li>
                            <li>对数据分析有浓厚兴趣</li>
                            <li>有编程基础者优先</li>
                        </ul>
                    `,
                    certificateInfo: `
                        <p>培训结束后将获得：</p>
                        <ul>
                            <li>Python数据分析师认证证书</li>
                            <li>项目实战经验证明</li>
                            <li>就业推荐服务</li>
                        </ul>
                    `
                },
                '3': {
                    orderId: 3,
                    orderTitle: '企业管理与领导力提升',
                    trainingType: '管理培训',
                    trainingLevel: '中级',
                    trainingDuration: 40,
                    maxParticipants: 20,
                    currentParticipants: 12,
                    trainingFee: 4500.00,
                    startDate: '2025-07-30 09:00:00',
                    endDate: '2025-08-03 18:00:00',
                    trainingAddress: '青岛市市北区辽宁路263号',
                    orderStatus: '2',
                    contactPerson: '王老师',
                    contactPhone: '0532-11223344',
                    description: `
                        <p>企业管理与领导力提升培训专为中层管理人员设计，提升管理技能和领导力。</p>
                        <p>课程内容包括：</p>
                        <ul>
                            <li>现代企业管理理论与实践</li>
                            <li>团队建设与沟通技巧</li>
                            <li>决策分析与问题解决</li>
                            <li>变革管理与创新思维</li>
                            <li>领导力发展与实战演练</li>
                        </ul>
                    `
                },
                '4': {
                    orderId: 4,
                    orderTitle: '财务分析与预算管理',
                    trainingType: '管理培训',
                    trainingLevel: '中级',
                    trainingDuration: 50,
                    maxParticipants: 25,
                    currentParticipants: 15,
                    trainingFee: 3500.00,
                    startDate: '2025-08-18 09:00:00',
                    endDate: '2025-08-25 18:00:00',
                    trainingAddress: '青岛市市南区山东路9号',
                    orderStatus: '1',
                    contactPerson: '赵老师',
                    contactPhone: '0532-55667788',
                    description: `
                        <p>财务分析与预算管理培训帮助财务人员提升专业技能，掌握现代财务管理方法。</p>
                        <p>课程内容包括：</p>
                        <ul>
                            <li>财务报表分析与解读</li>
                            <li>预算编制与执行控制</li>
                            <li>成本管理与绩效评估</li>
                            <li>投资决策与风险管理</li>
                            <li>财务软件应用实操</li>
                        </ul>
                    `
                }
            };

            var mockOrder = mockOrders[orderId] || mockOrders['1'];
            renderOrderDetail(mockOrder);
        }

        // 显示提示消息
        function showToast(message, type) {
            var toastClass = type === 'success' ? 'toast-success' : (type === 'error' ? 'toast-error' : 'toast-info');
            var toast = document.createElement('div');
            toast.className = 'toast ' + toastClass;
            toast.textContent = message;

            // 添加样式
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-size: 14px;
                z-index: 10000;
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;

            if (type === 'success') {
                toast.style.backgroundColor = '#52c41a';
            } else if (type === 'error') {
                toast.style.backgroundColor = '#ff4d4f';
            } else {
                toast.style.backgroundColor = '#1890ff';
            }

            document.body.appendChild(toast);

            setTimeout(function () {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 100);

            setTimeout(function () {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(function () {
                    if (toast.parentNode) {
                        document.body.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 初始化页面
        function initPage() {
            console.log('开始初始化培训订单详情页面...');
            console.log('订单ID:', orderId);

            // 加载订单详情
            loadOrderDetail();

            // 启动倒计时定时器
            setInterval(function () {
                if (orderDetail.registrationDeadline) {
                    updateCountdown();
                }
            }, 60000); // 每分钟更新一次
        }

        // 确保在所有资源加载完成后初始化
        window.addEventListener('load', function () {
            console.log('Window load event triggered');
            // 公用模块html
            if (typeof headerBar === 'function') {
            }
            if (typeof footerBar === 'function') {
                footerBar();
            }

            // 初始化页面
            initPage();
        });

    </script>
</body>

</html>