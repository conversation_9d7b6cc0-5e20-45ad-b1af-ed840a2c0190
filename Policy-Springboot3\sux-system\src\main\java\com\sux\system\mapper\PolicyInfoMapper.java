package com.sux.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.sux.system.domain.PolicyInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 政策信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-21
 */
@Mapper
public interface PolicyInfoMapper extends BaseMapper<PolicyInfo>
{
    /**
     * 查询政策信息列表
     * 
     * @param policyInfo 政策信息
     * @return 政策信息集合
     */
    public List<PolicyInfo> selectPolicyInfoList(PolicyInfo policyInfo);

    /**
     * 查询政策信息
     * 
     * @param policyId 政策信息主键
     * @return 政策信息
     */
    public PolicyInfo selectPolicyInfoByPolicyId(Long policyId);

    /**
     * 新增政策信息
     * 
     * @param policyInfo 政策信息
     * @return 结果
     */
    public int insertPolicyInfo(PolicyInfo policyInfo);

    /**
     * 修改政策信息
     * 
     * @param policyInfo 政策信息
     * @return 结果
     */
    public int updatePolicyInfo(PolicyInfo policyInfo);

    /**
     * 删除政策信息
     * 
     * @param policyId 政策信息主键
     * @return 结果
     */
    public int deletePolicyInfoByPolicyId(Long policyId);

    /**
     * 批量删除政策信息
     * 
     * @param policyIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePolicyInfoByPolicyIds(Long[] policyIds);

    /**
     * 校验政策名称是否唯一
     * 
     * @param policyName 政策名称
     * @return 结果
     */
    public PolicyInfo checkPolicyNameUnique(String policyName);
}
