<!DOCTYPE html>
<html>

<head>
    <meta name="keywords" content="青创通 · 青岛市创业服务云平台">
    <meta name="description" content="">
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">
    <meta http-equiv="Content-Type"
        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />
    <title>找活动-青创通 · 青岛市创业服务云平台</title>
    <!-- base css -->
    <link rel="stylesheet" type="text/css" href="../public/css/zh.min.css" />
    <!--分页 css-->
    <link rel="stylesheet" type="text/css" href="../public/plugins/pagination/pagination.css" />
    <!-- swiper css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/swiper/swiper.min.css" />
    <!-- jbox css -->
    <link rel="stylesheet" type="text/css" href="../public/plugins/jbox/Skins/Blue/jbox.css" />
    <!-- common css -->
    <link rel="stylesheet" type="text/css" href="../public/css/common.css" />
    <!-- this page css -->
    <link rel="stylesheet" type="text/css" href="css/index.css?v=202505271645" />
    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />
</head>

<body id="viewModelBox">
    <div id="headerBar"></div>
    <!-- main start -->
    <!-- banner -->
    <div class="bannerBox pr">
        <!-- ko if:bagnnerList().length>0 -->
        <div class="bannerSlide" data-bind="foreach:bagnnerList">
            <img src="./image/new_zhdBanner.jpg" data-bind="attr:{src:fullPath}">
        </div>
        <div class="hd" data-bind="visible:bagnnerList().length>1">
            <ul></ul>
        </div>
        <!-- /ko -->
        <!-- ko if:bagnnerList().length==0 -->
        <div class="bannerSlide">
            <img src="./image/new_zhdBanner.jpg">
        </div>
        <!-- /ko -->
    </div>
    <div class="pageBg">
        <div class="conAuto2">
            <div class="clearfix">
                <!-- 活动动态 -->
                <div class="fl" style="width: 685px;">
                    <div class="clearfix mt30">
                        <div class="fl">
                            <p class="contentTitle">活动<em>动态</em></p>
                        </div>
                        <a href="./activityList.html" class="moreBtn block fr transi mt20">
                            <span class="inlineblock text-white">更多</span>
                        </a>
                    </div>
                    <div class="cyhdOut">
                        <ul class="clearfix activityList" data-bind="foreach:activityList">
                            <li class="pr fl transi">
                                <a href="javascript:;" data-bind="click:$root.goInfo">
                                    <em class="pa arrs"
                                        data-bind="css:{spanBox0:status=='未开始',spanBox1:status=='进行中',spanBox2:status=='已结束'}"></em>
                                    <p class="baseName transi paraoverflow2"
                                        data-bind="text:baseName,attr:{title:baseName}"></p>
                                    <p class="textoverflow time">活动时间：<span
                                            data-bind="text:activityFromTime&&activityEndTime?(activityFromTime?activityFromTime:'--')+' 至 '+(activityEndTime?activityEndTime:'--'):'--'"></span>
                                    </p>
                                    <p class="textoverflow address">活动地址：<span data-bind="text:province"></span><span
                                            data-bind="text:city"></span><span
                                            data-bind="text:districtName"></span><span data-bind="text:address"></span>
                                    </p>
                                    <div class="clearfix type">
                                        <span class="fl"
                                            data-bind="text:activityTheme?activityTheme:'--',visible:activityTheme"></span>
                                        <p href="javascript:;" class="fr"><span>立即查看</span></p>
                                    </div>
                                </a>
                            </li>
                        </ul>
                        <div class="nodataPic nodataPic2 none" style="margin-top: 50px;"></div>
                    </div>
                </div>
                <!-- 活动回顾 -->
                <div class="rightBox fr">
                    <p class="title mb20">活动回顾</p>
                    <div class="pr hdhg">
                        <div class="videoBox clearfix" data-bind="foreach:attachmentList">
                            <a class="fl transi" target="_blank"
                                data-bind="attr:{href:'./activityBackDetail.html?id='+baseId}">
                                <div>
                                    <img data-bind="attr:{src:fullPath}">
                                </div>
                            </a>
                        </div>
                        <div class="hd dtHd">
                            <a href="javascript:;" class="sPrev"></a>
                            <a href="javascript:;" class="sNext"></a>
                            <ul class="clearfix" data-bind="foreach:attachmentList">
                                <li class="transi fl" >
                                    <img data-bind="attr:{src:fullPath}">
                                </li>
                            </ul>
                        </div>
                        <div class="listBox scroll-bar">
                            <ul class="yearList" data-bind="foreach:yearList">
                                <li>
                                    <p class="years" data-bind="text:baseName+'活动',click:function(event){$root.selectData('1',event)}"></p>
                                    <div class="monthList none" data-bind="foreach:$root.monthList">
                                        <div class="li">
                                            <p class="months" data-bind="text:baseName+'活动',click:function(event){$root.selectData('2',event)}"></p>
                                            <!-- ko if:$root.hdList().length>0 -->
                                            <div class="hdList none" data-bind="foreach:$root.hdList">
                                                <p class="hds" data-bind="text:baseName,click:function(event){$root.selectData('3',event)}"></p>
                                            </div>
                                            <!-- /ko -->
                                            <!-- ko if:$root.hdList().length==0 -->
                                            <div class="hdList none">
                                                <p class="hds tc" style="height: 30px;line-height: 30px;">暂无活动</p>
                                            </div>
                                            <!-- /ko -->
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- main end -->
    <!-- 底部 开始 -->
    <div id="footerBar"> </div>
    <!-- 底部 开始 -->
    <!--jquery js-->
    <script src="../public/js/jquery-3.5.0.min.js" type="text/javascript" charset="utf-8"></script>
    <!--jquery js-->
    <script src="../public/plugins/superSlide/jquery.SuperSlide.2.1.3.js" type="text/javascript" charset="utf-8">
    </script>
    <!--分页 js-->
    <script type="text/javascript" src="../public/plugins/pagination/jquery.pagination.js"></script>
    <script src="../public/plugins/swiper/swiper.min5.js" type="text/javascript" charset="utf-8"></script>
    <!--common js-->
    <!-- <script src="../public/js/api.js"></script> -->
    <script src="../public/js/knockout.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/mapping.js" type="text/javascript" charset="utf-8"></script>
    <script type="text/javascript" src="../public/plugins/jbox/jquery.jBox.js"></script>
    <script src="../public/js/utils.js" type="text/javascript" charset="utf-8"></script>
    <script src="../public/js/common.js" type="text/javascript" charset="utf-8"></script>
    <!--this page js  -->
    <script src="js/indexs.js?v=202505280848" type="text/javascript" charset="utf-8"></script>
</body>

</html>