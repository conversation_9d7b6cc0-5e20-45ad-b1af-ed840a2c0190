package com.sux.web.controller.training;

import com.sux.common.annotation.Anonymous;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.system.domain.TrainingOrder;
import com.sux.system.service.ITrainingOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 培训订单公开API Controller（无需登录）
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Anonymous
@RestController
@RequestMapping("/public/training/order")
public class PublicTrainingOrderController extends BaseController {
    @Autowired
    private ITrainingOrderService trainingOrderService;

    /**
     * 查询培训订单列表（公开接口，无需登录）
     * 只返回已发布状态的订单
     */
    @GetMapping("/list")
    public TableDataInfo list(TrainingOrder trainingOrder) {
        // 强制设置为只查询已发布的订单
        trainingOrder.setOrderStatus("1");

        startPage();
        List<TrainingOrder> list = trainingOrderService.selectTrainingOrderList(trainingOrder);
        return getDataTable(list);
    }

    /**
     * 获取培训订单详细信息（公开接口，无需登录）
     * 只返回已发布状态的订单详情
     */
    @GetMapping(value = "/{orderId}")
    public AjaxResult getInfo(@PathVariable("orderId") Long orderId) {
        TrainingOrder order = trainingOrderService.selectTrainingOrderByOrderId(orderId);

        // 检查订单是否存在且为已发布状态
        if (order == null) {
            return error("培训订单不存在");
        }

        if (!"1".equals(order.getOrderStatus())) {
            return error("培训订单未发布或已下线");
        }

        return success(order);
    }

    /**
     * 获取推荐培训订单列表（公开接口，无需登录）
     * 返回推荐的已发布订单
     */
    @GetMapping("/featured")
    public AjaxResult getFeaturedOrders(@RequestParam(defaultValue = "4") int limit) {
        TrainingOrder queryOrder = new TrainingOrder();
        queryOrder.setOrderStatus("1"); // 已发布
        queryOrder.setIsFeatured("1");  // 推荐

        // 设置分页参数
        startPage();
        List<TrainingOrder> list = trainingOrderService.selectTrainingOrderList(queryOrder);

        return success(list);
    }

    /**
     * 获取最新培训订单列表（公开接口，无需登录）
     * 返回最新的已发布订单
     */
    @GetMapping("/latest")
    public AjaxResult getLatestOrders(@RequestParam(defaultValue = "6") int limit) {
        TrainingOrder queryOrder = new TrainingOrder();
        queryOrder.setOrderStatus("1"); // 已发布

        // 设置分页参数
        startPage();
        List<TrainingOrder> list = trainingOrderService.selectTrainingOrderList(queryOrder);

        return success(list);
    }

    /**
     * 获取首页培训订单列表（公开接口，无需登录）
     * 返回最新的5条已发布订单，按创建时间倒序
     */
    @GetMapping("/homepage")
    public AjaxResult getHomepageOrders() {
        TrainingOrder queryOrder = new TrainingOrder();
        queryOrder.setOrderStatus("1"); // 已发布

        // 设置分页参数，只返回5条数据
        startPage();
        List<TrainingOrder> list = trainingOrderService.selectTrainingOrderList(queryOrder);

        return success(list);
    }

    /**
     * 根据培训类型获取订单列表（公开接口，无需登录）
     */
    @GetMapping("/by-type/{trainingType}")
    public AjaxResult getOrdersByType(@PathVariable String trainingType,
                                      @RequestParam(defaultValue = "10") int limit) {
        TrainingOrder queryOrder = new TrainingOrder();
        queryOrder.setOrderStatus("1"); // 已发布
        queryOrder.setTrainingType(trainingType);

        // 设置分页参数
        startPage();
        List<TrainingOrder> list = trainingOrderService.selectTrainingOrderList(queryOrder);

        return success(list);
    }

    /**
     * 搜索培训订单（公开接口，无需登录）
     */
    @GetMapping("/search")
    public TableDataInfo searchOrders(@RequestParam(required = false) String keyword,
                                      @RequestParam(required = false) String trainingType,
                                      @RequestParam(required = false) String trainingLevel,
                                      @RequestParam(required = false) String trainingCategory) {
        TrainingOrder queryOrder = new TrainingOrder();
        queryOrder.setOrderStatus("1"); // 只查询已发布的订单

        // 设置搜索条件
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryOrder.setOrderTitle(keyword.trim());
        }
        if (trainingType != null && !trainingType.trim().isEmpty()) {
            queryOrder.setTrainingType(trainingType.trim());
        }
        if (trainingLevel != null && !trainingLevel.trim().isEmpty()) {
            queryOrder.setTrainingLevel(trainingLevel.trim());
        }
        if (trainingCategory != null && !trainingCategory.trim().isEmpty()) {
            queryOrder.setTrainingCategory(trainingCategory.trim());
        }

        startPage();
        List<TrainingOrder> list = trainingOrderService.selectTrainingOrderList(queryOrder);
        return getDataTable(list);
    }
}
