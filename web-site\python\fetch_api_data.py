import urllib.request
import urllib.parse
import json
import os
import ssl
from pathlib import Path

# 忽略SSL证书验证
ssl._create_default_https_context = ssl._create_unverified_context

def fetch_api_data(base_url, endpoint, params=None, method='GET', data=None):
    """获取API数据"""
    try:
        url = urllib.parse.urljoin(base_url, endpoint)
        
        if method == 'GET' and params:
            query_string = urllib.parse.urlencode(params)
            url = f"{url}?{query_string}"
        
        print(f"正在获取: {url}")
        
        # 创建请求
        if method == 'POST' and data:
            if isinstance(data, dict):
                data = json.dumps(data).encode('utf-8')
            elif isinstance(data, str):
                data = data.encode('utf-8')
            
            req = urllib.request.Request(url, data=data)
            req.add_header('Content-Type', 'application/json')
        else:
            req = urllib.request.Request(url)
        
        # 添加请求头
        req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        req.add_header('Accept', 'application/json, text/plain, */*')
        req.add_header('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8')
        req.add_header('Referer', 'https://hrss.qingdao.gov.cn/')

        # 发送请求
        with urllib.request.urlopen(req, timeout=30) as response:
            content = response.read().decode('utf-8')
            return json.loads(content)
            
    except Exception as e:
        print(f"获取数据失败 {url}: {e}")
        return None

def main():
    base_url = 'https://hrss.qingdao.gov.cn/'
    output_dir = "api_data"
    Path(output_dir).mkdir(exist_ok=True)
    
    # 定义所有需要获取的API
    apis = [
        # 地域数据
        {
            'name': 'regions',
            'endpoint': 'api-manage/v2/front/sysArea/sysAreaAndChildsByIp',
            'params': {'cityId': '63E8F55B-0879-4C0C-B177-61EEED393A95'},
            'method': 'GET'
        },
        # 政策类型
        {
            'name': 'policy_types',
            'endpoint': 'api-manage/v2/front/sysDictData/sysDictDataList',
            'params': {'typeId': '2a2acbab-0b5e-11ee-9603-0050569e68c8'},
            'method': 'GET'
        },
        # 政策支持
        {
            'name': 'support_modes',
            'endpoint': 'api-manage/v2/front/sysDictData/sysDictDataList',
            'params': {'typeId': '7892e4eb-0bde-11ee-9603-0050569e68c8'},
            'method': 'GET'
        },
        # Banner数据
        {
            'name': 'banners',
            'endpoint': 'api-qingdao/v1/sysBannerManager/getFrontList',
            'params': {'pageNum': 1, 'pageSize': 1, 'bannerModule': 1},
            'method': 'GET'
        },
        # 政策申报列表
        {
            'name': 'calendar_list',
            'endpoint': 'api-manage/v2/front/subject/dSubjectInfoListPage',
            'params': {
                'pageSize': 12,
                'pageNum': 1,
                'sortOrder': 1,
                'sortField': 1,
                'currentCityId': '63E8F55B-0879-4C0C-B177-61EEED393A95'
            },
            'method': 'GET'
        },
        # 政策文件列表
        {
            'name': 'file_list',
            'endpoint': 'api-manage/v2/front/zctPolicy/policyListPage',
            'params': {
                'pageSize': 12,
                'pageNum': 1,
                'type': 'D4F6C59F-2C9F-4A7E-8F57-1BD696F5C010',
                'thisLevel': False,
                'currentCityId': '63E8F55B-0879-4C0C-B177-61EEED393A95'
            },
            'method': 'GET'
        },
        # 政策专区列表
        {
            'name': 'column_list',
            'endpoint': 'api-policy/v1/policyAreaPunishApp/allPolicyAreaList',
            'data': {
                'pageSize': 6,
                'pageNum': 1,
                'areaType': "1"
            },
            'method': 'POST'
        },
        # 政策解读列表
        {
            'name': 'read_list',
            'endpoint': 'api-manage/v2/front/zctRead/zctReadListPage',
            'params': {
                'pageSize': 2,
                'pageNum': 1,
                'thisLevel': False,
                'currentCityId': '63E8F55B-0879-4C0C-B177-61EEED393A95'
            },
            'method': 'GET'
        },
        # 政策短视频列表
        {
            'name': 'video_list',
            'endpoint': 'api-policy/v1/frontPolicyVideo/policyVideoListPage',
            'data': {
                'pageSize': 3,
                'pageNum': 1,
                'isHot': 0
            },
            'method': 'POST'
        }
    ]
    
    # 获取所有API数据
    all_data = {}
    
    for api in apis:
        print(f"\n正在获取 {api['name']} 数据...")
        
        if api['method'] == 'GET':
            data = fetch_api_data(
                base_url, 
                api['endpoint'], 
                params=api.get('params'),
                method='GET'
            )
        else:
            data = fetch_api_data(
                base_url, 
                api['endpoint'], 
                method='POST',
                data=api.get('data')
            )
        
        if data:
            all_data[api['name']] = data
            
            # 保存单个API数据
            filename = os.path.join(output_dir, f"{api['name']}.json")
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"已保存到: {filename}")
        else:
            print(f"获取 {api['name']} 数据失败")
    
    # 保存所有数据到一个文件
    all_data_file = os.path.join(output_dir, "all_data.json")
    with open(all_data_file, 'w', encoding='utf-8') as f:
        json.dump(all_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n所有数据已保存到: {output_dir}")
    print("数据获取完成！")

if __name__ == "__main__":
    main()
