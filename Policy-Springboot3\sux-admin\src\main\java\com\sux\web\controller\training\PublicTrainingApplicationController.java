package com.sux.web.controller.training;

import com.sux.common.annotation.Anonymous;
import com.sux.common.annotation.Log;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.enums.BusinessType;
import com.sux.common.utils.SecurityUtils;
import com.sux.system.domain.TrainingApplication;
import com.sux.system.domain.TrainingOrder;
import com.sux.system.service.ITrainingApplicationService;
import com.sux.system.service.ITrainingOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 培训报名公开API Controller（无需登录）
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@Anonymous
@RestController
@RequestMapping("/public/training/application")
public class PublicTrainingApplicationController extends BaseController {
    @Autowired
    private ITrainingApplicationService trainingApplicationService;

    @Autowired
    private ITrainingOrderService trainingOrderService;

    /**
     * 提交培训报名申请（公开接口，无需登录）
     */
    @Log(title = "培训报名", businessType = BusinessType.INSERT)
    @PostMapping("/submit")
    public AjaxResult submitApplication(@Validated @RequestBody TrainingApplication trainingApplication) {
        // 验证培训订单是否存在且可报名
        TrainingOrder order = trainingOrderService.selectTrainingOrderByOrderId(trainingApplication.getOrderId());
        if (order == null) {
            return error("培训订单不存在");
        }

        if (!"1".equals(order.getOrderStatus())) {
            return error("培训订单未发布或已下线，无法报名");
        }

        // 检查报名截止时间
        if (order.getRegistrationDeadline() != null && new Date().after(order.getRegistrationDeadline())) {
            return error("报名已截止");
        }

        // 检查报名人数是否已满
        int currentCount = trainingApplicationService.countApplicationsByOrderId(trainingApplication.getOrderId());
        if (order.getMaxParticipants() != null && currentCount >= order.getMaxParticipants()) {
            return error("报名人数已满");
        }

        // 检查报名唯一性
        if (!trainingApplicationService.checkApplicationUnique(trainingApplication)) {
            return error("您已报名此培训，请勿重复报名");
        }

        // 提交报名申请
        int result = trainingApplicationService.submitApplication(trainingApplication);
        if (result > 0) {
            return success("报名成功，请等待审核");
        } else {
            return error("报名失败，请稍后重试");
        }
    }

    /**
     * 检查用户报名状态（公开接口，无需登录）
     */
    @GetMapping("/check-status")
    public AjaxResult checkApplicationStatus(@RequestParam Long orderId,
                                             @RequestParam(required = false) String phone,
                                             @RequestParam(required = false) Long userId) {
        TrainingApplication application = null;

        // 优先通过用户ID查询
        if (userId != null) {
            application = trainingApplicationService.checkUserApplication(orderId, userId);
        }

        // 如果没有找到且提供了手机号，通过手机号查询
        if (application == null && phone != null && !phone.trim().isEmpty()) {
            application = trainingApplicationService.checkPhoneApplication(orderId, phone.trim());
        }

        if (application != null) {
            return success(application);
        } else {
            return success(null);
        }
    }

    /**
     * 获取当前登录用户的报名状态（需要登录）
     */
    @GetMapping("/my-status/{orderId}")
    public AjaxResult getMyApplicationStatus(@PathVariable Long orderId) {
        try {
            Long userId = SecurityUtils.getUserId();
            TrainingApplication application = trainingApplicationService.checkUserApplication(orderId, userId);

            if (application != null) {
                return success(application);
            } else {
                return success(null);
            }
        } catch (Exception e) {
            return error("请先登录");
        }
    }

    /**
     * 获取当前登录用户的所有报名记录（需要登录）
     */
    @GetMapping("/my-applications")
    public AjaxResult getMyApplications() {
        try {
            Long userId = SecurityUtils.getUserId();
            TrainingApplication queryApplication = new TrainingApplication();
            queryApplication.setUserId(userId);

            List<TrainingApplication> list = trainingApplicationService.selectTrainingApplicationList(queryApplication);
            return success(list);
        } catch (Exception e) {
            return error("请先登录");
        }
    }

    /**
     * 取消报名（需要登录）
     */
    @Log(title = "培训报名", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{applicationId}")
    public AjaxResult cancelMyApplication(@PathVariable Long applicationId) {
        try {
            Long userId = SecurityUtils.getUserId();

            // 验证报名记录是否属于当前用户
            TrainingApplication application = trainingApplicationService.selectTrainingApplicationByApplicationId(applicationId);
            if (application == null) {
                return error("报名记录不存在");
            }

            if (!userId.equals(application.getUserId())) {
                return error("无权操作此报名记录");
            }

            // 检查是否可以取消
            if ("3".equals(application.getApplicationStatus())) {
                return error("报名已取消");
            }

            if ("1".equals(application.getApplicationStatus())) {
                // 检查培训是否已开始
                TrainingOrder order = trainingOrderService.selectTrainingOrderByOrderId(application.getOrderId());
                if (order != null && order.getStartDate() != null && new Date().after(order.getStartDate())) {
                    return error("培训已开始，无法取消报名");
                }
            }

            int result = trainingApplicationService.cancelApplication(applicationId);
            if (result > 0) {
                return success("取消报名成功");
            } else {
                return error("取消报名失败");
            }
        } catch (Exception e) {
            return error("请先登录");
        }
    }

    /**
     * 获取培训订单的报名统计信息（公开接口，无需登录）
     */
    @GetMapping("/statistics/{orderId}")
    public AjaxResult getApplicationStatistics(@PathVariable Long orderId) {
        int totalCount = trainingApplicationService.countApplicationsByOrderId(orderId);

        // 统计各状态的报名数量
        TrainingApplication queryApplication = new TrainingApplication();
        queryApplication.setOrderId(orderId);
        List<TrainingApplication> allApplications = trainingApplicationService.selectTrainingApplicationList(queryApplication);

        long pendingCount = allApplications.stream().filter(app -> "0".equals(app.getApplicationStatus())).count();
        long approvedCount = allApplications.stream().filter(app -> "1".equals(app.getApplicationStatus())).count();
        long rejectedCount = allApplications.stream().filter(app -> "2".equals(app.getApplicationStatus())).count();
        long cancelledCount = allApplications.stream().filter(app -> "3".equals(app.getApplicationStatus())).count();

        return success()
                .put("totalCount", totalCount)
                .put("pendingCount", pendingCount)
                .put("approvedCount", approvedCount)
                .put("rejectedCount", rejectedCount)
                .put("cancelledCount", cancelledCount);
    }
}
