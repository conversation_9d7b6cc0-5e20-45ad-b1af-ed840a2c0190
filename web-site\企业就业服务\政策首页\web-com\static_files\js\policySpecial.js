
// 公用模块html
headerBar()
footerBar()

let cityArr = []
let ztArr = []
let hyArr = []

var openIndex = getQueryString('open')
if (openIndex == 1) {
    alertShow('.cxPopBox')
}
function clearsx(){
    localStorage.removeItem('zcplace');
    localStorage.removeItem('zcprovince');
    localStorage.removeItem('zccity');
    localStorage.removeItem('zcdistrict');
    localStorage.removeItem('zcpolicyType');
    localStorage.removeItem('zcsupportmode');
}
clearsx()
var viewModel = {
    regionList: ko.observableArray(),//地域
    positionName: ko.observable('全部'),
    position: ko.observable(),
    place: ko.observable(),
    province: ko.observable(),
    city: ko.observable(),
    district: ko.observable(),
    typeList: ko.observableArray(),//政策类型
    typeName: ko.observable('全部'),
    policyType: ko.observable(),
    supportmodeList: ko.observableArray(),//政策支持
    supportmodeName: ko.observable('全部'),
    supportmode: ko.observable(),
    selectData: function (data, data2) { //筛选项
        changeSelectStyle(data,data2)
        switch (data) {
            case '0':
                viewModel.positionName(data2.baseName)
                viewModel.position(data2.baseId)
                viewModel.place('')
                viewModel.province('')
                viewModel.city('')
                viewModel.district('')
                if(data.baseName=='全部'){
                
                }else if(data2.baseName=='中央'){
                    viewModel.place(data2.baseId)
                    localStorage.setItem('zcplace',data2.baseId)
                }else if(data2.baseName=='山东省'){
                    viewModel.province(data2.baseId)
                    localStorage.setItem('zcprovince',data2.baseId)
                }else if(data2.baseName=='青岛市'){
                    viewModel.city(data2.baseId)
                    localStorage.setItem('zccity',data2.baseId)
                }else{
                    viewModel.district(data2.baseId)
                    localStorage.setItem('zcdistrict',data2.baseId)
                }
                break;
            case '1':
                viewModel.policyType(data2.baseId)
                localStorage.setItem('zcpolicyType',data2.baseId)
                viewModel.typeName(data2.baseName)
                break;
            case '2':
                viewModel.supportmode(data2.baseId)
                localStorage.setItem('zcsupportmode',data2.baseId)
                viewModel.supportmodeName(data2.baseName)
                break;
        }

        fileListFun()
        calendarListFun()
        readListFun()
    },
    calendarList: ko.observableArray(),//政策申报列表
    fileList: ko.observableArray(),//政策文件列表
    columnList: ko.observableArray(),//政策专区列表
    readList: ko.observableArray(),//政策解读列表
    dspList: ko.observableArray(),//短视频列表
    reportLinkBtn: function (data) {//立即申报按钮
        if (data.reportLink) {
            window.open(data.reportLink)
        } else {
            $.jBox.tip("该项目可能需要线下申报，请在详情页查看具体申报信息");
        }
    },
    bagnnerList: ko.observableArray(),
    financeMeasureList: ko.observableArray(),
    jlcs: function (data) {
        viewModel.financeMeasureList(data.financeMeasureList)
        alertShow('.alertBg')
    },
    cybtLink:function(data){
        if(localStorage.getItem('ticket')&&localStorage.getItem('ticket').length>7){
            initNum('创业补贴申请')
            window.open('https://tysfrz.isdapp.shandong.gov.cn/jpaas-jis-sso-server/sso/entrance/auth-center?appMark=QDRLIZHYUIWANSDD&backUrl=https://12333.qingdao.gov.cn/wssbggjy3/directIndex.action?url=/work/f30020202/index.action&menuId=2212')
        }else{
            initNum('创业补贴申请')
            window.open('http://hrsswb.qingdao.gov.cn:81/cas-server/gopage/index?urlCode=02JYFW026&type=-92')
        }
    },
    cydbLink:function(data){
        if(localStorage.getItem('ticket')&&localStorage.getItem('ticket').length>7){
            initNum('创业担保贷款申请')
            window.open('https://tysfrz.isdapp.shandong.gov.cn/jpaas-jis-sso-server/sso/entrance/auth-center?appMark=QDRLIZHYUIWANSDD&backUrl=https://12333.qingdao.gov.cn/wssbggjy3/directIndex.action?url=/work/f30021001/index.action&menuId=2183')
        }else{
            initNum('创业担保贷款申请')
            if(viewModel.mine().userType=='1'){
                window.open('http://hrsswb.qingdao.gov.cn:81/cas-server/gopage/index?urlCode=03JYFW112&type=-92&source=3')
            }else{
                window.open('http://hrsswb.qingdao.gov.cn:81/cas-server/gopage/index?urlCode=02JYFW025')
            }
            
        }
    },
    cbqyLink:function(data){
        if(localStorage.getItem('ticket')&&localStorage.getItem('ticket').length>7){
            initNum('创办企业、变更备案、企业注销')
            window.open('https://tysfrz.isdapp.shandong.gov.cn/jpaas-jis-sso-server/sso/entrance/auth-center?appMark=QDQYKBZHINENGYITH&backUrl=https://qydjfw.qingdao.gov.cn/qykb.dhtml')
        }else{
            initNum('创办企业、变更备案、企业注销')
            window.open('https://qydjfw.qingdao.gov.cn/qykb.dhtml')
        }
    },
}
Object.assign(viewModel, viewModel1);
function initNum(type){
    var objs={
        type:type,
        userName:viewModel.mine().nickname
    }
    ajaxPostDataFull_token('api-service/v1/officeLog',JSON.stringify(objs))
}
// 更多列表
function moreList(e){
    if(e==1){
        if(viewModel.fileList().length>0){
            window.location.href='./fileList.html?index=1'
        }else{
            clearsx()
            window.location.href='./fileList.html'
        }
    }else if(e==2){
        if(viewModel.calendarList().length>0){
            window.location.href='./calendar.html?index=1'
        }else{
            clearsx()
            window.location.href='./calendar.html'
        }
    }else if(e==3){
        if(viewModel.readList().length>0){
            window.location.href='./readList.html?index=1'
        }else{
            clearsx()
            window.location.href='./readList.html'
        }
    }
}
// 初始化地域
initregion() 
function initregion() {
    ajaxgetDataFull('api-manage/v2/front/sysArea/sysAreaAndChildsByIp?cityId=63E8F55B-0879-4C0C-B177-61EEED393A95')
    if (getData.code == 0) {
        var arr = getData.obj.district
        arr.unshift({
            baseName: getData.obj.city.baseName,
            baseId: getData.obj.city.baseId
        })
        arr.unshift({
            baseName: getData.obj.province.baseName,
            baseId: getData.obj.province.baseId
        })
        arr.unshift({
            baseName: getData.obj.place.baseName,
            baseId: getData.obj.place.baseId
        })
        arr.unshift({
            baseName: '全部',
            baseId: ''
        })
        viewModel.regionList(arr);
    }
}
// 初始化政策类型
initpolicyType() 
function initpolicyType() {
    ajaxgetDataFull('api-manage/v2/front/sysDictData/sysDictDataList?typeId=2a2acbab-0b5e-11ee-9603-0050569e68c8')
    if (getData.code == 0) {
        var arr = getData.obj
        arr.unshift({
            baseName: '全部',
            baseId: ''
        })
        viewModel.typeList(arr);
    }
}
// 初始化政策支持
initsupportmode() 
function initsupportmode() {
    ajaxgetDataFull('api-manage/v2/front/sysDictData/sysDictDataList?typeId=7892e4eb-0bde-11ee-9603-0050569e68c8')
    if (getData.code == 0) {
        var arr = getData.obj
        arr.unshift({
            baseName: '全部',
            baseId: ''
        })
        viewModel.supportmodeList(arr);
    }
}
// 政策匹配
function gozcppPage(e) {
    if (checkLogin()) {
        if (viewModel1.mine().userType == '1') {
            window.location.href = '../policyMatch/policyMatchList.html'
        } else {
            window.location.href = '../policyMatch/policyMatch.html'
        }
    } else {
        alertShow('.loginPop')
    }

}
// banner管理
function initBanner() {
    var obj = {
        pageNum: 1,
        pageSize: 1,
        bannerModule: 1
    }
    ajaxgetDataFull('api-qingdao/v1/sysBannerManager/getFrontList', obj)
    if (getData.code == 0) {
        if (getData.obj.content.length > 0) {
            viewModel.bagnnerList(getData.obj.content[0].pictureFileList)
            viewModel.bagnnerList.unshift({ fullPath: './images/ps_bannerBg.jpg' })
        }
    }
}
// initBanner()
// 初始化地域
zccxinitregion()
// 初始化政策类型
zccxinitpolicyType()
// 初始化政策支持
zccxinitsupportmode()
// 初始化产业分类
zccxinitchain()
// 初始化申报时间
zccxinitstatusLevel()
//  ************ 政策查询 start************
$(document).on('click', '.ksA', function () {
    if ($('.cxPopBox .sxUl li.chooseLi').hasClass('on') || $('.cxPopBox .sxUl li.chooseLis').hasClass('on')) {
        var uuid = guid();
        var obj = {
            place: localStorage.getItem('zccxplace'),
            province: localStorage.getItem('zccxprovince'),
            city: localStorage.getItem('zccxcity'),
            district: localStorage.getItem('zccxdistrict'),
            projectType: localStorage.getItem('zccxpolicyType'),
            method: localStorage.getItem('zccxsupportmode'),
            chain: localStorage.getItem('zccxchain'),
            statusLevel: localStorage.getItem('zccxstatusLevel'),
            uuid: uuid
        };
        var param = JSON.stringify(obj);
        ajaxPostDataFull_token('api-policy/v1/policyMatchApp/policyCalcCach', param);;
        if (PostData.code == 0) {
            window.location.href = "./policyInquiryResult.html?id=" + uuid;
        };
    } else {
        $.jBox.tip("请至少选择一个选项");
    }
    // window.location.href = "./policyInquiryResult.html?id="+123;
})

//跳转判断是否登录
function loginGo(obj) {
    if (checkLogin()) {
        window.open(obj)
    } else {
        $.jBox.tip("请先登录");
        // setTimeout(function () {
        //     //跳转到登录界面
        //     //sessionStorage.setItem('loginUrl', obj);
        //     window.location.href = 'https://tysfrz.isdapp.shandong.gov.cn/jpaas-jis-sso-server/sso/entrance/auth-center?appMark=QDCHUANGYEYPT&userType=2';
        // }, 500);
    }
}
// 政策申报列表
calendarListFun()
function calendarListFun() {
    var obj = {
        pageSize: 12,
        pageNum: 1,
        sortOrder: 1,
        sortField: 1,
        currentCityId: '63E8F55B-0879-4C0C-B177-61EEED393A95',
        place:viewModel.place(),
        province:viewModel.province(),
        city:viewModel.city(),
        district:viewModel.district(),
        projectType:viewModel.policyType(),
        method:viewModel.supportmode()
    };
    ajaxgetData('api-manage/v2/front/subject/dSubjectInfoListPage', obj, function (data) {
        if (data.code == 0) {
            viewModel.calendarList(data.obj.content?data.obj.content:[]);
            setTimeout(function () {
                $(".box2").slide({
                    // titCell: ".hd",
                    mainCell: ".sbUl",
                    autoPage: true,
                    effect: "left",
                    // autoPlay: true,
                    // effect: "leftLoop",
                    vis: 2
                });
            }, 200)
        }
    })
}

// 政策文件列表
fileListFun()
function fileListFun() {
    var obj = {
        pageSize: 12,
        pageNum: 1,
        type: 'D4F6C59F-2C9F-4A7E-8F57-1BD696F5C010',
        thisLevel: false,
        sortOrder: 2,
        sortField: 4,
        currentCityId: '63E8F55B-0879-4C0C-B177-61EEED393A95',
        place:viewModel.place(),
        province:viewModel.province(),
        city:viewModel.city(),
        district:viewModel.district(),
        policyType:viewModel.policyType(),
        supportmode:viewModel.supportmode()
    };
    ajaxgetData('api-manage/v2/front/zctPolicy/policyListPage', obj, function (data) {
        if (data.code == 0) {
            viewModel.fileList(data.obj.content.slice(0, 3));
        }
    })
}

// 政策专区列表
columnListFun()
function columnListFun() {
    var obj = {
        pageSize: 6,
        pageNum: 1,
        areaType: "1",
    };
    obj = JSON.stringify(obj)
    ajaxPostData('api-policy/v1/policyAreaPunishApp/allPolicyAreaList', obj, function (data) {
        if (data.code == 0) {
            viewModel.columnList(data.obj.content);
        }
    })
}

// 政策解读列表
readListFun()
function readListFun() {
    var obj = {
        pageSize: 2,
        pageNum: 1,
        thisLevel: false,
        currentCityId: '63E8F55B-0879-4C0C-B177-61EEED393A95',
        sortOrder: 2,
        sortField: 4,
        place:viewModel.place(),
        province:viewModel.province(),
        city:viewModel.city(),
        district:viewModel.district(),
        supportmode:viewModel.supportmode(),
        policyType:viewModel.policyType(),
    };
    ajaxgetData('api-manage/v2/front/zctRead/zctReadListPage', obj, function (data) {
        if (data.code == 0) {
            viewModel.readList(data.obj.content);
        }
    })
}
// 政策短视频列表
dspListFun()
function dspListFun() {
    var obj = {
        pageSize: 3,
        pageNum: 1,
        isHot:0
    };
    ajaxPostData('api-policy/v1/frontPolicyVideo/policyVideoListPage', JSON.stringify(obj), function (data) {
        if (data.code == 0) {
            viewModel.dspList(data.obj.content);
            setTimeout(function () {
                $(".box4").slide({
                    // titCell: ".hd",
                    mainCell: ".videoBox",
                    autoPage: true,
                    effect: "left",
                    // autoPlay: true,
                    // effect: "leftLoop",
                    vis: 1
                });
            }, 200)
        }
    })
}




ko.applyBindings(viewModel, document.getElementById("viewModelBox"));
$('#policyPage').addClass('on')

$(document).ready(function () {
    $('.box1 .navBtn').hover(function () { // 鼠标移入时触发
        $(this).addClass('on').siblings().removeClass('on'); // 显示元素
    });
});
//多选
$(document).on('click', '.cxPopBox .sxUl li.chooseLi', function () {
    if ($(this).hasClass('on')) {
        $(this).removeClass('on')
    } else {
        $(this).addClass('on').siblings().removeClass('on');
    }
})
$(document).on('click', '.cxPopBox .sxUl li.chooseLis', function () {
    if ($(this).hasClass('on')) {
        $(this).removeClass('on')
    } else {
        $(this).addClass('on')
    }
})
$('#policyPage').addClass('on')
$(".bannerBg").slide({
    titCell: ".hd ul",
    mainCell: ".bannerSlide",
    autoPlay: true,
    effect: "fold",
    interTime: 5000,
    autoPage: true
});
$('.selectList').mouseleave(function(){
    $(this).hide()
})
$('.selectModule').click(function(){
    $(this).siblings().show();
    for(var i = 0;i<$(this).parent().siblings().children().length;i++){
        if(i%2==1){
            $(this).parent().siblings().children().eq(i).hide();
        }
    }
})
function changeSelectStyle(index,data){
    $('.selectModule').eq(index).addClass('on');

    if(!data.baseId){
        $('.selectModule').eq(index).removeClass('on');
    }
}
$('.lbOut li').mouseover(function () {
    $(this).addClass('on').siblings().removeClass('on')
})
$('.lbOut li').mouseleave(function () {
    $(this).removeClass('on')
})