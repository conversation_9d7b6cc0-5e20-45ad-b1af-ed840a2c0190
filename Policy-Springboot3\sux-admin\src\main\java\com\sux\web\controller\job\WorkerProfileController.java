package com.sux.web.controller.job;

import com.sux.common.annotation.Log;
import com.sux.common.core.controller.BaseController;
import com.sux.common.core.domain.AjaxResult;
import com.sux.common.core.page.TableDataInfo;
import com.sux.common.enums.BusinessType;
import com.sux.common.utils.SecurityUtils;
import com.sux.common.utils.poi.ExcelUtil;
import com.sux.system.domain.WorkerProfile;
import com.sux.system.service.IWorkerProfileService;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 零工信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@RestController
@RequestMapping("/job/worker")
public class WorkerProfileController extends BaseController {
    @Autowired
    private IWorkerProfileService workerProfileService;

    /**
     * 查询零工信息列表
     */
    @PreAuthorize("@ss.hasPermi('job:worker:list')")
    @GetMapping("/list")
    public TableDataInfo list(WorkerProfile workerProfile) {
        startPage();
        List<WorkerProfile> list = workerProfileService.selectWorkerProfileList(workerProfile);
        return getDataTable(list);
    }

    /**
     * 查询活跃的零工信息列表
     */
    @GetMapping("/active")
    public TableDataInfo activeList(WorkerProfile workerProfile) {
        startPage();
        List<WorkerProfile> list = workerProfileService.selectActiveWorkerProfileList(workerProfile);
        return getDataTable(list);
    }

    /**
     * 查询已验证的零工信息列表
     */
    @GetMapping("/verified")
    public TableDataInfo verifiedList(WorkerProfile workerProfile) {
        startPage();
        List<WorkerProfile> list = workerProfileService.selectVerifiedWorkerProfileList(workerProfile);
        return getDataTable(list);
    }

    /**
     * 查询高评分零工信息
     */
    @GetMapping("/high-rated")
    public AjaxResult highRatedList(@RequestParam(defaultValue = "4.0") Double minRating, 
                                   @RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectHighRatedWorkerProfileList(minRating, limit);
        return success(list);
    }

    /**
     * 查询经验丰富的零工信息
     */
    @GetMapping("/experienced")
    public AjaxResult experiencedList(@RequestParam(defaultValue = "3") Integer minExperience, 
                                     @RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectExperiencedWorkerProfileList(minExperience, limit);
        return success(list);
    }

    /**
     * 查询推荐零工信息
     */
    @GetMapping("/recommended")
    public AjaxResult recommendedList(@RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectRecommendedWorkerProfileList(limit);
        return success(list);
    }

    /**
     * 查询新注册的零工信息
     */
    @GetMapping("/new")
    public AjaxResult newList(@RequestParam(defaultValue = "30") Integer days, 
                             @RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectNewWorkerProfileList(days, limit);
        return success(list);
    }

    /**
     * 根据关键词搜索零工信息
     */
    @GetMapping("/search")
    public TableDataInfo search(@RequestParam String keyword) {
        startPage();
        List<WorkerProfile> list = workerProfileService.selectWorkerProfileByKeyword(keyword);
        return getDataTable(list);
    }

    /**
     * 根据技能匹配零工信息
     */
    @GetMapping("/match-skills")
    public AjaxResult matchBySkills(@RequestParam List<String> skills, 
                                   @RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectWorkerProfileBySkills(skills, limit);
        return success(list);
    }

    /**
     * 根据地理位置匹配零工信息
     */
    @GetMapping("/match-location")
    public AjaxResult matchByLocation(@RequestParam String location, 
                                     @RequestParam(defaultValue = "10.0") Double radius, 
                                     @RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.selectWorkerProfileByLocation(location, radius, limit);
        return success(list);
    }

    /**
     * 根据薪资期望匹配零工信息
     */
    @GetMapping("/match-salary")
    public AjaxResult matchBySalary(@RequestParam Double salaryMin, 
                                   @RequestParam Double salaryMax, 
                                   @RequestParam String salaryType) {
        List<WorkerProfile> list = workerProfileService.selectWorkerProfileBySalaryExpectation(salaryMin, salaryMax, salaryType);
        return success(list);
    }

    /**
     * 根据招聘信息匹配零工信息
     */
    @GetMapping("/match-job/{jobId}")
    public AjaxResult matchForJob(@PathVariable Long jobId, @RequestParam(defaultValue = "10") Integer limit) {
        List<WorkerProfile> list = workerProfileService.matchWorkerProfileForJob(jobId, limit);
        return success(list);
    }

    /**
     * 智能推荐零工信息
     */
    @GetMapping("/recommend")
    public AjaxResult recommendList(@RequestParam(defaultValue = "10") Integer limit) {
        Long employerId = SecurityUtils.getUserId();
        List<WorkerProfile> list = workerProfileService.recommendWorkerProfileForEmployer(employerId, limit);
        return success(list);
    }

    /**
     * 查询相似的零工信息
     */
    @GetMapping("/similar/{workerId}")
    public AjaxResult similarList(@PathVariable Long workerId, @RequestParam(defaultValue = "5") Integer limit) {
        WorkerProfile workerProfile = workerProfileService.selectWorkerProfileByWorkerId(workerId);
        if (workerProfile == null) {
            return error("零工信息不存在");
        }
        List<WorkerProfile> list = workerProfileService.selectSimilarWorkerProfileList(workerProfile, limit);
        return success(list);
    }

    /**
     * 导出零工信息列表
     */
    @PreAuthorize("@ss.hasPermi('job:worker:export')")
    @Log(title = "零工信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WorkerProfile workerProfile) {
        List<WorkerProfile> list = workerProfileService.selectWorkerProfileList(workerProfile);
        ExcelUtil<WorkerProfile> util = new ExcelUtil<WorkerProfile>(WorkerProfile.class);
        util.exportExcel(response, list, "零工信息数据");
    }

    /**
     * 获取零工信息详细信息
     */
    @GetMapping(value = "/{workerId}")
    public AjaxResult getInfo(@PathVariable("workerId") Long workerId) {
        WorkerProfile workerProfile = workerProfileService.selectWorkerProfileDetailByWorkerId(workerId);
        return success(workerProfile);
    }

    /**
     * 获取当前用户的零工信息
     */
    @GetMapping("/my-profile")
    public AjaxResult getMyProfile() {
        Long userId = SecurityUtils.getUserId();
        WorkerProfile workerProfile = workerProfileService.selectWorkerProfileByUserId(userId);
        return success(workerProfile);
    }

    /**
     * 新增零工信息
     */
    @PreAuthorize("@ss.hasPermi('job:worker:add')")
    @Log(title = "零工信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody WorkerProfile workerProfile) {
        Long userId = SecurityUtils.getUserId();
        if (workerProfileService.checkUserHasWorkerProfile(userId)) {
            return error("新增零工信息失败，用户已有零工信息");
        }
        return toAjax(workerProfileService.insertWorkerProfile(workerProfile));
    }

    /**
     * 修改零工信息
     */
    @PreAuthorize("@ss.hasPermi('job:worker:edit')")
    @Log(title = "零工信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody WorkerProfile workerProfile) {
        Long userId = SecurityUtils.getUserId();
        if (!workerProfileService.checkWorkerProfileEditable(workerProfile.getWorkerId(), userId)) {
            return error("修改零工信息失败，无权限或状态不允许修改");
        }
        return toAjax(workerProfileService.updateWorkerProfile(workerProfile));
    }

    /**
     * 激活零工信息
     */
    @PreAuthorize("@ss.hasPermi('job:worker:activate')")
    @Log(title = "零工信息", businessType = BusinessType.UPDATE)
    @PutMapping("/activate/{workerId}")
    public AjaxResult activate(@PathVariable Long workerId) {
        return toAjax(workerProfileService.activateWorkerProfile(workerId));
    }

    /**
     * 停用零工信息
     */
    @PreAuthorize("@ss.hasPermi('job:worker:deactivate')")
    @Log(title = "零工信息", businessType = BusinessType.UPDATE)
    @PutMapping("/deactivate/{workerId}")
    public AjaxResult deactivate(@PathVariable Long workerId) {
        return toAjax(workerProfileService.deactivateWorkerProfile(workerId));
    }

    /**
     * 暂停零工信息
     */
    @PreAuthorize("@ss.hasPermi('job:worker:suspend')")
    @Log(title = "零工信息", businessType = BusinessType.UPDATE)
    @PutMapping("/suspend/{workerId}")
    public AjaxResult suspend(@PathVariable Long workerId) {
        return toAjax(workerProfileService.suspendWorkerProfile(workerId));
    }

    /**
     * 禁用零工信息
     */
    @PreAuthorize("@ss.hasPermi('job:worker:ban')")
    @Log(title = "零工信息", businessType = BusinessType.UPDATE)
    @PutMapping("/ban/{workerId}")
    public AjaxResult ban(@PathVariable Long workerId) {
        return toAjax(workerProfileService.banWorkerProfile(workerId));
    }

    /**
     * 验证零工信息
     */
    @PreAuthorize("@ss.hasPermi('job:worker:verify')")
    @Log(title = "零工信息", businessType = BusinessType.UPDATE)
    @PutMapping("/verify/{workerId}")
    public AjaxResult verify(@PathVariable Long workerId) {
        return toAjax(workerProfileService.verifyWorkerProfile(workerId));
    }

    /**
     * 删除零工信息
     */
    @PreAuthorize("@ss.hasPermi('job:worker:remove')")
    @Log(title = "零工信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{workerIds}")
    public AjaxResult remove(@PathVariable Long[] workerIds) {
        Long userId = SecurityUtils.getUserId();
        for (Long workerId : workerIds) {
            if (!workerProfileService.checkWorkerProfileDeletable(workerId, userId)) {
                return error("删除零工信息失败，无权限或状态不允许删除");
            }
        }
        return toAjax(workerProfileService.deleteWorkerProfileByWorkerIds(workerIds));
    }

    /**
     * 批量更新零工状态
     */
    @PreAuthorize("@ss.hasPermi('job:worker:edit')")
    @Log(title = "零工信息", businessType = BusinessType.UPDATE)
    @PutMapping("/batch-status")
    public AjaxResult batchUpdateStatus(@RequestBody Map<String, Object> params) {
        Long[] workerIds = (Long[]) params.get("workerIds");
        String status = (String) params.get("status");
        return toAjax(workerProfileService.batchUpdateWorkerProfileStatus(workerIds, status));
    }

    /**
     * 更新零工评分
     */
    @PreAuthorize("@ss.hasPermi('job:worker:rate')")
    @Log(title = "零工信息", businessType = BusinessType.UPDATE)
    @PutMapping("/rate/{workerId}")
    public AjaxResult rate(@PathVariable Long workerId, @RequestParam Double rating) {
        if (rating < 0 || rating > 5) {
            return error("评分必须在0-5之间");
        }
        return toAjax(workerProfileService.updateWorkerRating(workerId, rating));
    }

    /**
     * 更新零工工作统计
     */
    @PreAuthorize("@ss.hasPermi('job:worker:update-stats')")
    @Log(title = "零工信息", businessType = BusinessType.UPDATE)
    @PutMapping("/update-stats/{workerId}")
    public AjaxResult updateStats(@PathVariable Long workerId, @RequestParam Boolean isSuccess) {
        return toAjax(workerProfileService.updateWorkerJobStats(workerId, isSuccess));
    }

    /**
     * 查询零工统计数据
     */
    @PreAuthorize("@ss.hasPermi('job:worker:list')")
    @GetMapping("/statistics")
    public AjaxResult statistics(@RequestParam(required = false) Long userId) {
        Map<String, Object> data = workerProfileService.selectWorkerProfileStatistics(userId);
        return success(data);
    }

    /**
     * 根据工作类别统计零工数量
     */
    @GetMapping("/statistics/category")
    public AjaxResult statisticsByCategory() {
        List<Map<String, Object>> data = workerProfileService.selectWorkerProfileCountByCategory();
        return success(data);
    }

    /**
     * 根据所在地统计零工数量
     */
    @GetMapping("/statistics/location")
    public AjaxResult statisticsByLocation() {
        List<Map<String, Object>> data = workerProfileService.selectWorkerProfileCountByLocation();
        return success(data);
    }

    /**
     * 根据学历统计零工数量
     */
    @GetMapping("/statistics/education")
    public AjaxResult statisticsByEducation() {
        List<Map<String, Object>> data = workerProfileService.selectWorkerProfileCountByEducation();
        return success(data);
    }

    /**
     * 根据经验年数统计零工数量
     */
    @GetMapping("/statistics/experience")
    public AjaxResult statisticsByExperience() {
        List<Map<String, Object>> data = workerProfileService.selectWorkerProfileCountByExperience();
        return success(data);
    }

    /**
     * 查询即将可工作的零工信息
     */
    @GetMapping("/available-soon")
    public AjaxResult availableSoonList(@RequestParam(defaultValue = "7") Integer days) {
        List<WorkerProfile> list = workerProfileService.selectWorkerProfileAvailableSoon(days);
        return success(list);
    }
}
