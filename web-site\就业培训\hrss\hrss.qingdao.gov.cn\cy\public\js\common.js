//版本浏览器内核、IE实现Object.assign
if (typeof Object.assign != 'function') {
	Object.assign = function (target) {
		'use strict';
		if (target == null) {
			throw new TypeError('Cannot convert undefined or null to object');
		}
		target = Object(target);
		for (var index = 1; index < arguments.length; index++) {
			var source = arguments[index];
			if (source != null) {
				for (var key in source) {
					if (Object.prototype.hasOwnProperty.call(source, key)) {
						target[key] = source[key];
					}
				}
			}
		}
		return target;
	};
}
// 创建一个新的meta元素
var meta = document.createElement('meta');
// 设置name和content属性
meta.setAttribute('name', 'referrer');
meta.setAttribute('content', 'no-referrer');
// 将meta元素添加到head中
document.head.appendChild(meta);
$(function () {
	// 搜索
	$('.selected').click(function (e) {
		$(this).next().toggle();
	});
	$('.mySelect .msUl a').click(function (e) {
		var att = $(this).attr("rel");
		$(this).closest('.msUl').prev().html($(this).html()).attr("rel", att);
		$(this).closest('.msUl').hide();
		$(this).parent().addClass('current').siblings().removeClass('current');
	});
	//给大盒子添加鼠标离开 让ul隐藏
	$('.mySelect').mouseleave(function (e) {
		$(this).children('.msUl').hide();
	});
	//下拉菜单
	// $(".navCon").slide({ 
	// 	type:"menu",
	// 	titCell:".navLi", //鼠标触发对象
	// 	targetCell:"ul", //titCell里面包含的要显示/消失的对象
	// 	effect:"slideDown", //targetCell下拉效果
	// 	delayTime:500 , //效果时间
	// 	triggerTime:0, //鼠标延迟触发时间（默认150）
	// 	returnDefault:true 
	// });	
	// 筛选条件点击选中状态
	$('.filterBar a').click(function () {
		$(this).addClass('on').siblings().removeClass('on')
	})
	// 网站导航
	$(".webnavBox").hover(function () {
		$('.siteNav_Box').stop().slideDown()
	}, function () {
		$('.siteNav_Box').stop().slideUp()
	});
	$(".zq_Box").hover(function () {
		$('.zqSon').show()
	}, function () {
		$('.zqSon').hide()
	});
})
// 地区专区返回首页
function zqindex(e){
	viewModel1.zqcityId(localStorage.setItem('zqcityId',''))
	viewModel1.zqcityId(localStorage.setItem('zqcityName',''))
	if(e==1){
		window.location.href = './index.html'
	}else{
		window.location.href = '../index.html'
	}
}
//筛选项更多效果
function operate(btn) {
	const optionsNode = document.getElementById('container');
	const unfold = btn.getAttribute("unfold");
	if (unfold && unfold === "1") {
		$('.filterDown').removeClass('filterUp')
		optionsNode.classList.remove("unfold");
	} else {
		$('.filterDown').addClass('filterUp')
		optionsNode.classList.add("unfold");
	}
	btn.setAttribute("unfold", unfold === "0" ? "1" : "0");
}
//页底法律声明
// $(document).on('click', '#declare', function () {
// 	alertShow('.popDeclare');
// 	getNum($('#getNum'));
// });

//法律声明弹窗
function getNum(obj) {
	var t1 = null;
	obj.html('立即关闭（10秒）');
	$('.lookDeclare').removeClass("bgBlue").attr({
		'disabled': true
	});
	$('.lookDeclare').css('pointer-events', 'none')
	t1 = setInterval(countDown01, 1000);

	function countDown01() {
		var numOld = obj.html();
		var num = numOld.slice(5, (numOld.length - 2));
		if (num > 0) {
			num--;
			obj.html('立即关闭（' + num + ' 秒）');
		} else {
			$('.lookDeclare').addClass("bgBlue").attr({
				'disabled': false
			});
			$('.lookDeclare').css('pointer-events', 'auto')
			obj.html('立即关闭');
			obj.addClass('cp')
			clearInterval(t1);
			t1 = null;
		};
	}
}
// 正则表达式
//检验手机号
var formReg = {
	mobile: /^(0|86|17951)?(13[0-9]|15[012356789]|17[0135678]|18[0-9]|19[0-9]|14[57])[0-9]{8}$/,
}
function phoneReg(num) {
	var reg = formReg.mobile;
	if (reg.test(num) === false) {
		return false
	} else {
		return true;
	}
}
// 正负整数
function integer(e) {
	e.value = e.value.replace(/[^\-\d]/g, ""); // 只能输入-和数字
	e.value = e.value.replace(/\-{2,}/g, "-"); // -只能保留一个
	e.value = e.value.replace(/(\d)\-/g, "$1"); // 数字后面不能接-，不能出现类似-11-2,12-，11-23
	e.value = e.value.replace(/-(0+)/g, "0"); // 不能出现-0,-001,-0001类似
	e.value = e.value.replace(/^0+(\d)/, "$1"); // 第一位0开头，0后面为数字，则过滤掉，取后面的数字

}
//正整数校验
function proving(e) {//输入框校验正整数且不为空
	e.value = e.value.replace(/[^\.\d]/g, '');
	e.value = e.value.replace('.', '');
	if (e.value.indexOf(".") < 0 && e.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额 
		e.value = parseFloat(e.value);
	}
	if (e.value == '') {//为空时默认为0
		e.value = ''
	}
}
// 保留两位小数
function provingSpot2(e) {//输入框校验正整数且不为空
	e.value = e.value.replace(/[^\.\d]/g, '');
	e.value = e.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的  
	e.value = e.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
	e.value = e.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数  
	if (e.value.indexOf(".") < 0 && e.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额 
		e.value = parseFloat(e.value);
	}
	if (e.value == '') {//为空时默认为0
		e.value = ''
	}
}
//小数点前5位，点后两位
function provingSpot2Self5(e) {//输入框校验正整数且不为空
	e.value = e.value.replace(/[^\.\d]/g, '');
	e.value = e.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的  
	e.value = e.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
	e.value = e.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数  
	if (e.value.indexOf(".") < 0 && e.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额 
		e.value = parseFloat(e.value);
	}
	if (e.value > 99999.99) {//为空时默认为0
		e.value = e.value.substr(0, e.value.length - 1)
	}
}
//小数点前6位，点后两位
function provingSpot2Self6(e) {//输入框校验正整数且不为空
	e.value = e.value.replace(/[^\.\d]/g, '');
	e.value = e.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的  
	e.value = e.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
	e.value = e.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数  
	if (e.value.indexOf(".") < 0 && e.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额 
		e.value = parseFloat(e.value);
	}
	if (e.value > 999999.99) {//为空时默认为0
		e.value = e.value.substr(0, e.value.length - 1)
	}
}
//小数点前7位，点后两位
function provingSpot2Self7(e) {//输入框校验正整数且不为空
	e.value = e.value.replace(/[^\.\d]/g, '');
	e.value = e.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的  
	e.value = e.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
	e.value = e.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数  
	if (e.value.indexOf(".") < 0 && e.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额 
		e.value = parseFloat(e.value);
	}
	if (e.value > 9999999.99) {//为空时默认为0
		e.value = e.value.substr(0, e.value.length - 1)
	}
}
//小数点前8位，点后两位
function provingSpot2Self8(e) {//输入框校验正整数且不为空
	e.value = e.value.replace(/[^\.\d]/g, '');
	e.value = e.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的  
	e.value = e.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
	e.value = e.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数  
	if (e.value.indexOf(".") < 0 && e.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额 
		e.value = parseFloat(e.value);
	}
	if (e.value > 99999999.99) {//为空时默认为0
		e.value = e.value.substr(0, e.value.length - 1)
	}
}
//小数点前9位，点后两位
function provingSpot2Self9(e) {//输入框校验正整数且不为空
	e.value = e.value.replace(/[^\.\d]/g, '');
	e.value = e.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的  
	e.value = e.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
	e.value = e.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数  
	if (e.value.indexOf(".") < 0 && e.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额 
		e.value = parseFloat(e.value);
	}
	if (e.value > 999999999.99) {//为空时默认为0
		e.value = e.value.substr(0, e.value.length - 1)
	}
}
// 大于等于0正整数校验
function provingSelf0(e) {//输入框校验正整数且不为空
	e.value = e.value.replace(/[^\.\d]/g, '');
	e.value = e.value.replace('.', '');
	if (e.value.indexOf(".") < 0 && e.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额 
		e.value = parseFloat(e.value);
	}

}
//大于0正整数校验
function provingSelf(e) {//输入框校验正整数且不为空
	e.value = e.value.replace(/[^\.\d]/g, '');
	e.value = e.value.replace('.', '');
	if (e.value.indexOf(".") < 0 && e.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额 
		e.value = parseFloat(e.value);
	}
	if (e.value == 0) {//为空时默认为0
		e.value = ''
	}
}
function provingSpot2Self(e) {//输入框校验正整数且不为空
	e.value = e.value.replace(/[^\.\d]/g, '');
	e.value = e.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的  
	e.value = e.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
	e.value = e.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数  
	if (e.value.indexOf(".") < 0 && e.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额 
		e.value = parseFloat(e.value);
	}
	if (e.value == 0) {//为空时默认为0
		e.value = ''
	}
}
function provingSpot2(e) {//输入框校验正整数且不为空
	e.value = e.value.replace(/[^\.\d]/g, '');
	e.value = e.value.replace(/\.{2,}/g, "."); //只保留第一个. 清除多余的  
	e.value = e.value.replace(".", "$#$").replace(/\./g, "").replace("$#$", ".");
	e.value = e.value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');//只能输入两个小数  
	if (e.value.indexOf(".") < 0 && e.value != "") {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额 
		e.value = parseFloat(e.value);
	}
	if (e.value == '') {//为空时默认为0
		e.value = ''
	}
}
//小数点前10位，点后两位，正负数
function provingZF(e){//输入框校验正整数且不为空
    e.value = e.value.replace(/[^\-|.|\d]/g, '');
    if(!/^[+-]?\d*\.{0,1}\d{0,1}$/.test(e.value)){
    e.value=e.value.replace(/\.\d{2,}$/,e.value.substr(e.value.indexOf('.'),3));
    }
    if(e.value.length>1){
        var one = e.value.substr(0,1)
        var other = e.value.slice(1,e.value.length)
        if (other.indexOf("-") != -1 ) {//以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额 
            other = other.replace("-","")
        }
        e.value = one+other
    }
    if(e.value >9999999999.99){//为空时默认为0
        e.value = e.value.substr(0, e.value.length-1)
    }
    if(e.value < -9999999999.99){//为空时默认为0
        e.value = e.value.substr(0, e.value.length-1)
    }
}
//身份证正则校验
var aCity = { 11: "北京", 12: "天津", 13: "河北", 14: "山西", 15: "内蒙古", 21: "辽宁", 22: "吉林", 23: "黑龙江", 31: "上海", 32: "江苏", 33: "浙江", 34: "安徽", 35: "福建", 36: "江西", 37: "山东", 41: "河南", 42: "湖北", 43: "湖南", 44: "广东", 45: "广西", 46: "海南", 50: "重庆", 51: "四川", 52: "贵州", 53: "云南", 54: "西藏", 61: "陕西", 62: "甘肃", 63: "青海", 64: "宁夏", 65: "新疆", 71: "台湾", 81: "香港", 82: "澳门", 91: "国外" }
function isCardID(sId) {
	var iSum = 0;
	var info = "";
	// if ( !new RegExp(regexEnum.idcard).test(sId)) return "你输入的身份证长度或格式错误";
	var slen = sId.length;
	var reg2 = /([a-zA-Z0-9]{15,17})/;
	var reg = /(^\d{17}(\d|X)$)/;
	if (slen > 0) {
		if (reg.test(sId) === false)
			//      return "你输入的身份证长度或格式错误,若最后一位为X请输入大写";
			return "身份证长度或格式错误,若最后一位为X请输入大写";
		sId = sId.replace(/x$/i, "a");

		if (aCity[parseInt(sId.substr(0, 2))] == null) return "您的身份证地区非法";
		sBirthday = sId.substr(6, 4) + "-" + Number(sId.substr(10, 2)) + "-" + Number(sId.substr(12, 2));
		var d = new Date(sBirthday.replace(/-/g, "/"));
		if (sBirthday != (d.getFullYear() + "-" + (d.getMonth() + 1) + "-" + d.getDate())) return "身份证上的出生日期非法";
		for (var i = 17; i >= 0; i--) iSum += (Math.pow(2, i) % 11) * parseInt(sId.charAt(17 - i), 11);
		if (iSum % 11 != 1) return "您输入的身份证号非法";
		return true;//aCity[parseInt(sId.substr(0,2))]+","+sBirthday+","+(sId.substr(16,1)%2?"男":"女") 
	} else {
		if (reg2.test(sId) === false) {
			return "身份证格式有误";
		} else {
			return true;
		}

	}
}
//邮箱正则验证
function isEmail(obj) {
	var reg = new RegExp(/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/);//正则表达式  /* lyw 23.11.1 修改 */
	// var reg = new RegExp("^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$"); //正则表达式
	// var reg = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/; //正则表达式
	if (!reg.test(obj)) { //正则验证不通过，格式不对
		return '请输入正确的邮箱地址';
	} else {
		return true;
	}
}
//邮政编码正则验证
function isPostalCode(obj) {
	var reg = new RegExp(/^\d{6}$/);//正则表达式  
	if (!reg.test(obj)) { //正则验证不通过，格式不对
		return '请输入正确的邮政编码';
	} else {
		return true;
	}
}
//统一社会信用代码正则验证
function isCreditCode(obj) {
	var reg = new RegExp("[1-9A-GY]{1}[1239]{1}[1-5]{1}[0-9]{5}[0-9A-Z]{10}"); //正则表达式
	if (!reg.test(obj)) { //正则验证不通过，格式不对
		return '请输入正确的统一社会信用代码';
	} else {
		if(obj.length!=18){
            return '请输入正确的统一社会信用代码';
        }
		return true;
	}
}
//检验手机固话
var formReg2 = {
	mobile: /^((0\d{2,3}-\d{7,8})|(1[7359846]\d{9}))$/,
}
function mobiles(num) {
	var reg = formReg2.mobile;
	if (reg.test(num) === false) {
		return '请输入正确的联系电话'
	} else {
		return true;
	}
}
//网址验证
function isWebs(obj) {
	var reg = new RegExp("^((https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|])+$"); //正则表达式
	if (!reg.test(obj)) { //正则验证不通过，格式不对
		return '请输入正确的网址';
	} else {
		return true;
	}
}
function afterLoginEffect() {
	$(".afterLogin").hover(function () {
		$('.afterLoginBox').stop(true, true).slideDown()
	}, function () {
		$('.afterLoginBox').stop(true, true).slideUp()
	});
}
//点击当前dom添加类名on，其兄弟dom隐藏类名on
function thisOn(obj) {
	$(obj).addClass('on').siblings().removeClass('on');
}
//点击当前dom显示和隐藏类名on
function toggleOn(obj) {
	$(obj).toggleClass('on');
}
//弹窗显示函数--show方法dom不存在
function alertShow(obj) {
	$(obj).show();
	$(obj).find('.zh_popUpcon').removeClass('none zh_fadeOutUp').addClass('animated zh_fadeInDown');
}
//弹窗消失函数--hide方法方法dom不存在
function alertClose(obj) {
	$(obj).find('.zh_popUpcon').removeClass('zh_fadeInDown').addClass('zh_fadeOutUp');
	$(obj).fadeOut();
}
//弹窗显示函数--visible方法dom存在
function alertVisible(obj) {
	$(obj).css({ 'visibility': 'visible', 'z-index': '300' });
	$(obj).find('.zh_popUpcon').css('visibility', 'visible').removeClass('zh_fadeOutUp').addClass('animated zh_fadeInDown');
}
//弹窗消失函数--hidden方法dom存在
function alertHidden(obj) {
	$(obj).find('.zh_popUpcon').removeClass('zh_fadeInDown').addClass('zh_fadeOutUp');
	$(obj).css({ 'visibility': 'hidden' });
	setTimeout(function () {
		$(obj).css({ 'z-index': '-100' });
	}, 300);
}

//判断用户是否已经登录
// var viewModel2 = {};
// var viewModel = {};
// Object.assign(viewModel, viewModel1, viewModel2);

//获取浏览器的参数
function getQueryString(name) {
	var reg = new RegExp('(?:(?:&|\\?)' + name + '=([^&]*))|(?:/' + name + '/([^/]*))', 'i');
	var r = window.location.href.match(reg);
	if (r != null)
		return decodeURI(r[1] || r[2]);
	return null;
}
// 头部调用
function headerBar() {
	$.ajax({
		type: "GET",
		async: false,
		dataType: 'html',
		url: '../public/html/headerBar.html',
		crossDomain: true,
		success: function (result) {
			$('#headerBar').html(result);
			afterLoginEffect(); //登录后状态展开
		},
		error: function (e) {
		}
	})
}
// 底部调用 
function footerBar() {
	$.ajax({
		type: "GET",
		async: false,
		dataType: 'html',
		url: '../public/html/footerBar.html?v=************',
		crossDomain: true,
		success: function (result) {
			$('#footerBar').html(result);
		},
		error: function (e) {
		}
	})
}

function clearTab() {
	sessionStorage.removeItem('compScales')
	sessionStorage.removeItem('policyThemes')
	sessionStorage.removeItem('crowds')
	sessionStorage.removeItem('policyTypes')
}


//用于生成uuid
function guid() {
	function S4() {
		return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
	}
	return (S4() + S4() + "-" + S4() + "-" + S4() + "-" + S4() + "-" + S4() + S4() + S4());
}

/**
 * 页面跳转
 * 判断已登录：直接跳转目标页面
 * 判断未登录：跳转登录页
 *  1：政策订阅;  2:发布技术供应;  3:发布技术需求； 4：政策匹配 5:发布融资需求
 */
function goOtherPage(type) {
	var submits = function (v, h, f) {
		if (v == true) {
			//sessionStorage.setItem('loginUrl', url);
			window.location.href = "https://tysfrz.isdapp.shandong.gov.cn/jpaas-jis-sso-server/sso/entrance/auth-center?appMark=QDCHUANGYEYPT&userType=2";
		} else {

		}
		return true;
	};
	var url = "";
	switch (type) {
		case 1:
			url = '../member/mySubscribe.html';
			break;
		case 2:
			url = '../member/technicalSupNeedList.html';
			break;
		case 3:
			url = '../member/technicalSupNeedList.html';
			break;
		// case 4:
		// 	url = '../policyMatch/policyMatchList.html';
		// 	break;
		case 5:
			url = './member/financialList.html';
			break;
	}
	if (checkLogin()) {
		window.location.href = url
	} else {
		$.jBox.tip("请登录后进行查看");
		// $.jBox.confirm("请登录后进行查看", "提示", submits, { buttons: { '去登录': true, '取消': false } });

	}

}

/**
 * 未开发功能
 * 弹窗提示“功能开发中，敬请期待”
 */
function undeveloped() {
	$.jBox.tip('正在开发中，敬请期待！');
}
//改变选中状态
function changeChkStatus(obj) {
	if ($(obj).className != 'on') {
		$(obj).addClass('on');
		$(obj).siblings('a').removeClass('on');
	}
}

function notOpen() {
	$.jBox.tip('正在开发中，敬请期待！');
}


// 收藏
function addOrCanleCollection(name) {
	var objs = {
		infoId: getQueryString('id'),
		infoTypeName: name, //当前模块中文名称
		infoUrl: window.location.pathname + window.location.search, //当前页面url
	}
	obj = JSON.stringify(objs);
	ajaxPostDataFull_token("api-app/v1/qctCollection/addOrCanleCollection", obj);
	if (PostData.code == 0) {
		// viewModel.isColl(0)
		// $.jBox.tip("取消收藏！");
		if (PostData.obj == '取消成功') {
			$.jBox.tip("取消成功")
			viewModel.isColl(0)
		} else {
			$.jBox.tip("收藏成功")
			viewModel.isColl(1)
		}
	} else {
		$.jBox.tip(PostData.obj)
	}
}
//法律声明 倒计时
function lawgetNum(obj) {
	var t1 = null;
	if (obj) { //开始倒计时
		obj.val('5s').addClass("on").attr({
			'disabled': true
		});
		obj.removeClass('cp')
		t1 = setInterval(lawcountDown, 1000);

		function lawcountDown() {
			var numOld = obj.val();
			var num = numOld.slice(0, (numOld.length - 1));
			if (num > 0) {
				num--;
				obj.val(num + "s");
			} else {
				obj.val('关闭').removeClass("on").attr({
					'disabled': false
				});
				obj.addClass('cp')
				clearInterval(t1);
				t1 = null;
			};
		}
	} else { //结束倒计时
		$('.lawclosebtn').val('关闭').removeClass("on").attr({
			'disabled': false
		});
		obj.addClass('cp')
		clearInterval(t1);
		t1 = null;
	}
};
// 去掉富文本返回的标签
function delHtmlTag(str) {
	str = str.replace(/<[^>]+>/g, "");
	str = str.replace(/\s/g, "");
	str = str.replace(/&nbsp;/ig, "");
	return str
}
// 返回上一页
function backPage() {
	window.history.go(-1)
}
// 排序
function numSort(json,key){
	for(var j=1,jl=json.length;j < jl;j++){
		var temp = json[j],
			val  = Number(temp[key]) ,
			i    = j-1;
		while(i >=0 && json[i][key]>val){
			json[i+1] = json[i];
			i = i-1;    
		}
		json[i+1] = temp;

	}
	return json;
}


window.onload = function() {
	var images = document.querySelectorAll('img');
	images.forEach(function (image) {
		// 将原始路径进行替换操作
		var newPath = image.getAttribute('src').replace('./UEditor/dialogs/attachment/fileTypeImages/', 'https://hrss.qingdao.gov.cn/cy/public/plugins/UEditor/dialogs/attachment/fileTypeImages/');
		// 更新图片元素的src属性为新路径
		image.setAttribute('src', newPath);
	});
 };
//  下载附件改名字
 function getBlob(url, cb) {
    var xhr = new XMLHttpRequest();
    xhr.open('GET', url, true);
    xhr.responseType = 'blob';
    xhr.onload = function () {
        if (xhr.status === 200) {
            cb(xhr.response);
        }
    };
    xhr.send();
}
/**
 * 保存
 * @param  {Blob} blob
 * @param  {String} filename 想要保存的文件名称
 */
function saveAs(blob, filename) {
    if (window.navigator.msSaveOrOpenBlob) {
        navigator.msSaveBlob(blob, filename);
    } else {
        var link = document.createElement('a');
        var body = document.querySelector('body');

        link.href = window.URL.createObjectURL(blob);
        link.download = filename;

        // fix Firefox
        link.style.display = 'none';
        body.appendChild(link);

        link.click();
        body.removeChild(link);

        window.URL.revokeObjectURL(link.href);
    }
}
// 政策订阅
function gozcdyPage(e){
    if(checkLogin()){
        if(e==1){
            window.location.href='./member/mySubscribe.html'
        }else{
            window.location.href='../member/mySubscribe.html'
        }
    }else{
		$.jBox.tip("请先登录");
	}
}
$('.selectList').mouseleave(function(){
    $(this).hide()
})
$('.selectModule').click(function(){
	if($(this).hasClass('cdmj')){//创业场地
		if(!viewModel.type()){
			$.jBox.tip("请先选择场地类型！");
			return false
		}
        $(this).siblings().show();
        for(var i = 0;i<$(this).parent().siblings().children().length;i++){
            if(i%2==1){
                $(this).parent().siblings().children().eq(i).hide();
            }
        }
	}else if($(this).hasClass('gyxx')){//供应信息
		if(!viewModel.provinceId()){
			$.jBox.tip("请先选择省！");
			return false
		}
		if($(this).hasClass('gyxxq')){
			if(!viewModel.cityId()){
				$.jBox.tip("请先选择市！");
				return false
			}
		}
        $(this).siblings().show();
        for(var i = 0;i<$(this).parent().siblings().children().length;i++){
            if(i%2==1){
                $(this).parent().siblings().children().eq(i).hide();
            }
        }
	}else if($(this).hasClass('zjxx')){//专家信息
		if(!viewModel.province()){
			$.jBox.tip("请先选择省！");
			return false
		}
		if($(this).hasClass('zjxxq')){
			if(!viewModel.city()){
				$.jBox.tip("请先选择市！");
				return false
			}
		}
        $(this).siblings().show();
        for(var i = 0;i<$(this).parent().siblings().children().length;i++){
            if(i%2==1){
                $(this).parent().siblings().children().eq(i).hide();
            }
        }
	}else{
		$(this).siblings().show();
		for(var i = 0;i<$(this).parent().siblings().children().length;i++){
			if(i%2==1){
				$(this).parent().siblings().children().eq(i).hide();
			}
		}
	}
    
})
function changeSelectStyle(index,data){
    $('.selectModule').eq(index).addClass('on');

    if(data.baseId==''||data.value==''){
        $('.selectModule').eq(index).removeClass('on');
    }
}
// 敏感词
function sensitiveWords(title,obj){
	ajaxgetDataFull('api-service/v1/tutorConsult/frontend/sensitive?title='+title)
	if(getData.code==0){
		if(getData.obj.length>0){
			$.jBox.confirm(obj+"涉及敏感词："+getData.obj, "提示",  { buttons: { '确定': true } });
            // $.jBox.tip(obj+'请勿使用敏感词！')
            return false;
		}else{
			return true;
		}
	}
}