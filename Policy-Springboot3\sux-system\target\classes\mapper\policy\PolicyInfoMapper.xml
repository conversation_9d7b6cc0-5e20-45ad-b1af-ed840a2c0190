<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sux.system.mapper.PolicyInfoMapper">
    
    <resultMap type="PolicyInfo" id="PolicyInfoResult">
        <result property="policyId"          column="policy_id"          />
        <result property="policyName"        column="policy_name"        />
        <result property="policyDescription" column="policy_description" />
        <result property="policyType"        column="policy_type"        />
        <result property="status"            column="status"             />
        <result property="delFlag"           column="del_flag"           />
        <result property="createId"          column="create_id"          />
        <result property="createTime"        column="create_time"        />
        <result property="updateId"          column="update_id"          />
        <result property="updateTime"        column="update_time"        />
        <result property="remark"            column="remark"             />
    </resultMap>

    <sql id="selectPolicyInfoVo">
        select policy_id, policy_name, policy_description, policy_type, status, del_flag, create_id, create_time, update_id, update_time, remark from policy_info
    </sql>

    <select id="selectPolicyInfoList" parameterType="PolicyInfo" resultMap="PolicyInfoResult">
        <include refid="selectPolicyInfoVo"/>
        <where>  
            del_flag = '0'
            <if test="policyName != null  and policyName != ''"> and policy_name like concat('%', #{policyName}, '%')</if>
            <if test="policyType != null  and policyType != ''"> and policy_type = #{policyType}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND create_time &gt;= #{params.beginTime}
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND create_time &lt;= #{params.endTime}
            </if>
        </where>
        order by policy_id desc
    </select>
    
    <select id="selectPolicyInfoByPolicyId" parameterType="Long" resultMap="PolicyInfoResult">
        <include refid="selectPolicyInfoVo"/>
        where policy_id = #{policyId} and del_flag = '0'
    </select>

    <select id="checkPolicyNameUnique" parameterType="String" resultMap="PolicyInfoResult">
        <include refid="selectPolicyInfoVo"/>
        where policy_name = #{policyName} and del_flag = '0' limit 1
    </select>
        
    <insert id="insertPolicyInfo" parameterType="PolicyInfo" useGeneratedKeys="true" keyProperty="policyId">
        insert into policy_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="policyName != null and policyName != ''">policy_name,</if>
            <if test="policyDescription != null">policy_description,</if>
            <if test="policyType != null">policy_type,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createId != null">create_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateId != null">update_id,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="policyName != null and policyName != ''">#{policyName},</if>
            <if test="policyDescription != null">#{policyDescription},</if>
            <if test="policyType != null">#{policyType},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createId != null">#{createId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateId != null">#{updateId},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updatePolicyInfo" parameterType="PolicyInfo">
        update policy_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="policyName != null and policyName != ''">policy_name = #{policyName},</if>
            <if test="policyDescription != null">policy_description = #{policyDescription},</if>
            <if test="policyType != null">policy_type = #{policyType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createId != null">create_id = #{createId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateId != null">update_id = #{updateId},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where policy_id = #{policyId}
    </update>

    <delete id="deletePolicyInfoByPolicyId" parameterType="Long">
        update policy_info set del_flag = '2' where policy_id = #{policyId}
    </delete>

    <delete id="deletePolicyInfoByPolicyIds" parameterType="String">
        update policy_info set del_flag = '2' where policy_id in 
        <foreach item="policyId" collection="array" open="(" separator="," close=")">
            #{policyId}
        </foreach>
    </delete>

</mapper>
