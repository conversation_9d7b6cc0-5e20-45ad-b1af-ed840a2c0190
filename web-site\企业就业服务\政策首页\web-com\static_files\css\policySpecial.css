.conAuto1400 {
    width: 1400px;
    min-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

/* 弹窗 */

.alertBg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 13;
}

.alertCont {
    width: 818px;
    background-color: #ffffff;
    margin: 5% auto 0;
    border-radius: 5px;
    /* overflow: hidden; */
}

.alertTitle {
    line-height: 67px;
    color: #ffffff;
    font-size: 20px;
    font-weight: bold;
    background: linear-gradient(135deg, #2c7be5 0%, #1e5aa8 100%);
    text-align: center;
    position: relative;
}

.alertClose {
    width: 15px;
    height: 15px;
    position: absolute;
    right: 20px;
    top: 26px;
}

.alertClose:hover {
    cursor: pointer;
}

.alertClose img {
    display: block;
    width: 100%;
}

.alertUl2 {
    max-height: 560px;
    padding: 30px;
    background: #f8f9fa;
}

/* 弹窗 start */
.cxPopBox {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 11;
}

.cxPopBox .popBox {
    width: 860px;
    top: 5%;
    left: 50%;
    margin-left: -430px;
    background: #fff;
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
}

.cxPopBox .popBox .titleBox {
    height: 80px;
    box-sizing: border-box;
    padding-top: 10px;
    background: linear-gradient(135deg, #2c7be5 0%, #1e5aa8 100%);
}

.cxPopBox .popBox .closeBtn {
    width: 16px;
    height: 15px;
    right: 20px;
    top: 28px;
}

.cxPopBox .popBox .closeBtn:hover {
    transform: rotate(90deg);
}

.cxPopBox .popBox .titleBox .bTitle {
    font-size: 24px;
    color: #fff;
}

.cxPopBox .popBox .sxBox {
    width: 820px;
    max-height: 420px;
    margin-top: 30px;
    overflow-x: hidden;
    overflow-y: scroll;
    box-sizing: border-box;
    padding-bottom: 10px;
}

.cxPopBox .popBox .sxBox::-webkit-scrollbar {
    width: 5px;
    height: 4px;
}

.cxPopBox .popBox .sxBox::-webkit-scrollbar-thumb {
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background-color: #dddddd;
}

.cxPopBox .popBox .sxBox::-webkit-scrollbar-track {
    border-radius: 0;
}

.cxPopBox .popBox .sxBox .sxDiv {
    padding-right: 20px;
    padding-bottom: 20px;
    box-sizing: border-box;
    box-shadow: 0px 0px 10px 5px #deecfe;
}

.cxPopBox .popBox .sxBox .sxDiv .sxTitle {
    padding-left: 37px;
    padding-right: 18px;
    border-radius: 6px 0 16px 0;
    height: 36px;
    line-height: 36px;
    background: linear-gradient(to bottom, #12abbf, #0155d8);
    -webkit-border-radius: 6px 0 16px 0;
    -moz-border-radius: 6px 0 16px 0;
    -ms-border-radius: 6px 0 16px 0;
    -o-border-radius: 6px 0 16px 0;
}

.cxPopBox .popBox .sxBox .sxDiv .sxTitle img {
    left: 14px;
    top: 10px;
}

.cxPopBox .popBox .sxBox .sxDiv .sxUl li {
    min-width: 100px;
    height: 30px;
    line-height: 30px;
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    border: 1px solid #dddddd;
    padding: 0 10px;
    box-sizing: border-box;
    background-color: #fff;
    cursor: pointer;
}

.cxPopBox .popBox .sxBox .sxDiv .sxUl li.on {
    color: #0052d9;
    border: 1px solid #0052d9;
    background-color: #eef3fc;
}

.cxPopBox .popBox .ksA {
    width: 140px;
    height: 40px;
    line-height: 40px;
    border-radius: 30px;
    background: linear-gradient(to right, #12abbf, #0155d8);
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -ms-border-radius: 30px;
    -o-border-radius: 30px;
}

/* 弹窗 end */
/* banner start */
.bannerBg {
    width: 100%;
    height: 260px;
    min-width: 1400px;
    background: url(../images/policySBanner.jpg) no-repeat center;
}

/* box2 start */
.box2 {
    width: 100%;
    height: 358px;
    min-width: 1400px;
    box-sizing: border-box;
    padding-top: 40px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.titleP {
    font-size: 30px;
    padding-bottom: 18px;
    border-bottom: 3px solid #2c7be5;
}

.titleP em {
    font-weight: bold;
    color: #0052d9;
}

.moreBtn {
    height: 30px;
    padding-right: 12px;
    border-radius: 30px;
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
}

.moreBtn span {
    line-height: 30px;
    padding-right: 20px;
    padding-left: 11px;
}

.moreBtn:hover {
    animation: moreBtn 0.5s 1 forwards;
    -webkit-animation: moreBtn 0.5s 1 forwards;
    box-shadow: 0px 0px 10px 3px rgba(0, 77, 198, 0.35);
}

@keyframes moreBtn {
    from {
        background-position: 0;
    }

    to {
        background-position: 71px;
    }
}

.moreBtn.moreFour:hover {
    animation: moreFour 0.5s 1 forwards;
    -webkit-animation: moreFour 0.5s 1 forwards;
    box-shadow: 0px 0px 10px 3px rgba(0, 77, 198, 0.35);
}

@keyframes moreFour {
    from {
        background-position: 0;
    }

    to {
        background-position: 100px;
    }
}

.box2 .hd {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
}

.box2 .hd a {
    position: absolute;
    height: 36px;
    width: 36px;
    top: 102px;
}

.box2 .hd a.prev {
    left: 0;
    background: #2c7be5;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.box2 .hd a.prev:hover {
    background: #1e5aa8;
}

.box2 .hd a.next {
    right: 0;
    background: #2c7be5;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.box2 .hd a.next:hover {
    background: #1e5aa8;
}

.box2 .bd {
    box-sizing: border-box;
    padding-left: 48px;
    height: 243px;
    width: 1350px;
    overflow: hidden;
}

.box2 .sbUl li {
    width: 619px !important;
    box-shadow: 0px 0px 10px 3px #d9e4f4;
    border-radius: 9px;
    box-sizing: border-box;
    /* border: 2px solid #fff; */
    -webkit-border-radius: 9px;
    -moz-border-radius: 9px;
    -ms-border-radius: 9px;
    -o-border-radius: 9px;
    background: white;
    margin: 20px 17px ;
}

.box2 .sbUl li:hover {
    background: #f8f9fa;
    box-shadow: 0px 0px 5px 5px #d9e4f7;
}

.box2 .sbUl li a:hover {
    color: #0050ff;
}

.box2 .sbUl li .topBox {
    box-sizing: border-box;
    padding: 22px 17px 0 18px;
}

.box2 .sbUl li .topBox .sqSpan {
    width: 61px;
    height: 24px;
    line-height: 24px;
    padding-right: 7px;
    background: #28a745;
    color: white;
    border-radius: 12px;
    text-align: center;
}

.box2 .sbUl li .topBox .sqSpan.icon2 {
    background: #ffc107;
    color: #333;
}

.box2 .sbUl li .topBox .sqSpan.icon3 {
    background: #6c757d;
}

.box2 .sbUl li .topBox .linkA {
    width: 486px;
    height: 24px;
    line-height: 24px;
}

.box2 .sbUl li .topBox .lBox {
    width: 267px;
}

.box2 .sbUl li .topBox .fwP,
.box2 .sbUl li .topBox .timeP {
    height: 16px;
    line-height: 16px;
    padding-left: 22px;
    margin-top: 12px;
    box-sizing: border-box;
    color: #666;
}

.box2 .sbUl li .topBox .fwP:before {
    content: "🏢 ";
}

.box2 .sbUl li .topBox .timeP:before {
    content: "📅 ";
}

.box2 .sbUl li .topBox .spanBox {
    height: 30px;
}

.box2 .sbUl li .topBox .spanBox span {
    height: 30px;
    line-height: 30px;
    padding: 0 12px;
    color: #0050ff;
    background-color: #e4eaf9;
    margin-right: 9px;
    border-radius: 0 30px 30px 30px;
    -webkit-border-radius: 0 30px 30px 30px;
    -moz-border-radius: 0 30px 30px 30px;
    -ms-border-radius: 0 30px 30px 30px;
    -o-border-radius: 0 30px 30px 30px;
    max-width: 35%;
}

.box2 .sbUl li .topBox .spanBox span:last-child {
    margin-right: 0;
}

.box2 .sbUl li .topBox .numBox {
    width: 310px;
    height: 65px;
    margin-top: 14px;
}

.box2 .sbUl li .topBox .numBox.on {
    background: linear-gradient(135deg, #2c7be5 0%, #1e5aa8 100%);
    border-radius: 10px;
    color: white;
    padding: 10px;
}

.detailStatusName {
    padding-top: 25px;
    color: #999;
}

.detailStatusName i {
    color: #0052d9 !important;
    font-style: normal;
    font-size: 24px !important;
}

.f30 {
    font-size: 30px;
}

.f15 {
    font-size: 15px;
}

.text-ff8a00 {
    color: #ff6000;
}

.text-0050ff {
    color: #0050ff;
}

.text-grayB {
    color: #bbb;
}

.box2 .sbUl li .topBox .numBox>div {
    width: 49%;
    vertical-align: top;
}

.box2 .sbUl li .bottomBox {
    padding: 9px 0;
    background-color: #eef3fb;
    border-radius: 0 0 9px 9px;
    -webkit-border-radius: 0 0 9px 9px;
    -moz-border-radius: 0 0 9px 9px;
    -ms-border-radius: 0 0 9px 9px;
    -o-border-radius: 0 0 9px 9px;
    margin-top: 18px;
}

.box4 .calculateBtn {
    display: block;
    margin-top: 40px;
    width: 1402px;
    height: 122px;
    background: linear-gradient(135deg, #2c7be5 0%, #1e5aa8 100%);
    border-radius: 15px;
    color: white;
    text-align: center;
    line-height: 122px;
    font-size: 24px;
    font-weight: bold;
    text-decoration: none;
}

.box4 .calculateBtn:hover {
    background: linear-gradient(135deg, #1e5aa8 0%, #2c7be5 100%);
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(44, 123, 229, 0.3);
}

/* box2 end */

/* box4 start */
.box4 {
    width: 100%;
    height: 752px;
    min-width: 1400px;
    box-sizing: border-box;
    background: #fff;
    padding-top: 40px;
}

.box4 .leftBox {
    width: 685px;
}

.box4 .wjUl li {
    width: 685px;
    height: 99px;
    margin-top: 22px;
}

.box4 .wjUl .li {
    width: 685px;
    left: 0;
    bottom: 0;
    border-radius: 6px;
    box-sizing: border-box;
    padding: 20px 20px;
    /* border: 2px solid #fff; */
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -ms-border-radius: 6px;
    -o-border-radius: 6px;
    background: white;
    box-shadow: 0px 0px 10px 3px #deecfe;
}

.box4 .wjUl .li:nth-child(2n) {
    margin-right: 0;
}

.box4 .wjUl .li .paImg {
    width: 60px;
    height: 60px;
    right: -2px;
    top: -2px;
}

.box4 .wjUl .li .titleA {
    width: 100%;
    height: 24px;
    line-height: 24px;
}

.box4 .wjUl .li .iconDiv p {
    height: 16px;
    line-height: 16px;
    padding-left: 20px;
    margin-right: 15px;
    margin-top: 15px;
}

.box4 .wjUl .li .iconDiv p.icon1:before {
    content: "📋 ";
}

.box4 .wjUl .li .iconDiv p.icon2 {
    max-width: 400px;
}

.box4 .wjUl .li .iconDiv p.icon2:before {
    content: "🏷️ ";
}

.box4 .wjUl .li .iconDiv p.icon3 {
    margin-right: 0;
}

.box4 .wjUl .li .iconDiv p.icon3:before {
    content: "📅 ";
}

.box4 .wjUl .li:hover {
    box-shadow: 0px 0px 15px 8px #e7effb;
    background: #f8f9fa;
    transform: translateY(-2px);
}

.box4 .wjUl .li:hover .titleA {
    height: auto;
    white-space: normal;
    text-overflow: auto;
    word-break: break-word;
    color: #0052d9;
}

.box4 .rightBox {
    width: 685px;
    height: 422px;
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
}

.box4 .rightBox > .title {
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    color: #333;
    text-align: center;
    font-weight: bold;
}

.box4 .rightBox .moreTxt {
    display: block;
    width: 50px;
    color: #0052d9;
    margin: 13px auto 0;
    text-decoration: none;
}

/* box4 end */

/* box7 start */
.box7 {
    width: 100%;
    min-width: 1400px;
    box-sizing: border-box;
    padding-top: 40px;
    padding-bottom: 30px;
    background: #fff;
}

.box7 .banner2 {
    box-shadow: 0px 0px 16px 0px rgba(0, 82, 217, 0.2);
}

.box7 .rightBox{
    width: 685px;
}

.box7 .rightBox .videoBox a {
    width: 328px;
    height: 332px;
    padding: 20px 20px 0;
    box-sizing: border-box;
    border-radius: 9px;
    margin-right: 25px;
    background: white;
    box-shadow: 0px 0px 10px 3px #d9e4f7;
    margin-top: 20px;
}

.box7 .rightBox .videoBox a:nth-of-type(2){
    margin-right: 0px;
}

.box7 .rightBox .videoBox a:hover .liTitle {
    color: #0052d9;
}

.box7 .rightBox .videoBox a:hover{
    margin-top: 10px;
    transform: translateY(-10px);
}

.box7 .rightBox .videoBox a .imgSize {
    width: 100%;
    height: 192px;
    overflow: hidden;
}

.box7 .rightBox .videoBox a .imgSize p {
    z-index: 2;
    color: #fff;
    font-weight: bold;
    margin: 50px 30px 0;
    font-size: 18px;
    line-height: 24px;
    height: 72px;
}

.box7 .rightBox .videoBox a:hover .imgSize img {
    transform: scale(1.2);
}

.box7 .rightBox .videoBox a .imgSize img {
    width: 100%;
    height: 100%;
    border-radius: 8px;
}

.box7 .rightBox .videoBox a .liTitle {
    line-height: 30px;
    height: 60px;
    margin-top: 10px;
}

.box7 .rightBox .videoBox a .linkP {
    max-width: 150px;
    padding-left: 20px;
    height: 16px;
    line-height: 16px;
}

.box7 .rightBox .videoBox a .linkP:before {
    content: "🔗 ";
}

.box7 .rightBox .videoBox a .timeP {
    padding-left: 20px;
    height: 16px;
    line-height: 16px;
}

.box7 .rightBox .videoBox a .timeP:before {
    content: "📅 ";
}

/* box7 end */

/* 列表顶部筛选、面包屑 */
.selectBox{
    padding-top: 30px;
}

.selectBox .selectModule{
    width: 447px;
    height: 60px;
    line-height: 60px;
    border-radius: 5px;
    overflow: hidden;
    background: #fff;
    font-size: 16px;
    box-shadow: 0px 0px 10px 3px #deecfe;
}

.selectBox .selectModule .title{
    float: left;
    background-color: #f2f8fd;
    width: 120px;
    height: 60px;
    color: #333;
    font-style: italic;
    text-align: center;
}

.selectBox .selectModule.on .title{
    background:linear-gradient(to right, #1a65dd, #2081d4);
    color: #d9d9d9;
}

.selectBox .selectModule.on .name{
    background: linear-gradient(to right, #0052d9, #13b0be);
    color: #fff;
}

.selectBox .selectModule .name{
    float: left;
    width: 317px;
    color: #999;
    padding-left: 10px;
    cursor: pointer;
}

.selectBox .selectModule .name p{
    width: 80%;
}

.selectBox .selectList{
    top: 76px;
    z-index: 2;
}

.selectBox .selectList ul{
    min-width: 426px;
    height: 250px;
    overflow-y: scroll;
    background-color: #fff;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0px 0px 5px 2px #f3f7fd;
}

.selectBox .selectList ul::-webkit-scrollbar {
    width: 8px;
}

.selectBox .selectList ul::-webkit-scrollbar-thumb {
    width: 8px;
    height: 50px;
    border-radius: 25px;
    background: #eaf2ff;
}

.selectBox .selectList ul li{
    height: 50px;
    line-height: 50px;
    padding-left: 119px;
    padding-right: 25px;
    border-radius: 5px;
    color: #333;
    font-size: 16px;
    white-space: nowrap;
    cursor: pointer;
}

.selectBox .selectList ul li:hover{
    background: linear-gradient(to right, #0052d9, #13b0be);
    color: #fff;
}

.selectBox .selectList ul li.on{
    background: linear-gradient(to right, #0052d9, #13b0be);
    color: #fff;
}

/* 顶部导航栏、面包屑 */
.qdzct{
    display: block;
    position: fixed;
    width: 160px;
    height: 120px;
    background: linear-gradient(135deg, #2c7be5 0%, #1e5aa8 100%);
    top: 45%;
    right: 10px;
    z-index: 999;
    border-radius: 10px;
    color: white;
    text-align: center;
    line-height: 120px;
    text-decoration: none;
}

/* 联办 */
.lbOut{
    width: 100%;
    min-width: 1400px;
    height: 276px;
    background: url(../images/lb_bg.jpg) no-repeat top center;
    padding-top: 35px;
}

.lbOut ul{
    height: 212px;
    width: 1422px;
    position: absolute;
    bottom: 30px;
    left: 0;
}

.lbOut ul li{
    width: 264px;
    height: 170px;
    background: url(../images/baseEnter.png) no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
    margin-top: 42px;
    box-shadow: 0px 0px 10px 0px rgba(0, 82, 217, 0.14);
    margin-right: 20px;
    position: relative;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.lbOut ul li .tips{
    position: absolute;
    width: 49px;
    height: 50px;
    display: block;
    top: 0;
    right: 0;
    background: url(../images/tips.png) no-repeat;
}

.lbOut ul li.on .tips{
    background: url(../images/tipson.png) no-repeat;
}

.lbOut ul li.on{
    height: 212px;
    background: linear-gradient(135deg, #2c7be5 0%, #1e5aa8 100%);
    margin-top: 0px;
    color: white;
}

.lbOut ul li p{
    font-size: 18px;
    text-align: center;
    line-height: 30px;
    height: 60px;
    padding-top: 60px;
}

.lbOut ul li.on p{
    color: #fff;
}

.lbOut ul li div a{
    display: none;
}

.lbOut ul li.on div a{
    display: block;
    width: 80px;
    height: 30px;
    line-height: 30px;
    background: #fff;
    text-align: center;
    color: #0052d9;
    border-radius: 50px;
    margin: 14px auto 0;
    text-decoration: none;
}

.lbOut ul li.on div a:hover{
    box-shadow: inset 0 0 10px #2478ff;
}

.lbOut ul li .icon1,
.lbOut ul li .icon2,
.lbOut ul li .icon3,
.lbOut ul li .icon4,
.lbOut ul li .icon5,
.lbOut ul li .icon6,
.lbOut ul li .icon7{
    padding-top: 94px;
    background-size: 26%;
    background-repeat: no-repeat;
    background-position: center 19px;
}

.lbOut ul li .icon1{
    background-image: url(../images/baseEnterIcon1.png);
}

.lbOut ul li.on .icon1{
    background-image: url(../images/baseEnterIcon1on.png);
}

.lbOut ul li .icon2{
    background-image: url(../images/baseEnterIcon2.png);
}

.lbOut ul li.on .icon2{
    background-image: url(../images/baseEnterIcon2on.png);
}

.lbOut ul li .icon3{
    background-image: url(../images/baseEnterIcon3.png);
}

.lbOut ul li.on .icon3{
    background-image: url(../images/baseEnterIcon3on.png);
}

.lbOut ul li .icon4{
    background-image: url(../images/baseEnterIcon4.png);
}

.lbOut ul li.on .icon4{
    background-image: url(../images/baseEnterIcon4on.png);
}

.lbOut ul li .icon5{
    background-image: url(../images/baseEnterIcon5.png);
}

.lbOut ul li.on .icon5{
    background-image: url(../images/baseEnterIcon5on.png);
}

.lbOut ul li .icon6{
    background-image: url(../images/baseEnterIcon6.png);
}

.lbOut ul li.on .icon6{
    background-image: url(../images/baseEnterIcon6on.png);
}

.lbOut ul li .icon7{
    background-image: url(../images/baseEnterIcon7.png);
}

.lbOut ul li.on .icon7{
    background-image: url(../images/baseEnterIcon7on.png);
}

/* 添加悬浮效果 */
.lbOut ul li:hover{
    height: 212px;
    background: url(../images/baseEnterOn.png) no-repeat;
    background-size: 100% 100%;
    margin-top: 0px;
}

.lbOut ul li:hover .tips{
    background: url(../images/tipson.png) no-repeat;
}

.lbOut ul li:hover p{
    color: #fff;
}

.lbOut ul li:hover .icon1{
    background-image: url(../images/baseEnterIcon1on.png);
}

.lbOut ul li:hover .icon2{
    background-image: url(../images/baseEnterIcon2on.png);
}

.lbOut ul li:hover .icon3{
    background-image: url(../images/baseEnterIcon3on.png);
}

.lbOut ul li:hover .icon4{
    background-image: url(../images/baseEnterIcon4on.png);
}

.lbOut ul li:hover .icon5{
    background-image: url(../images/baseEnterIcon5on.png);
}

.lbOut ul li:hover .icon6{
    background-image: url(../images/baseEnterIcon6on.png);
}

.lbOut ul li:hover .icon7{
    background-image: url(../images/baseEnterIcon7on.png);
}

.lbOut ul li:hover div a{
    display: block;
    width: 80px;
    height: 30px;
    line-height: 30px;
    background: #fff;
    text-align: center;
    color: #0052d9;
    border-radius: 50px;
    margin: 14px auto 0;
    text-decoration: none;
}

/* 残疾人 start */
.alertBg1,.alertBg2,.alertBg3{
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: rgba(0,0,0,0.5);
    z-index: 20001;
}

.alertCont{
    width: 800px;
    background-color: #ffffff;
    margin: 5% auto 0;
    border-radius: 5px;
    overflow: hidden;
}

.alertTitle{
    line-height: 67px;
    color: #ffffff;
    font-size: 20px;
    font-weight: bold;
    background: linear-gradient(135deg, #2c7be5 0%, #1e5aa8 100%);
    text-align: center;
    position: relative;
}

.alertClose{
    width: 15px;
    height: 15px;
    position: absolute;
    right: 20px;
    top: 26px;
}

.alertClose:hover{
    cursor: pointer;
}

.alertClose img{
    display: block;
    width: 100%;
}

.alertUl {
    background: #f8f9fa;
    padding-bottom: 30px;
    height: 600px;
}

.alertBtnCont{
    width: 343px;
    margin: 0 auto;
}

.alertBtn{
    width: 160px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    border-radius: 50px;
    text-decoration: none;
}

.alertCont .infos{
    padding: 22px 30px;
    line-height: 28px;
    font-size: 16px;
}

.btnCancel{
    border: 1px solid #8db6f2;
    color: #1569e6;
    background-color: #e7f1fc;
}

.btnSubmit{
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
    color: #ffffff;
}

.alertBg1 .zcyw{
    width: 740px;
    height: 150px;
    background: linear-gradient(135deg, #2c7be5 0%, #1e5aa8 100%);
    margin: 0 auto 30px;
    border-radius: 10px;
    padding: 20px;
}

.alertBg1 .titleBox span{
    font-size: 18px;
    color: #fff;
    line-height: 40px;
    margin-top: 20px;
    margin-left: 38px;
}

.alertBg1 .titleBox a{
    line-height: 40px;
    margin-top: 20px;
    margin-left: 30px;
    font-size: 18px;
    font-weight: bold;
    text-decoration: underline;
    color: #fff;
}

.alertBg1 .phoneBoxs{
    padding-left: 119px;
    font-size: 16px;
    line-height: 24px;
    margin-top: 42px;
}

.alertBg1 .phoneBoxs:before{
    content: "📞 ";
    margin-right: 10px;
}

.alertBg1 .phoneBoxs span{
    font-size: 24px;
    font-weight: bold;
    color: #fe6a1c;
    font-style: italic;
}

/* 残疾人 end */

/* 退役军人 start */
.alertBg2 .titlesp{
    font-size: 16px;
    font-weight: bold;
    color: #0052d9;
}

.alertBg2 .zcyw{
    width: 740px;
    height: 280px;
    background: linear-gradient(135deg, #2c7be5 0%, #1e5aa8 100%);
    margin: 0 auto 30px;
    border-radius: 10px;
    padding: 20px;
}

.alertBg2 .titleBox span{
    font-size: 18px;
    color: #fff;
    line-height: 40px;
    margin-top: 20px;
    margin-left: 38px;
}

.alertBg2 .titleBox a{
    line-height: 40px;
    margin-top: 20px;
    margin-left: 30px;
    font-size: 18px;
    font-weight: bold;
    text-decoration: underline;
    color: #fff;
}

.alertBg2 .phoneBoxs{
    width: 300px;
    height: 40px;
    line-height: 40px;
    background: #fff;
    padding-left: 54px;
    border-radius: 50px;
    margin: 30px auto 0;
}

.alertBg2 .phoneBoxs:before{
    content: "📞 ";
    margin-right: 10px;
}

.alertBg2 .phones li{
    width: 218px;
    margin-left: 28px;
    line-height: 30px;
}

.alertBg2 .phones li span{
    color: #fe6a1c;
    font-weight: bold;
}

/* 退役军人 end */

/* 办事指南 start */
.alertBg3 .alertCont{
    width: 600px;
    height: 360px;
}

.alertBg3 .alertUl{
    height: 263px;
}

.alertBg3 ul{
    padding: 10px 30px 0;
}

.alertBg3 li{
    padding:20px 0 20px 120px;
    height: 80px;
    border-bottom: 1px dashed #f5f9fd;
}

.alertBg3 li:before{
    content: "🔗 ";
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.alertBg3 li p{
    line-height: 30px;
    height: 30px;
    font-size: 18px;
    margin-bottom: 13px;
}

.alertBg3 li a{
    display: block;
    width: 121px;
    height: 29px;
    line-height: 29px;
    color: #0052d9;
    border: 1px solid #0052d9;
    border-radius: 50px;
    padding-left: 15px;
    background: #fff;
    text-decoration: none;
}

/* 办事指南 end */

/* 通用样式 */
.none { display: none !important; }
.block { display: block !important; }
.inlineblock { display: inline-block !important; }
.fl { float: left; }
.fr { float: right; }
.clearfix:after { content: ""; display: table; clear: both; }
.pr { position: relative; }
.pa { position: absolute; }
.tc { text-align: center; }
.fb { font-weight: bold; }
.cp { cursor: pointer; }
.transi { transition: all 0.3s ease; }
.width100 { width: 100%; }
.mt5 { margin-top: 5px; }
.mt10 { margin-top: 10px; }
.mt15 { margin-top: 15px; }
.mt20 { margin-top: 20px; }
.mt40 { margin-top: 40px; }
.mb20 { margin-bottom: 20px; }
.mb30 { margin-bottom: 30px; }
.ml10 { margin-left: 10px; }
.mr10 { margin-right: 10px; }
.mr25 { margin-right: 25px; }
.pb30 { padding-bottom: 30px; }
.f16 { font-size: 16px; }
.f18 { font-size: 18px; }
.f20 { font-size: 20px; }
.f24 { font-size: 24px; }
.text-white { color: white; }
.text-gray6 { color: #666; }
.text-gray9 { color: #999; }
.textoverflow {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.paraoverflow2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}
.paraoverflow3 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}
.scroll-bar {
    overflow-y: auto;
}
.scroll-bar::-webkit-scrollbar {
    width: 6px;
}
.scroll-bar::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
}
.scroll-bar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

/* 响应式设计 */
@media screen and (min-width: 1920px) {
    .bannerBg {
        background-size: 100% 100%;
    }
    .box2 {
        background-size: 100% 100%;
    }
    .box4 {
        background-size: 100% 100%;
    }
}

.box4 .wjUl .li:hover {
    box-shadow: 0px 0px 15px 8px #e7effb;
    background: #f8f9fa;
    transform: translateY(-2px);
}

.box4 .wjUl .li:hover .titleA {
    height: auto;
    white-space: normal;
    /* pre-wrap */
    text-overflow: auto;
    word-break: break-word;
}

.box4 .wjUl .li:hover .titleA {
    color: #0052d9;
}

.box4 .rightBox {
    width: 685px;
    height: 422px;
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
}

.box4 .rightBox > .title {
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    color: #333;
    text-align: center;
    font-weight: bold;
}

.box4 .rightBox .moreTxt {
    display: block;
    width: 50px;
    color: #0052d9;
    margin: 13px auto 0;
    text-decoration: none;
}

.box4 .hd {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
}

.box4 .hd a {
    position: absolute;
    height: 30px;
    width: 30px;
    top: 146px;
}

.box4 .hd a.prev {
    left: 20px;
    background: #2c7be5;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.box4 .hd a.prev:hover {
    background: #1e5aa8;
}

.box4 .hd a.next {
    right: 20px;
    background: #2c7be5;
    border-radius: 50%;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
}

.box4 .hd a.next:hover {
    background: #1e5aa8;
}

.box4 .rightBox .tempWrap{
    margin-left: 60px;
}

.box4 .rightBox .videoBox a {
    width: 565px;
    height: 318px;
    border-radius: 9px;
    overflow: hidden;
    position: relative;
}

.box4 .rightBox .videoBox .title{
    position: absolute;
    width: 480px;
    height: 50px;
    line-height: 50px;
    padding: 0 55px 0 10px;
    z-index: 10;
    bottom: 10px;
    left: 10px;
    color: #fff;
    font-size: 18px;
    background: rgba(0,0,0,0.7);
    border-radius: 25px;
}

.box4 .rightBox .videoBox a video{
    width: 565px;
    height: 318px;
}

.box7 .rightBox{
    width: 685px;
}

.box7 .rightBox .videoBox a {
    width: 328px;
    height: 332px;
    padding: 20px 20px 0;
    box-sizing: border-box;
    border-radius: 9px;
    margin-right: 25px;
    background: white;
    box-shadow: 0px 0px 10px 3px #d9e4f7;
    margin-top: 20px;
}

.box7 .rightBox .videoBox a:nth-of-type(2){
    margin-right: 0px;
}

.box7 .rightBox .videoBox a:hover .liTitle {
    color: #0052d9;
}

.box7 .rightBox .videoBox a:hover{
    margin-top: 10px;
    transform: translateY(-10px);
}

.box7 .rightBox .videoBox a .imgSize {
    width: 100%;
    height: 192px;
    overflow: hidden;
}

.box7 .rightBox .videoBox a .imgSize p {
    z-index: 2;
    color: #fff;
    font-weight: bold;
    margin: 50px 30px 0;
    font-size: 18px;
    line-height: 24px;
    height: 72px;
}

.box7 .rightBox .videoBox a:hover .imgSize img {
    transform: scale(1.2);
    -webkit-transform: scale(1.2);
    -moz-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -o-transform: scale(1.2);
    z-index: 1;
}

.box7 .rightBox .videoBox a .imgSize img {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
}

.box7 .rightBox .videoBox a .liTitle {
    line-height: 30px;
    height: 60px;
    margin-top: 10px;
}

.box7 .rightBox .videoBox a .linkP {
    max-width: 150px;
    padding-left: 20px;
    height: 16px;
    line-height: 16px;
}

.box7 .rightBox .videoBox a .linkP:before {
    content: "🔗 ";
}

.box7 .rightBox .videoBox a .timeP {
    padding-left: 20px;
    height: 16px;
    line-height: 16px;
}

.box7 .rightBox .videoBox a .timeP:before {
    content: "📅 ";
}
