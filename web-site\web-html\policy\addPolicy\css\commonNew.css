/*== 安全区域 ==*/
.conAuto{ width: 1200px; min-width: 1200px; margin-left: auto; margin-right: auto; }/* 安全区域 */
.conAuto2{ width: 1400px; min-width: 1400px; margin-left: auto; margin-right: auto;}
.minwidth{ min-width: 1200px; }
/*== 布局 ==*/
.tl{ text-align: left; }
.tc{ text-align: center; }
.tr{ text-align: right; }
.pr{ position: relative; }
.pa{ position: absolute; }
.pf{ position: fixed; }
.fl{ float: left; }
.fr{ float: right; }
.block{ display: block; }
.displayTable{ display: table; }
.displayTableCell{ display: table-cell; }
.inlineblock{ display: inline-block; }
.none{ display: none; }
.hidden{ visibility: hidden; }
.zoom{zoom: 1;}
.width100{ width: 100%;}
.bc{ margin-left: auto; margin-right: auto; }/*块水平居中*/
.vm{ vertical-align: middle; }
.imgVertical{height: 100%; width: 0; display: inline-block; vertical-align: middle;}/* 图片水平垂直居中 */
/*== 鼠标状态==*/
.cp{cursor: pointer; }
.cd{cursor: default;}
/*== 字号 ==*/
.f12{ font-size: 12px; }
.f13{ font-size: 13px; }
.f14{ font-size: 14px; }
.f15{ font-size: 15px; }
.f16{ font-size: 16px; }
.f18{ font-size: 18px; }
.f20{ font-size: 20px; }
.f24{ font-size: 24px; }
.f36{ font-size: 36px; }
.fb{ font-weight: bold; }
.fn{ font-weight: normal; }
/*== 字色 ==*/
.text-white{ color: #fff; }
.text-gray3{ color: #333; }
.text-gray6{ color: #666; }
.text-gray9{ color: #999; }
.text-blue{ color: #1569e6; }
.text-red{ color: #EF205A; }
.text-orange{color: #f28b11;}
/*== 宽度 ==*/
.w80{width: 80px;}
.w280{width: 280px;}
.w700{width: 700px;}
/*== 背景色 ==*/
.bg-white{background-color: #fff;}
/*== 圆角 ==*/
.br2{ border-radius: 2px; }
.br4{ border-radius: 4px; }
.br6{ border-radius: 6px; }
.br8{ border-radius: 8px; }
.br10{ border-radius: 10px; }
.circle{ border-radius: 50%; }
/*== 内外边距 ==*/
.m5{margin: 5px;}
.m10{margin: 10px;}
.m15{margin: 15px;}
.m30{margin: 30px;}
.mt5{margin-top: 5px;}
.mt2{margin-top: 2px;}
.mt10{margin-top: 10px;}
.mt15{margin-top: 15px;}
.mt20{margin-top: 20px;}
.mt25{margin-top: 25px;}
.mt30{margin-top: 30px;}
.mt35{margin-top: 35px;}
.mt40{margin-top: 40px;}
.mt50{margin-top: 50px;}
.mt100{margin-top: 100px;}
.mb5{margin-bottom: 5px;}
.mb10{margin-bottom: 10px;}
.mb15{margin-bottom: 15px;}
.mb20{margin-bottom: 20px;}
.mb25{margin-bottom: 25px;}
.mb30{margin-bottom: 30px;}
.mb35{margin-bottom: 35px;}
.mb40{margin-bottom: 40px;}
.mb45{margin-bottom: 45px;}
.mb50{margin-bottom: 50px;}
.mb100{margin-bottom: 100px;}
.ml5{margin-left: 5px;}
.ml10{margin-left: 10px;}
.ml15{margin-left: 15px;}
.ml20{margin-left: 20px;}
.ml25{margin-left: 25px;}
.ml30{margin-left: 30px;}
.ml35{margin-left: 35px;}
.ml40{margin-left: 40px;}
.ml50{margin-left: 50px;}
.ml100{margin-left: 100px;}
.mr5{margin-right: 5px;}
.mr10{margin-right: 10px;}
.mr15{margin-right: 15px;}
.mr20{margin-right: 20px;}
.mr25{margin-right: 25px;}
.mr30{margin-right: 30px;}
.mr35{margin-right: 35px;}
.mr40{margin-right: 40px;}
.mr50{margin-right: 50px;}
.mr100{margin-right: 100px;}
.p20{ padding: 20px; }
.p5{padding: 5px;}
.p10{padding: 10px;}
.p15{padding: 15px;}
.p30{padding: 30px;}
.p40{padding: 40px;}
.p50{padding: 50px;}
.pt5{padding-top: 5px;}
.pt10{padding-top: 10px;}
.pt15{padding-top: 15px;}
.pt20{padding-top: 20px;}
.pt25{padding-top: 25px;}
.pt30{padding-top: 30px;}
.pt35{padding-top: 35px;}
.pt40{padding-top: 40px;}
.pt50{padding-top: 50px;}
.pt100{padding-top: 100px;}
.pb5{padding-bottom: 5px;}
.pb10{padding-bottom: 10px;}
.pb15{padding-bottom: 15px;}
.pb20{padding-bottom: 20px;}
.pb25{padding-bottom: 25px;}
.pb30{padding-bottom: 30px;}
.pb35{padding-bottom: 35px;}
.pb40{padding-bottom: 40px;}
.pb50{padding-bottom: 50px;}
.pb100{padding-bottom: 100px;}
.pl5{padding-left: 5px;}
.pl10{padding-left: 10px;}
.pl15{padding-left: 15px;}
.pl20{padding-left: 20px;}
.pl25{padding-left: 25px;}
.pl30{padding-left: 30px;}
.pl35{padding-left: 35px;}
.pl40{padding-left: 40px;}
.pl50{padding-left: 50px;}
.pl100{padding-left: 100px;}
.pr5{padding-right: 5px;}
.pr10{padding-right: 10px;}
.pr15{padding-right: 15px;}
.pr20{padding-right: 20px;}
.pr25{padding-right: 25px;}
.pr30{padding-right: 30px;}
.pr35{padding-right: 35px;}
.pr40{padding-right: 40px;}
.pr50{padding-right: 50px;}
.pr100{padding-right: 100px;}
/*=== 文本溢出隐藏 单行 ===*/
.textoverflow{
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	word-break: break-all;
}
/*=== 文本溢出隐藏 多行 ===*/
.paraoverflow2{
	display: -webkit-box !important;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
	-webkit-box-orient:vertical;
	-webkit-line-clamp: 2;
}
.paraoverflow3{
	display: -webkit-box !important;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
	-webkit-box-orient:vertical;
	-webkit-line-clamp: 3;
}
.paraoverflow4{
	display: -webkit-box !important;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
	-webkit-box-orient:vertical;
	-webkit-line-clamp: 4;
}
/*== 动画 ==*/
.transi{/* 过度动画 */
	-webkit-transition: all .3s;
	-moz-transition: all .3s;
	-ms-transition: all .3s;
	-o-transition: all .3s;
	transition: all .3s;
}
.scale:hover{/* 鼠标划上放大效果 */
	-webkit-transform: scale(1.2,1.2);
	-moz-transform: scale(1.2,1.2);
	-ms-transform: scale(1.2,1.2);
	-o-transform: scale(1.2,1.2);
	transform: scale(1.2,1.2);
}
.upward:hover{/* 鼠标划上上升效果 */
	-webkit-transform:translate(0,-10px);
	-moz-transform:translate(0,-10px);
	-ms-transform:translate(0,-10px);
	-o-transform:translate(0,-10px);
	transform:translate(0,-10px);
}
/* 按钮 样式*/
.btn {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
	margin-right: 11px;
}
.btn-default,
.btn-default.btn[disabled]:hover{
    color: #333;
    background-color: #fff;
    border-color: #ccc;
}
.btn-default:hover {
    color: #333;
    background-color: #e6e6e6;
    border-color: #adadad;
}
.btn-primary,
.btn-primary.btn[disabled]:hover{
    color: #fff;
    background-color: #337ab7;
    border-color: #2e6da4;
}
.btn-primary:hover {
    color: #fff;
    background-color: #286090;
    border-color: #204d74;
}
.btn-success,
.btn-success.btn[disabled]:hover{
    color: #fff;
    background-color: #5cb85c;
    border-color: #4cae4c;
}
.btn-success:hover {
    color: #fff;
    background-color: #449d44;
    border-color: #398439;
}
.btn-warning,
.btn-warning.btn[disabled]:hover {
    color: #fff;
    background-color: #f0ad4e;
    border-color: #eea236;
}
.btn-warning:hover {
    color: #fff;
    background-color: #ec971f;
    border-color: #d58512;
}
.btn-danger,
.btn-danger.btn[disabled]:hover {
    color: #fff;
    background-color: #d9534f;
    border-color: #d43f3a;
}
.btn-danger:hover {
    color: #fff;
    background-color: #c9302c;
    border-color: #ac2925;
}
.btn[disabled]{
    cursor: not-allowed;
    filter: alpha(opacity=65);
    -webkit-box-shadow: none;
    box-shadow: none;
    opacity: .65;
}
/*********************************** 本项目公共样式 start ***********************************/
body{
	width: 100%;
	height: 100%;
}
/* 输入框placeholder */
input::-webkit-input-placeholder{
	color:#969696;
}
input::-webkit-moz-placeholder{
	color:#969696;
}
input::-webkit-ms-placeholder{
	color:#969696;
}
textarea::-webkit-input-placeholder{
	color:#969696;
}
textarea::-webkit-moz-placeholder{
	color:#969696;
}
textarea::-webkit-ms-placeholder{
	color:#969696;
}
.HeaderWrap {
	height: 39px;
	line-height: 39px;
	min-width: 1400px;
	background: #0052d9;
	background-size: 100% 100%;
	border-bottom: 1px solid rgba(255, 255, 255,0.2);
}
/* 顶部 */
.headersOut{
	height: 110px;
	background: #0052d9;
	width: 100%;
	min-width: 1400px;
}
.conAuto1400{
	width: 1400px;
	min-width: 1400px;
	margin: 0 auto;
}
.HeaderWrap {
	height: 39px;
	line-height: 39px;
	min-width: 1400px;
	border-bottom: 1px solid #1862db;
}
.HeaderWrap .loginBtn{
	width: 100px;
	height: 40px;
	line-height: 40px;
	text-align: center;
	color: #fff;
}
.HeaderWrap .registerBtn{
	width: 100px;
	height: 40px;
	line-height: 40px;
	text-align: center;
	color: #fff;
	background: #ff8a00;
}
.afterLogin {
	z-index: 9;
	padding: 0 10px;
}
.HeaderWrap .afterLogin .loginUser{
	display: inline-block;
	/* max-width: 98px; */
	padding: 0 0 0 35px;
	background: url(../../index/indexImg/userNameIcon.png) no-repeat left center;
	color: #fff;
}
.HeaderWrap .afterLogin .afterLoginBox em{
	display: block;
	width: 14px;
    height: 8px;
    top: -7px;
	left: 50%;
	margin-left: -4px;
	background: url(.././images/icons/header_topArr.png) no-repeat;
}
.HeaderWrap .lineBpx{
	font-size: 10px;
	color: #999;
}
.newsBtnTop{
	display: inline-block;
	width: 16px;
	height: 16px;
	background: url(.././images/icons/header_news.png) no-repeat;
	margin: 12px 10px 0 0;
}
.newsBtnTop em{
	min-width: 12px;
	height: 13px;
	line-height: 13px;
	color: #fff;
	text-align: center;
	border-radius: 50px;
	font-size: 12px;
	background: #ef205a;
	top: -8px;
	right: -10px;
	padding: 0 2px;
}
.afterLoginBox {
	left: 50%;
	margin-left: -58px;
	top:39px;
	background: #fff;
	width: 99px;
	padding: 0 10px;
	border-radius: 5px;
	box-shadow: 0px 1px 11px #b7caff;
}

.afterLoginBox li {
	border-bottom: 1px solid #eee;
}
.afterLoginBox li.nobd{
	border: none;
}
.afterLoginBox li a {
	display: block;
	text-align: center;
	width: 99px;
	height: 34px;
	line-height: 34px;
}

.afterLoginBox li a:hover {
	color: #1569e6;
}
.welcome_p01 {
    color: #fff;
}
.welcome_p01 span{
	color: #ff8a00;
	font-weight: bold;
	padding-right: 6px;
}
.welcome_a {
    display: block;
    color: #fff;
    height: 40px;
    line-height: 40px;
    padding: 0 20px;
}
.welcome_a:hover,.webvnavBbox:hover,.afterLogin:hover{
	background-color: #0052d9;
}
/* 网站导航 */
.webvnavBbox {
    width: 78px;
    padding-left: 20px;
	padding-right: 10px;
}
.webvnavBbox a{
	padding-right: 15px;
	background: url(../images/icons/new_barArr.png) no-repeat right center;
}
.siteNav_Box {
    background: #fff;
    width: 1400px;
    left: 0;
    top:39px;
    border-radius: 5px;
    z-index: 103;
	box-shadow: 0px 1px 11px #b7caff;
}
.siteNav_Box em {
    display: block;
    width: 14px;
    height: 8px;
    top: -7px;
    right: 230px;
    margin-left: -4px;
    background: url(.././images/icons/header_topArr.png) no-repeat;
}
.siteNav_Box dl{
	/* width: 150px; */
	/* 创业者联盟 */
	width: 172px;
	padding: 20px 0 24px;
	margin: 0;
}
.siteNav_Box dl:hover{
	background: linear-gradient(to bottom, #fff 0%,  #d9e5fb 100%);
}
.siteNav_Box dl dt a:hover {
    color: #0052d9;
}
.siteNav_Box dl dd{
	padding-left: 60px;
	height: 360px;
}
.siteNav_Box dl dd a:hover {
    color: #0052d9;
}

.site_title {
    height: 21px;
    line-height: 21px;
	padding-left: 60px;
	font-size: 18px;
	font-weight: bold;
	margin-bottom: 10px;
}
.site_title.headericon1{
	background: url(.././images/icons/new_wzdh1.png) no-repeat 20px center;
}
.site_title.headericon2{
	background: url(.././images/icons/new_wzdh2.png) no-repeat 20px center;
}
.site_title.headericon3{
	background: url(.././images/icons/new_wzdh3.png) no-repeat 20px center;
}
.site_title.headericon4{
	background: url(.././images/icons/new_wzdh4.png) no-repeat 20px center;
}
.site_title.headericon5{
	background: url(.././images/icons/new_wzdh5.png) no-repeat 20px center;
}
.site_title.headericon6{
	background: url(.././images/icons/new_wzdh6.png) no-repeat 20px center;
}
.site_title.headericon7{
	background: url(.././images/icons/new_wzdh7.png) no-repeat 20px center;
}
.site_title.headericon8{
	background: url(.././images/icons/new_wzdh8.png) no-repeat 20px center;
}
.site_title.headericon9{
	background: url(.././images/icons/new_wzdh9.png) no-repeat 20px center;
}
.siteNav_Box dd a {
    height: 40px;
    line-height: 40px;
    width: 100%;
    padding: 0;
}
/* 法律声明 */
.popDeclare .declareUpcon {
    width: 848px;
    height: 600px;
    padding-top: 55px;
    background: url(.././images/pics/popdeclare01.png) no-repeat center;
}

.popDeclare.zh_popUpbg {
    z-index: 9999;
}

.popDeclare .declareUpcon .declaremainBox .titleBox {
    margin-left: 45px;
}

.popDeclare .declareUpcon .declaremainBox .declareCon {
    width: 720px;
    height: 381px;
    border-radius: 4px;
    background-color: #fff;
    margin-left: 45px;
    margin-top: 30px;
    padding: 20px;
}

.lh32 {
    line-height: 32px;
}

.lookDeclare {
    display: block;
    width: 240px;
    height: 46px;
    line-height: 46px;
    background: #cccccc;
    border-radius: 30px;
    margin: 0 auto;
    color: #fff;
    text-align: center;
    font-size: 16px;
    margin-top: 25px;
}

.bgBlue {
    background: #3389ff;
}

/* 头部 */

/* 菜单 */
#navbar{
	width: 100%;
	min-width: 1400px;
	height: 70px;
	line-height: 70px;
	overflow: hidden;
}
#navbar .logoBox{
	width: 405px;
	height: 70px;
	margin-right: 25px;
}
#navbar .logoBox h1{
	display: block;
	width: 100%;
	height: 100%;
	background-size: 100% auto;
	color: #ffffff;
	font-size: 28px;
	font-weight: bold;
	line-height: 70px;
	text-align: left;
	padding-left: 20px;
	margin: 0;
}
#navbar .navCon{
	/* width: 960px; */
	margin: 0 auto;
}
#navbar .navCon .navLi  {
	height: 70px;
	padding: 0 26px;
	text-align: center;
	float: left;
}
#navbar .navCon .navLi ul{
	display: none;
	width: 100%;
	/* width: 110px; */
	background: #1569e6;
	position: relative;
	z-index: 1000;
	left: 0px;
	top: 0px;
}
#navbar .navCon .navLi ul li {
	height: 70px;
	line-height: 70px;
	border-bottom: 1px dashed #3383ff;
}
#navbar .navCon .navLi.on a,
#navbar .navCon .navLi:hover a{
	color: #ff8a00;
	border-bottom: 2px solid #ff8a00;
}
#navbar .navCon .navLi li a:hover{
	background: #1658bd;
	color: #fff;
}
#navbar .navCon .navLi>a{
	height: 68px;
	line-height: 68px;
	color: #fff;
	font-size: 18px;
	display: block;
	width: 100%;
	font-weight: bold;
	transition: background-color .3s;
	-moz-transition: background-color .3s;
	-ms-transition: background-color .3s;
	-webkit-transition: background-color .3s;
	-o-transition: background-color .3s;
}
#navbar .ccPop{
	position: absolute;
	display: block;
	width: 191px;
	height: 106px;
	top: -36px;
    right: -76px
}
#navbar .navCon .current{
	background: #00a0e9 url(../../images/navborder.jpg) no-repeat right center;
}
.nodataPic{
	width: 338px;
    height: 299px;
    background: url(../images/pics/noDataPic.png) no-repeat 50%;
    margin: 80px auto;
}
/* 底部 */
.footerBar{
	height: 215px;
	width: 100%;
	min-width: 1400px;
	background: url(../images/icons/footerBg.jpg) repeat;
	background-size: 100% 100%;
}
.footerMain{
	width: 1400px;
	min-width: 1400px;
	margin: 0 auto;
}
.footerMain .linkBox{
	line-height: 56px;
	height: 56px;
	border-bottom: 1px dashed #273a77;
	/* text-align: center; */
}
.footerMain .linkBox a{
	color: #fff;
	font-size: 16px;
}
.footerMain .linkBox em{
	color: #fff;
	padding: 0 45px;
}
.footerMain .linkBox select{
	background: none;
	border: none;
	color: #fff;
	font-size: 16px;
}
.footerMain .linkBox select option{
	color: #333;
}
.footerMain .bottomBoxfoot{
	line-height: 34px;
	color: #8592bf;
}
.footerMain .bottomBoxfoot .textBox{
	width: 60%;
}
.footerMain .bottomBoxfoot .textBox a{
	color: #8592bf;
	margin-right: 25px;
}
.footerMain .codeBox{
	width: 180px;
	height: 100px;
	background: url(../images/pics/footerCodeBg.png) no-repeat;
	margin-top: 10px;
}
.footerMain .codeBox img{
	width: 80px;
	height: 80px;
	margin: 10px 0 0 10px;
}
.footerMain .codeBox p{
	width: 86px;
    line-height: 30px;
    color: #fff;
    margin: 21px 0 0 4px;
	text-align: center;
}
.footerMain .picBox1{
	width: 80px;
	height: 80px;
	background: url(../images/pics/dzjg.png);
	margin-top: 20px;
}
.footerMain .picBox2{
	margin-top: 20px;
	width: 110px;
	height: 55px;
	background: url(../images/pics/jiucuo.png);
}
/* .footerBar{
	height: 180px;
	width: 100%;
	min-width: 1400px;
	background: url(../images/icons/footerBg.jpg) repeat;
}
.footerMain{
	width: 1400px;
	min-width: 1400px;
	margin: 0 auto;
}
.footerMain .linkBox{
	line-height: 56px;
	height: 56px;
	border-bottom: 1px dashed #273a77;
}
.footerMain .linkBox a{
	color: #fff;
}
.footerMain .linkBox em{
	color: #fff;
	padding: 0 28px;
}
.footerMain .bottomBoxfoot{
	line-height: 34px;
	color: #8592bf;
}
.footerMain .codeBox{
	width: 180px;
	height: 100px;
	background: url(../images/pics/footerCodeBg.png) no-repeat;
	margin-top: 10px;
}
.footerMain .codeBox img{
	width: 80px;
	height: 80px;
	margin: 10px 0 0 10px;
}
.footerMain .codeBox p{
	width: 71px;
	line-height: 30px;
	color: #fff;
	margin: 21px 0 0 10px;
} */
/* 底部 end */

/* 浏览路径 */
.pathbox{
	height: 40px;
	line-height: 40px;
	/* background: #f4f7fc; */
	width: 100%;
	min-width: 1200px;
}
.pathbox .pathPic{
	padding-left: 29px;
	background: url(../images/icons/pathHome2.png) no-repeat 3px center;
}
.pathbox span,.pathbox a{
	color: #586a7b;
}
.pathbox a:hover{
	color: #1569e6;
}
/* 浏览路径 新 */
.pathbox2{
	padding-top: 1px;
	height: 40px;
	line-height: 40px;
	width: 100%;
	min-width: 1200px;
}
.pathbox2 .pathPic{
	padding-left: 29px;
	background: url(../images/icons/pathHome2.png) no-repeat 3px center;
}
.pathbox2 span,.pathbox2 a{
	color: #586a7b;
}
.pathbox2 a:hover{
	color: #1569e6;
}
/* 筛选条件 start */
.filterContainer .filterOut {
    /* background-color: #fff; */
    padding: 10px 0;
    width: 1400px;
	min-width: 1400px;
}
.filterContainer ul li .filterTitle{
    text-align: right;
    display: inline-block;
    height: 54px;
	line-height: 54px;
}
.filterContainer ul li .filterBar{
    width: 1320px;
    line-height: 32px;
    padding: 11px 0;
}
.filterContainer .filterBox li select {
    width: 170px;
    height: 30px;
    margin-left: 5px;
    border: 1px solid #ededed;
    vertical-align: middle;
    border-radius: 30px;
	margin-top: 11px;
	padding: 0 15px;
}
.filterContainer .filterBox li select.ml15{
	margin-left: 15px;
}

.filterContainer .filterBar i {
    padding: 0 14px;
    display: inline-block;
    height: 30px;
    line-height: 30px;
    border-radius: 50px;
    /* margin-left: 12px; */
    font-style: normal;
}

.filterContainer .filterBar i:hover {
    color: #fff;
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
    cursor: pointer;
}

.filterContainer .filterBar i.on {
    color: #fff;
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
}

/* .filterContainer .filterBox li select {
    margin-top: 0 !important;
} */
.filterContainer .lastOne{
	color: #1371e6;
}
.filterContainer .lastOne:hover{
	background: none !important;
}
.filterContainer .lastOne.on{
	background: none !important;
	color: #1371e6 !important;
	border: none !important;
}
/* 展开收起 star */
#container{
    max-height: 170px;
    overflow: hidden;
    transition: all 1s;
}
.filterDown {
    bottom: -15px;
    left: 50%;
    height: 30px;
    width: 30px;
    background: url(.././images/icons/down.png) no-repeat center center;
    cursor: pointer;
    z-index: 10;
}
.filterUp {
    background: url(.././images/icons/up.png) no-repeat center center;
}
#container.unfold{
    max-height: 530px;
}
/* 展开收起 end */
/* 列表关键字搜索 start */
.outLine{
	border: 1px solid #ededed;
	padding: 0 20px;
	width: 1158px;
	/* min-width: 1158px; */
	margin-left: auto;
	margin-right: auto;
}
.searchBox{
	height: 83px;
}
.searchBox .pageTit{
	padding-left: 10px;
    background: url(.././images/icons/pageTitBg.png) no-repeat 5px 23px;
    height: 78px;
    line-height: 79px;
    font-size: 24px;
    font-weight: bold;
    margin-right: 20px;
    padding-top: 5px;
}
.searchBox .pageTit em{
	font-weight: bold;
}
.searchBox .keywordBox {
    width: 278px;
    height: 32px;
    border: 1px solid #0052d9;
	border-radius: 50px;
	margin-top: 24px;
}
/* 最新最热筛选样式修改 2023/10/10 by gaoqian */
.searchBox .sortBox{
	border: 1px solid #0052d9;
	border-radius: 32px;
	background: #fff;
}
.searchBox .sortBox a{
	width: 78px;
	height: 32px;
	line-height: 32px;
	border-radius: 50px;
	text-align: center;
	color: #0052d9;
}
/* 最新最热筛选样式修改 2023/10/10 by gaoqian */
.searchBox .sortBox a.on{
	border: 1px solid #0052d9;
	background: #0052d9;
	color: #fff;
}
.searchBox .sortBox .labeltext{
	margin-top: 7px;
	color: #303946;
}
.keywordBox input {
    width: 220px;
    height: 32px;
    line-height: 32px;
    border: none;
    margin-left: 15px;
	background: none;
}
.keywordBox a {
    width: 23px;
    height: 32px;
    background: url(../images/icons/searchIcon.png) no-repeat center;
    margin-right: 10px;
}
.searchBox > p{
	line-height: 60px;
	height: 60px;
	margin-right: 20px;
}
.searchBox > p span{
	color: #f28b11;
	padding: 0 5px;
}
/* 列表关键字搜索 end */
/* 搜索部分 */
.mryt_Search {
    height: 60px;
    line-height: 60px;
    padding: 0px 20px;
    z-index: 2;
	border-bottom: 1px solid #f1f8ff;
}
.mryt_Search .mryt_Title {
    width: 106px;
    height: 60px;
    font-size: 24px;
    font-weight: bold;
    font-style: italic;
    padding-left: 18px;
    background: url(.././images/icons/pageTitBg.png) no-repeat left 13px;
}
.mryt_Search input {
    margin-top: 14px;
    width: 228px;
    height: 32px;
    line-height: 32px;
    border-radius: 20px;
    border: 1px solid #ededed;
    padding: 0 40px 0 10px;
}
.mryt_Search .search {
    width: 35px;
    height: 30px;
    background: url(.././images/icons/mryt_fdj.png) no-repeat center;
    top: 25px;
    left: 395px;
}
/* 筛选条件 end */
/************************************************弹窗  开始***************************************************/
.zh_popUpbg {
	position: fixed;
	top:0;
	left: 0;
	z-index: 300;
	width: 100%;
	height: 100%;
	background-image: url(../images/icons/popUp_bg.png);
}

.zh_popUpcon {
	background-color: #fff;
	width: 747px;
	max-height: 734px;
	margin: 0 auto;
	top: 10%;
	z-index: 101;
}
.closeBtn{
	width: 15px;
	height: 15px;
	background: url(../images/icons/close3.png) no-repeat;
	top: 25px;
	right: 20px;
}

.zh_popimg {
	padding: 30px 90px;
	text-align: center;
}

/*弹窗关闭按钮*/
.zh_alertCloseBtn {
	-webkit-transform: rotate(0);
	-ms-transform: rotate(0);
	-o-transform: rotate(0);
	-moz-transform: rotate(0);
	transform: rotate(0);
	-webkit-transition: all 218ms;
	-ms-transition: all 218ms;
	-o-transition: all 218ms;
	-moz-transition: all 218ms;
	transition: all 218ms;
}

.zh_alertCloseBtn:hover {
	-webkit-transform: rotate(90deg);
	-ms-transform: rotate(90deg);
	-o-transform: rotate(90deg);
	-moz-transform: rotate(90deg);
	transform: rotate(90deg);
}

/*弹窗出现动画*/
.animated {
	-webkit-animation-duration: .7s;
	animation-duration: .7s;
	-webkit-animation-fill-mode: both;
	animation-fill-mode: both;
}

.zh_fadeInDown {
	-webkit-animation-name: fadeInDown;
	animation-name: fadeInDown;
}

@-webkit-keyframes fadeInDown {
	from {
		opacity: 0;
		-webkit-transform: translate3d(0, -10%, 0);
		transform: translate3d(0, -10%, 0);
	}

	to {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}

@keyframes fadeInDown {
	from {
		opacity: 0;
		-webkit-transform: translate3d(0, -10%, 0);
		transform: translate3d(0, -10%, 0);
	}

	to {
		opacity: 1;
		-webkit-transform: none;
		transform: none;
	}
}

/*弹窗消失动画*/
.zh_fadeOutUp {
	-webkit-animation-name: fadeOutUp;
	animation-name: fadeOutUp;
}

@-webkit-keyframes fadeOutUp {
	from {
		opacity: 1;
	}

	to {
		opacity: 0;
		-webkit-transform: translate3d(0, -25%, 0);
		transform: translate3d(0, -25%, 0);
	}
}

@keyframes fadeOutUp {
	from {
		opacity: 1;
	}

	to {
		opacity: 0;
		-webkit-transform: translate3d(0, -25%, 0);
		transform: translate3d(0, -25%, 0);
	}
}
/*滚动条*/
.scroll-bar {
	overflow: auto;
}

*.scroll-bar {
	overflow-x: hidden;
}

.scroll-bar::-webkit-scrollbar {
	width: 5px;
}

.scroll-bar::-webkit-scrollbar-thumb {
	width: 5px;
	height: 10px;
	border-radius: 20px;
	background: #ddd;
}
/* 收藏 开始 */
.iconBox {
	width: 88px;
    height: 32px;
}
.collectionBox {
    width: 32px;
    height: 32px;
    line-height: 32px;
    background: url(../images/icons/iconCollection.png) no-repeat left center;
    background-size: 32px 32px;
}
.collectionBox:hover {
    background: url(../images/icons/collectHover.png) no-repeat left center;
}
.collectionBox.on{
    background: url(../images/icons/collectHover.png) no-repeat left center;
}
/* 已收藏类名 */
.collectioned {
    width: 36px;
    height: 32px;
    line-height: 32px;
    padding-left: 37px;
    background: url(../images/icons/collected.png) no-repeat left center;
    background-size: 32px 32px;
}

.shareBox {
	width: 32px;
    height: 32px;
    line-height: 32px;
    background: url(../images/icons/iconShare.png) no-repeat left top;
	padding-bottom: 20px;
	margin-left: 20px !important;
}
.shareBox:hover,.shareBox.on {
    background: url(../images/icons/shareHover.png) no-repeat left top;
}

.logBox {
    width: 25px;
    height: 30px;
    line-height: 30px;
    background: url(../images/icons/incoLog.png) no-repeat left center;
    padding-left: 37px;
}
.logBox:hover {
    background: url(../images/icons/logHover.png) no-repeat left center;
}
/* 分享 */
.shareDiv:hover .shareUlBox{
	display: block;
}
.shareUlBox{
	width: 356px;
    height: 121px;
    left: 50%;
    margin-left: -171px;
    top: 36px;
    z-index: 22;
	display: none;
}
.shareUl{
	width: 356px;
	height: 121px;
	background: url(../images/icons/ShareBg.png) no-repeat center;
	box-sizing: border-box;
	padding: 12px 8px 0;
}
.social-share .icon-qq{
	color: none;
	background: url(../images/icons/Share01.png) no-repeat center;
}
.social-share .icon-qq:hover{
	background: url(../images/icons/Share01.png) no-repeat center;
}
.social-share .icon-qzone{
	color: none;
	background: url(../images/icons/Share02.png) no-repeat center;
}
.social-share .icon-qzone:hover{
	background: url(../images/icons/Share02.png) no-repeat center;
}
.social-share .icon-wechat{
	color: none;
	background: url(../images/icons/Share03.png) no-repeat center;
}
.social-share .icon-wechat:hover{
	background: url(../images/icons/Share03.png) no-repeat center;
}
.social-share .icon-weibo{
	color: none;
	background: url(../images/icons/Share04.png) no-repeat center;
}
.social-share .icon-weibo:hover{
	background: url(../images/icons/Share04.png) no-repeat center;
}
/*分享到*/
.social-share{
	zoom: 1;
}
.social-share .social-share-icon {
    position: relative;
	float: left;
    width: 85px;
    height: 98px;
    font-size: 20px;
    border-radius: 0;
    line-height: 40px;
    text-align: center;
    margin: 0;
   transition: background 0.6s ease-out 0s;
    border: none;
}
.social-share .icon-wechat .wechat-qrcode{
    display: none;
    border: 1px solid #eee;
    position: absolute;
    z-index: 9;
        top: -191px;
        left: -58px;
    width: 200px;
    height: 192px;
    color: #666;
    font-size: 12px;
    text-align: center;
    background-color: #fff;
    box-shadow: 0 2px 10px #aaa;
    transition: all 200ms;
    -webkit-tansition: all 350ms;
    -moz-transition: all 350ms;
    z-index: 100;
}
.social-share .icon-wechat .wechat-qrcode.bottom {
        top: 90px;
        left: -58px;
}

.social-share .icon-wechat .wechat-qrcode:hover{
	display: block;
}

/* 收藏 结束 */
/* 列表页tab切换 */

.tabBox{
    background-color: #f4f7fc;
    width: 1200px;
    height: 36px;
    margin: 20px auto 10px;
}
.tabBox p{
    width: 280px;
    height: 60px;
    background-color: #fff;
}
.tabBox p a{
    width: 118px;
    height: 36px;
    line-height: 36px;
    color: #8b94a1;
    text-align: center;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #ededed;
    margin-right: 20px;
}
.tabBox a.on{
    color: #fff;
    background-color: #1371e6;
}
.tabBox a.on em{
    width: 120px;
    height: 7px;
    background: url(../../college/images/topTipOn.png) no-repeat center center;
    bottom: -5px;
    left: 0;
}
/* 详情加载动画 */
#loaddingPop{
	position: fixed;
	width: 100%;
	height: 100%;
	z-index: 1000;
	background: #fff;
}
#loaddingPop img{
	margin: 10% auto 0;
	width: 40%;
}
/* 分页样式修改 */
.pagination .current {
    background-color: #0052d9;
    border-color: #0052d9;
}
.pagination > a {
    color: #0052d9;
}
/* 登录弹窗 start */
.loginPop{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: url(../images/pics/loginPop.png) repeat;
	z-index: 20000;
}
.loginPop .loginMain{
	width: 1165px;
	width: 1181px\0;
	background: url(../images/pics/loginPopBg.png) no-repeat;
	background-size: 100% 100%;
	margin: 1% auto 0;
	padding: 20px 15px 20px 20px;
}
.loginPop .loginMain .scroll-bar{
	height: 84vh;
	padding-right: 3px;
}
.loginPop .titleBox{
	text-align: center;
	font-weight: bold;
	font-size: 24px;
	color: #fff;
	padding: 0 0 24px;
	position: relative;
}
.loginPop .closeLogin{
	position: absolute;
	width: 16px;
	height: 15px;
	background: url(../images/pics/loginPopClose.png) no-repeat;
	top: 0;
	right: 0;
}
.loginPop .loginEnter a{
	width: 380px;
	height: 140px;
	line-height: 100px;
	background: url(../images/pics/loginPopEnter.png) no-repeat;
}
.loginPop .loginEnter a:hover{
	background: url(../images/pics/loginPopEnterOn.png) no-repeat;
}
.loginPop .loginEnter .login1 span{
	display: block;
	padding-left: 133px;
	font-size: 20px;
	font-weight: bold;
	height: 140px;
	background: url(../images/pics/loginPopPic1.png) no-repeat 19px  center;
}
.loginPop .loginEnter .login2 span{
	display: block;
	padding-left: 133px;
	font-size: 20px;
	font-weight: bold;
	height: 140px;
	background:  url(../images/pics/loginPopPic2.png) no-repeat 19px  center;
}
.loginPop .loginEnter .login3 span{
	display: block;
	padding-left: 133px;
	font-size: 20px;
	font-weight: bold;
	height: 140px;
	background:  url(../images/pics/loginPopPic11.png) no-repeat 19px  center;
}
.loginPop .boxText1{
	width: 100%;
	height: 140px;
	background: url(../images/pics/loginPopPic3.png) no-repeat;
	background-size: 100% 100%;
	margin: 10px 0;
}
.loginPop .boxText2{
	width: 575px;
	height: 410px;
	background: url(../images/pics/loginPopPic4.png) no-repeat;
}
.loginPop .boxText3{
	width: 575px;
	height: 410px;
	background: url(../images/pics/loginPopPic5.png) no-repeat;
}
.loginPop .titles{
	font-size: 16px;
	line-height: 36px;
	padding-left: 36px;
	color: #fff;
}
.loginPop  .infos{
	padding: 24px 32px 0 20px;
	line-height: 26px;
	font-size: 16px;
	text-indent: 31px;
}
.loginPop  .infos span{
	color: #0052d9;
	font-weight: bold;
}
.loginPop  .pic1{
	display: block;
	width: 520px;
	height: 190px;
	background: url(../images/pics/loginPopPic6.png) no-repeat;
	margin: 27px auto 20px;
}
.loginPop  .pic2{
	display: block;
	width: 495px;
	height: 190px;
	background: url(../images/pics/loginPopPic7.png) no-repeat;
	margin: 27px auto 20px;
}
.loginPop .boxText2 a{
	width: 166px;
	height: 40px;
	line-height: 40px;
	display: block;
	background: url(../images/pics/loginPopPic8.png) no-repeat;
	color: #fff;
	padding-left: 16px;
	margin: 0 auto;
}
.loginPop .boxText3 a{
	width: 166px;
	height: 40px;
	line-height: 40px;
	display: block;
	background: url(../images/pics/loginPopPic9.png) no-repeat;
	color: #fff;
	padding-left: 16px;
	margin: 0 auto;
}
.loginPop .phoneBottom{
	width: 440px;
	height: 40px;
	background: url(../images/pics/loginPopPic10.png) no-repeat;
	margin: 20px auto 0;
}
/* 弹窗 */
.phoneLoginPop{
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: url(../images/pics/loginPop.png) repeat;
	z-index: 1001;
}
.phoneLogin {
	width: 600px;
	border-radius: 8px;
	overflow: hidden;
	margin: 8% auto 0;
}
.phoneLogin .innerBox{
	background:#fff url(../images/pics/phoneLoginBg.png) no-repeat bottom center;
	padding-bottom: 30px;
	}
	.phoneLogin .bottomTip{
		background: url(../images/pics/phoneLoginBottom.png) no-repeat bottom center;
		height: 87px;
		padding:13px 86px 0;
		line-height: 24px;
	}
.phoneLogin .titlePop{
	height: 67px;
	line-height: 67px;
	text-align: center;
	color: #fff;
	font-size: 20px;
	font-weight: bold;
	text-align: center;
	background: url(../images/pics/phoneLoginTitle.png) no-repeat;
	position: relative;
}
.phoneLogin .titlePop a{
	position: absolute;
	display: block;
	width: 15px;
	height: 15px;
	background: url(../images/icons/close3.png) no-repeat;
	top: 25px;
	right: 30px;
}
.phoneLogin .alertLable {
    width: 105px;
    font-size: 14px;
    color: #000000;
    text-align: right;
    line-height: 44px;
}
.phoneLogin .alertLable span {
    color: rgb(243, 74, 74);
    margin-right: 4px;
}
.phoneLogin .alertIpts {
    height: 44px;
    width: 447px;
    border: 1px solid #ededed;
    border-radius: 4px;
    box-sizing: border-box;
    background-color: #fff;
	overflow: hidden;
}
.phoneLogin .alertIpts input {
    width: 100%;
    height: 42px;
    border: none;
    outline: none;
    padding: 0 10px;
    box-sizing: border-box;

}
.phoneLogin .yzBox {
    width: 91px;
    height: 30px;
    line-height: 30px;
    color: #fff;
    text-align: center;
    border-radius: 5px;
    position: absolute;
    top: 7px;
    right: 60px;
    border: none;
}
.phoneLogin input.loginDuanXinBox {
    width: 91px;
    height: 30px;
    line-height: 30px;
    color: #fff;
    text-align: center;
    background: #8ab4f2;
    border-radius: 5px;
    position: absolute;
    top: 7px;
    right: 50px;
    border: none;
	cursor: pointer;
}
.phoneLogin .alertBtnCont {
    text-align: center;
}
.phoneLogin .alertIptBtn:hover,
.phoneLogin .alertBtn:hover {
    cursor: pointer;
}
.phoneLogin .alertBtn {
    width: 110px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    display: inline-block;
    border-radius: 50px;
}
.phoneLogin .btnCancel {
    border: 1px solid #8db6f2;
    color: #1569e6;
    background-color: #e7f1fc;
}
.phoneLogin .btnSubmit {
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
    color: #ffffff;
}
.phoneLogin .userBtn{
	width: 540px;
	height: 100px;
	margin: 0 auto ;
	background: url(../images/pics/phoneLoginUser.png) no-repeat;
	position: relative;
}
.phoneLogin .qyBtn{
	display: block;
	position: absolute;
	width: 120px;
	height: 32px;
	bottom: 9px;
	left: 133px;
}
.phoneLogin .grBtn{
	display: block;
	position: absolute;
	width: 120px;
	height: 32px;
	bottom: 9px;
	right: 110px;
}
/* 登录弹窗 end */
/* ie 模式清除input框叉号 */
input::-ms-clear{
	display: none;
}
#loaddingDiv img{
	display: block;
	width: 40%;
	margin: 0 auto;
}
/* 地区专区 start */
.zq_Box{
	margin-left: 10px;
	padding-bottom: 20px;
	position: absolute;
	top: 0;
	left: 296px;
}
.zqcityName{
	color: #fff;
	cursor: pointer;
}
.zqcityName em{
	background: url(../images/icons/dq_arr.png) no-repeat right center;
	padding-right: 15px;
}
.zq_Box:hover .zqcityName em{
	color: #ff8a00;
	background: url(../images/icons/dq_arrOn.png) no-repeat right center;
}
.qds{
	display: block;
	padding-left: 24px;
	background: url(../images/icons/dq_icon.png) no-repeat left center;
	font-size: 16px;
	font-weight: bold;
	line-height: 46px;
	height: 46px;
	border-bottom: 1px solid #ddd;
}
.qds:hover a{
	color: #0052d9;
}
.zqSon{
	background: #fff url(../images/icons/dq_pic.png) no-repeat bottom center;
	width: 380px;
	padding: 3px 20px 20px;
	border-radius: 8px;
	border: 2px solid #fff;
	box-shadow: 0px 3px 12px 0px rgba(20, 82, 161, 0.15);
	position: absolute;
	top: 43px;
	left: -180px;
	z-index: 999;
}
.zqSon .arr{
	background: url(../images/icons/dq_bgarr.png) no-repeat;
	display: block;
	width: 16px;
	height: 10px;
	top: -10px;
	left: 202px;
}
.zqdistrictList{
	margin-top: 15px;
}
.zqdistrictList a{
	padding: 0 10px;
	height: 30px;
	line-height: 30px;
	border-radius: 50px;
	margin: 0 10px 5px;
}
.zqdistrictList a.on,.zqdistrictList a:hover{
	background: url(../images/icons/dq_hover.jpg) repeat;
	color: #fff;
}
/* 地区专区 end */
.Aipop{
	position: fixed;
	right: 10px;
	top: 50%;
	display: block;
	z-index: 1000;
	width: 96px;
	height: 96px;
	background: url(../../index/images/ai_pop.png) no-repeat;
	z-index: 1200;
}
.Aipop em{
	position: absolute;
	top: 0;
	right: 0;
	border-radius: 50%;
	background-image: -moz-linear-gradient( 90deg, rgb(255,96,0) 0%, rgb(255,138,0) 100%);
	background-image: -webkit-linear-gradient( 90deg, rgb(255,96,0) 0%, rgb(255,138,0) 100%);
	background-image: -ms-linear-gradient( 90deg, rgb(255,96,0) 0%, rgb(255,138,0) 100%);
	color: #fff;
	height: 20px;
	padding: 0 6px;
}
/* 列表筛选 */
.selectBox{
    padding-top: 20px;
}
.selectBox .selectModule{
    /* position: relative; */
    width: 225px;
    height: 60px;
    line-height: 60px;
    border-radius: 5px;
    overflow: hidden;
    background: #fff url(../../place/image/selectModule_arrowOff.png) no-repeat 207px;
    font-size: 16px;
}
.selectBox .selectModule .title{
    float: left;
    background-color: #f2f8fd;
    width: 88px;
    height: 60px;
    color: #333;
    font-style: italic;
    text-align: center;
}
.selectBox .selectModule.on .title{
    background:linear-gradient(to right, #1a65dd, #2081d4);
    color: #d9d9d9;
}
.selectBox .selectModule.on .name{
    background:url(../../place/image/selectModule_arrowOn.png) no-repeat 119px , linear-gradient(to right, #0052d9, #13b0be);
    color: #fff;
}
.selectBox .selectModule .name{
    float: left;
    width: 127px;
    color: #999;
    padding-left: 10px;
    cursor: pointer;
}
.selectBox .selectModule .name p{
    width: 80%;
}
.selectBox .selectList{
    top: 76px;
    z-index: 20;
}
.selectBox .selectList .arrowIcon{
    background: url(../../place/image/selectModule_ListIcon02.png) no-repeat center;
    width: 14px;
    height: 8px;
    position: absolute;
    top: -8px;
    left: 38px;
}
.selectBox .selectList ul{
    min-width: 205px;
    max-height: 250px;
    overflow-y: scroll;
    background-color: #fff;
    border-radius: 5px;
    /* border: 1px solid #ff6000; */
    padding: 10px;
    box-shadow: 0px 0px 5px 2px #f3f7fd;
}
.selectBox .selectList ul::-webkit-scrollbar {
	width: 8px;
}

.selectBox .selectList ul::-webkit-scrollbar-thumb {
	width: 8px;
	height: 50px;
	border-radius: 25px;
	background: #eaf2ff;
}
.selectBox .selectList ul li{
    height: 50px;
    line-height: 50px;
    padding-left: 86px;
    padding-right: 25px;
    border-radius: 5px;
    color: #333;
    font-size: 16px;
    white-space: nowrap;
    cursor: pointer;
}
.selectBox .selectList ul li:hover{
    background: url(../../place/image/selectModule_ListIcon.png) no-repeat 24px,linear-gradient(to right, #0052d9, #13b0be);
    color: #fff;
}
.selectBox .selectList ul li.on{
    background: url(../../place/image/selectModule_ListIcon.png) no-repeat 24px,linear-gradient(to right, #0052d9, #13b0be);
    color: #fff;
}
.topScreenBox {
    background: url(../../place/image/topScreenBox_bg.jpg) no-repeat center;
    height: 150px;
    min-width: 1400px;
}
/* 列表筛选 */