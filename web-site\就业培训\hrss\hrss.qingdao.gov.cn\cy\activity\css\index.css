/* banner */
.bannerBox {
    height: 260px;
}

.bannerBox .hd {
    position: absolute;
    bottom: 0;
    margin-left: 50%;
    z-index: 10;
}

.bannerBox .hd ul {
    text-align: center;
    display: block;
    margin: 0 auto;
}

.bannerBox .hd li {
    display: inline-block;
    width: 9px;
    height: 9px;
    overflow: hidden;
    margin-right: 5px;
    text-indent: -999px;
    cursor: pointer;
    background: #e2e2e2;
    border-radius: 50px;
}

.bannerBox .hd li.on {
    background: #0052d9;
}

/* banner 轮播 */
.bannerSlide {
    width: 100% !important;
    height: 260px;
    top: 0;
    left: 0;
    min-width: 1400px;
    position: absolute !important;
}

.bannerSlide img {
    display: block;
    width: 100% !important;
    height: 100%;
}

.conAuto2 {
    width: 1400px;
    min-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.bannerBox .numBox {
    width: 1160px;
    height: 120px;
    position: absolute;
    bottom: 0;
    margin-right: 50%;
    right: -206px;
    background: url(../image/new_zhdBannerPic.png) no-repeat right bottom;
}

.bannerBox .numBox li {
    padding-left: 151px;
    width: 150px;
    height: 120px;
}

.bannerBox .numBox li.line {
    width: 1px;
    height: 70px;
    margin-top: 25px;
    background: #449ce9;
    padding: 0;
}

.bannerBox .numBox li .nump {
    font-size: 20px;
    color: #fff;
    padding: 15px 0 4px;
}

.bannerBox .numBox li .nump span {
    font-size: 38px;
    color: #fff720;
    font-weight: bold;
}

.bannerBox .numBox li .textp {
    font-size: 20px;
    font-weight: bold;
    color: #fff;
}

.bannerBox .numBox li.icon1 {
    background: url(../image/new_zhdBannerPic2.png) no-repeat 54px center;
    background-size: 80px 80px;
}

.bannerBox .numBox li.icon2 {
    background: url(../image/new_zhdBannerPic3.png) no-repeat 54px center;
    background-size: 80px 80px;
}

.bannerBox .numBox li.icon3 {
    background: url(../image/new_zhdBannerPic4.png) no-repeat 54px center;
    background-size: 80px 80px;
}

.pageBg {
    width: 100%;
    min-width: 1400px;
    height: 892px;
    background: url(../image/zhd_bg.png) no-repeat top center;
    overflow: hidden;
}

/* 标题 */
.contentTitle {
    font-size: 30px;
    font-weight: bold;
    color: #333;
    padding-bottom: 18px;
    background: url(../../public/images/icons/titleStyleLine.png) no-repeat center bottom;
}

.contentTitle.contentTitle2 {
    background: url(../image/titleStyleLine2.png) no-repeat center bottom;
}

.contentTitle em {
    color: #0052d9;
    font-size: 30px;
    font-weight: bold;
}

.xg {
    color: #0052d9;
    font-size: 7.76px;
    line-height: 15px;
    margin-top: 10px;
}

.moreBtn {
    height: 30px;
    padding-right: 12px;
    border-radius: 30px;
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
}

.moreBtn span {
    line-height: 30px;
    padding-right: 20px;
    padding-left: 11px;
    background: url(../../public/images/icons/icon_more.png) no-repeat right center;
}

.moreBtn:hover {
    animation: moreBtn 0.5s 1 forwards;
    -webkit-animation: moreBtn 0.5s 1 forwards;
    box-shadow: 0px 0px 10px 3px rgba(0, 77, 198, 0.35);
}

@keyframes moreBtn {
    from {
        background-position: 0;
    }

    to {
        background-position: 71px;
    }
}

/* 入口 */
.enterOut {
    width: 1430px;
    margin-top: 40px;
    position: relative;
}

.enterOut a {
    width: 446px;
    height: 300px;
    margin-right: 30px;
    box-shadow: 0px 0px 10px 0px rgba(0, 82, 217, 0.14);
}

.enter1 {
    background: url(../image/zhd_enter1.png) no-repeat;
}

.enter2 {
    background: url(../image/zhd_enter2.png) no-repeat;
}

.enter3 {
    background: url(../image/zhd_enter3.png) no-repeat;
}

.enter4 {
    background: url(../image/zhd_enter4.png) no-repeat;
}

.enter1 em,
.enter2 em,
.enter3 em,
.enter4 em {
    display: block;
    width: 100px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #fff;
    border-radius: 50px;
    background: url(../image/zhd_btn1.png) no-repeat;
    margin: 240px 0 0 107px;
}

.enter2 em {
    background: url(../image/zhd_btn2.png) no-repeat;
    margin: 240px auto 0;
}

.enter3 em {
    margin: 240px auto 0;
}

.enter4 em {
    height: 35px;
    background: url(../image/zhd_btn4.png) no-repeat;
    margin: 240px auto 0;
}

.enterOut a:hover em {
    animation: rotate 0.3s ease infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0);
    }

    20% {
        transform: rotate(-2deg);
    }

    60% {
        transform: rotate(0);
    }

    80% {
        transform: rotate(2deg);
    }

    100% {
        transform: rotate(0);
    }
}

/* 活动动态 */
.cyhdOut {
    border: 1px solid #eaf2ff;
    width: 683px;
    height: 400px;
    border-radius: 8px;
    margin-top: 20px;
    background: #fff;
}

.activityList li {
    width: 605px;
    height: 154px;
    background: url(../image/new_zhdJqhdBg.png) no-repeat center top;
    border-radius: 6px;
    margin-top: 20px;
    margin-left: 20px;
    padding: 16px 20px 0;
}

.activityList li:last-child {
    margin-right: 0;
}

.activityList li:hover {
    background: url(../image/new_zhdJqhdBgOn.png) no-repeat center top;
    box-shadow: 0px 0px 16px 0px rgba(0, 82, 217, 0.2);
}

.activityList li .arrs {
    display: block;
    width: 60px;
    height: 60px;
    top: 1px;
    right: 1px;
}

.spanBox0 {
    background: url(../image/new_zhdJqhdTab1.png);
}

.spanBox1 {
    background: url(../image/new_zhdJqhdTab2.png);
}

.spanBox2 {
    background: url(../image/new_zhdJqhdTab3.png);
}

.activityList li .baseName {
    font-size: 20px;
    line-height: 26px;
    height: 26px;
    width: 553px;
    margin-bottom: 14px;
}

.activityList li:hover .baseName {
    color: #0052d9;
}

.activityList li .time {
    padding-left: 25px;
    background: url(../image/new_zhdTime.png) no-repeat left center;
    line-height: 30px;
    height: 30px;
    font-size: 16px;
    color: #666;
}

.activityList li .address {
    padding-left: 25px;
    background: url(../image/new_zhdAddress.png) no-repeat left center;
    line-height: 30px;
    height: 30px;
    font-size: 16px;
    color: #666;
    margin-bottom: 10px;
}

.activityList li .type>span {
    height: 30px;
    line-height: 30px;
    padding: 0 15px;
    background: #eaf2ff;
    text-align: center;
    color: #0052d9;
    border-radius: 0 50px 50px 50px;
}

.activityList li .type p {
    height: 30px;
    border-radius: 30px;
    background: #fff;
    color: #0052d9;
    width: 100px;
    border: 1px solid #eaf2ff;
}

.activityList li .type p span {
    display: block;
    line-height: 30px;
    padding-left: 12px;
    background: url(../image/new_zhdArr.png) no-repeat 80px center;
}

.activityList li:hover .type p span {
    background: url(../../public/images/icons/icon_more.png) no-repeat 80px center;
}

.activityList li:hover .type p {
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
    color: #fff;
}

.activityList li:hover .type p {
    animation: moreBtn2 0.5s 1 forwards;
    -webkit-animation: moreBtn2 0.5s 1 forwards;
    box-shadow: 0px 0px 10px 3px rgba(0, 77, 198, 0.35);
}

@keyframes moreBtn2 {
    from {
        background-position: 0;
    }

    to {
        background-position: 100px;
    }
}

/* 活动回顾 */
.rightBox {
    width: 675px;
    height: 450px;
    background: url(../image/zhd_bg2.png) no-repeat center bottom;
    margin-top: 40px;
    position: relative;
    padding: 17px 5px 5px;
}

.rightBox>.title {
    position: absolute;
    width: 249px;
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    color: #fff;
    text-align: center;
    font-weight: bold;
    background: url(../image/zhd_title.png) no-repeat top center;
    z-index: 100;
    left: 218px;
    top: 0;
}

.rightBox .moreTxt {
    display: block;
    width: 50px;
    color: #0052d9;
    background: url(../../policy/images/policySIcon10.png) no-repeat right center;
    margin: 7px auto 0;
}

.hd {
    width: 100%;
    position: absolute;
    left: 0;
    /* top: 0; */
}

.hd a {
    position: absolute;
    height: 30px;
    width: 30px;
    top: 172px;
}

.hd a.prev {
    left: 20px;
    background: url(../../policy/images/index_dspArr1.png) no-repeat center;
    background-size: 100% 100%;
}

.hd a.prev:hover {
    background: url(../../policy/images/policySIcon1On.png) no-repeat center;
    background-size: 100% 100%;
}

.hd a.next {
    right: 20px;
    background: url(../../policy/images/index_dspArr2.png) no-repeat center;
    background-size: 100% 100%;
}

.hd a.next:hover {
    background: url(../../policy/images/policySIcon2On.png) no-repeat center;
    background-size: 100% 100%;
}

.rightBox .tempWrap {
    border-radius: 8px;
}

.rightBox .videoBox a {
    width: 675px !important;
    height: 450px !important;
    border-radius: 9px;
    overflow: hidden;
    position: relative;
}

.rightBox .videoBox .title {
    position: absolute;
    width: 525px;
    height: 50px;
    line-height: 50px;
    padding: 0 10px 0 10px;
    background: url(../image/zhd_titleBg.png) repeat;
    z-index: 10;
    bottom: 10px;
    left: 10px;
    color: #fff;
    font-size: 18px;
    border-radius: 8px;
}

.rightBox .videoBox a img {
    width: 675px !important;
    height: 450px !important;
    display: block;
}
.rightBox .hd{
    background: url(../image/zhd_titleBg.png) repeat;
    position: absolute;
    bottom: 0;
    left: 0;
    border-radius: 0 0 8px 8px;
    height: 90px;
    padding: 10px 0 0 10px;
    box-sizing: border-box;
    overflow: hidden;
}
.rightBox .hd li{
    width: 101px !important;
    height: 66px;
    border-radius: 5px;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.281);
    margin-right: 5px;
}
.rightBox .hd li.on{
    border: 2px solid #fff;
}
.rightBox .hd li img{
    display: block;
    width: 100%;
    height: 100%;
}
.hdhg{
    width: 675px;
    height: 450px;
}
.rightBox .listBox{
    width: 267px;
    height: 452px;
    background: url(../image/zhd_listbg.png) no-repeat left center;
    position: absolute;
    right: 0;
    top: 0;
    overflow-y: scroll;
}
.yearList {
    margin-top: 33px;
    margin-left: 40px;
}
.yearList li{
    width: 210px;
}
.yearList li .years,.months{
    width: 190px;
    height: 39px;
    line-height: 39px;
    background: url(../image/zhd_libg.png);
    border: 1px solid #2e959c;
    border-radius: 8px;
    margin-bottom: 10px;
    padding-left: 18px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    position: relative;
    cursor: pointer;
}
.yearList li .years::after,.months::after{
    position: absolute;
    top: 16px;
    right: 18px;
    content: "";
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 10px solid #fff;
}
.yearList li .years.on{
    background-image: -moz-linear-gradient( 0deg, rgb(0,82,217) 0%, rgb(19,176,190) 100%);
    background-image: -webkit-linear-gradient( 0deg, rgb(0,82,217) 0%, rgb(19,176,190) 100%);
    background-image: -ms-linear-gradient( 0deg, rgb(0,82,217) 0%, rgb(19,176,190) 100%);
    border: none;
    width: 192px;
    height: 41px;
    line-height: 41px;
}
.yearList li .years.on::after{
    position: absolute;
    top: 6px;
    right: 18px;
    content: "";
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 10px solid #fff;
    border-top: 10px solid transparent;
}
.months{
    font-weight: 500;
    font-size: 16px;
}
.months.on{
    background-image: -moz-linear-gradient( 0deg, rgba(0,82,217,0.8) 0%, rgba(19,176,190,0.8) 100%);
    background-image: -webkit-linear-gradient( 0deg, rgba(0,82,217,0.8) 0%, rgba(19,176,190,0.8) 100%);
    background-image: -ms-linear-gradient( 0deg, rgba(0,82,217,0.8) 0%, rgba(19,176,190,0.8) 100%);
}
.yearList li .months.on::after{
    position: absolute;
    top: 6px;
    right: 18px;
    content: "";
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 10px solid #fff;
    border-top: 10px solid transparent;
}
.hds{
    width: 178px;
    height: 48px;
    line-height: 16px;
    background: url(../image/zhd_libg.png);
    border: 1px solid #2e959c;
    padding: 5px 10px;
    border-radius: 8px;
    margin-bottom: 10px;
    margin-left: 10px;
    font-size: 12px;
    color: #fff;
    position: relative;
    cursor: pointer;
}
.hds.on{
    background-image: -moz-linear-gradient( 0deg, rgba(0,82,217,0.8) 0%, rgba(19,176,190,0.8) 100%);
    background-image: -webkit-linear-gradient( 0deg, rgba(0,82,217,0.8) 0%, rgba(19,176,190,0.8) 100%);
    background-image: -ms-linear-gradient( 0deg, rgba(0,82,217,0.8) 0%, rgba(19,176,190,0.8) 100%);
}