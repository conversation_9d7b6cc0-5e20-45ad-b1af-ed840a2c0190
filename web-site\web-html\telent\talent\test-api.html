<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
    </style>
</head>
<body>
    <h1>招聘信息匹配API测试</h1>
    
    <div class="test-section">
        <h3>1. 获取招聘信息列表</h3>
        <button onclick="testGetJobPostings()">获取招聘信息</button>
        <button onclick="testGetHotJobs()">获取热门招聘</button>
        <button onclick="testGetFeaturedJobs()">获取推荐招聘</button>
        <div id="jobListResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>2. 获取零工信息列表</h3>
        <button onclick="testGetWorkers()">获取零工信息</button>
        <button onclick="testGetVerifiedWorkers()">获取已认证零工</button>
        <button onclick="testGetHighRatedWorkers()">获取高评分零工</button>
        <div id="workerListResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>3. 匹配功能测试</h3>
        <input type="number" id="jobIdInput" placeholder="输入招聘信息ID" value="1">
        <button onclick="testMatchWorkers()">匹配零工</button>
        <br>
        <input type="number" id="jobIdInput2" placeholder="招聘信息ID" value="1">
        <input type="number" id="workerIdInput" placeholder="零工ID" value="1">
        <button onclick="testCalculateSimilarity()">计算相似度</button>
        <div id="matchResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>4. 详情查询测试</h3>
        <input type="number" id="jobDetailId" placeholder="招聘信息ID" value="1">
        <button onclick="testGetJobDetail()">获取招聘详情</button>
        <br>
        <input type="number" id="workerDetailId" placeholder="零工ID" value="1">
        <button onclick="testGetWorkerDetail()">获取零工详情</button>
        <div id="detailResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h3>5. 统计信息测试</h3>
        <button onclick="testJobStatistics()">招聘信息统计</button>
        <button onclick="testWorkerStatistics()">零工统计</button>
        <button onclick="testCategoryStatistics()">分类统计</button>
        <div id="statisticsResult" class="result" style="display: none;"></div>
    </div>

    <script src="../public/js/jquery-3.5.0.min.js"></script>
    <script src="../public/js/utils.js"></script>
    <script>
        const API_BASE = 'http://localhost:80/sux-admin/';
        
        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'result ' + (isError ? 'error' : 'success');
            element.textContent = JSON.stringify(data, null, 2);
        }
        
        function apiCall(url, callback) {
            $.ajax({
                url: API_BASE + url,
                method: 'GET',
                success: function(data) {
                    callback(data, false);
                },
                error: function(xhr, status, error) {
                    callback({
                        error: error,
                        status: xhr.status,
                        responseText: xhr.responseText
                    }, true);
                }
            });
        }
        
        // 测试获取招聘信息列表
        function testGetJobPostings() {
            apiCall('public/job/postings?pageNum=1&pageSize=5', function(data, isError) {
                showResult('jobListResult', data, isError);
            });
        }
        
        function testGetHotJobs() {
            apiCall('public/job/postings/hot?limit=5', function(data, isError) {
                showResult('jobListResult', data, isError);
            });
        }
        
        function testGetFeaturedJobs() {
            apiCall('public/job/postings/featured?limit=5', function(data, isError) {
                showResult('jobListResult', data, isError);
            });
        }
        
        // 测试获取零工信息列表
        function testGetWorkers() {
            apiCall('public/job/workers?pageNum=1&pageSize=5', function(data, isError) {
                showResult('workerListResult', data, isError);
            });
        }
        
        function testGetVerifiedWorkers() {
            apiCall('public/job/workers/verified?pageNum=1&pageSize=5', function(data, isError) {
                showResult('workerListResult', data, isError);
            });
        }
        
        function testGetHighRatedWorkers() {
            apiCall('public/job/workers/high-rated?minRating=4.0&limit=5', function(data, isError) {
                showResult('workerListResult', data, isError);
            });
        }
        
        // 测试匹配功能
        function testMatchWorkers() {
            const jobId = document.getElementById('jobIdInput').value;
            apiCall(`public/job/postings/${jobId}/match-workers?limit=5`, function(data, isError) {
                showResult('matchResult', data, isError);
            });
        }
        
        function testCalculateSimilarity() {
            const jobId = document.getElementById('jobIdInput2').value;
            const workerId = document.getElementById('workerIdInput').value;
            apiCall(`public/job/postings/${jobId}/similarity/${workerId}`, function(data, isError) {
                showResult('matchResult', data, isError);
            });
        }
        
        // 测试详情查询
        function testGetJobDetail() {
            const jobId = document.getElementById('jobDetailId').value;
            apiCall(`public/job/postings/${jobId}`, function(data, isError) {
                showResult('detailResult', data, isError);
            });
        }
        
        function testGetWorkerDetail() {
            const workerId = document.getElementById('workerDetailId').value;
            apiCall(`public/job/workers/${workerId}`, function(data, isError) {
                showResult('detailResult', data, isError);
            });
        }
        
        // 测试统计信息
        function testJobStatistics() {
            apiCall('public/job/statistics/postings', function(data, isError) {
                showResult('statisticsResult', data, isError);
            });
        }
        
        function testWorkerStatistics() {
            apiCall('public/job/statistics/workers', function(data, isError) {
                showResult('statisticsResult', data, isError);
            });
        }
        
        function testCategoryStatistics() {
            apiCall('public/job/statistics/postings/category', function(data, isError) {
                showResult('statisticsResult', data, isError);
            });
        }
    </script>
</body>
</html>
