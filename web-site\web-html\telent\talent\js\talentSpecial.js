
// 公用模块html
headerBar()
footerBar()

var viewModel = {
    numData: ko.observableArray(),//数字统计
    calendarList: ko.observableArray(),//人才超市列表
    goTorccs:function(data){
        window.open('./calendarDetail.html?id='+data.baseId)
    },
    typeVo: ko.observableArray([{
        baseId: '2',
        baseName: '现场招聘会',
    },{
        baseId: '1',
        baseName: '线上招聘会',
    }]),//招聘会专场筛选
    jobfairType: ko.observable(2),//招聘会专场类别
    // linkFun:function(data){//招聘会专场类别事件
    //     $(this).addClass('on').siblings().removeClass('on'); // 显示元素
    //     viewModel.jobfairType(data.baseId)
    //     answersListFun()
    // },
    linkFun:function(data){//招聘会专场类别事件
        $(this).addClass('on').siblings().removeClass('on'); // 显示元素
        viewModel.jobfairType(data.baseId)
        console.log(data.baseId);
        if(viewModel.jobfairType() == 1){
            $('.xsZph').removeClass('none')
            $('.xcZph').addClass('none')
        }else{
            $('.xcZph').removeClass('none')
            $('.xsZph').addClass('none')
        }
        answersListFun()
    },
    answersList: ko.observableArray(),//人才超市列表
    goAnswersDetail:function(data){
        window.open('./answersDetail.html?id='+data.baseId + '&type='+viewModel.jobfairType())
    },
    bagnnerList: ko.observableArray(),
    dataList: ko.observableArray(),
}
Object.assign(viewModel, viewModel1);
// banner管理
function initBanner(){
    var obj={
        pageNum:1,
        pageSize:1,
        bannerModule:4
    }
    ajaxgetDataFull('api-qingdao/v1/sysBannerManager/getFrontList',obj)
    if(getData.code==0){
        if(getData.obj.content.length>0){
            viewModel.bagnnerList(getData.obj.content[0].pictureFileList)
            viewModel.bagnnerList.unshift({fullPath:'./images/ts_bannerBg.jpg'})
        }
    }
}
// initBanner()

// 数字统计
// numDataFun()
function numDataFun() {
    ajaxgetData('api-finance/v1/qctTalentsJobFairs/statistics', '', function(data) {
        if (data.code == 0) {
            viewModel.numData(data.obj);
        }
    });
}

// 人才超市列表初始化
calendarListFun()
function calendarListFun() {
    var obj = {
        pageSize: 6,
        pageNum: 1,
        status: 1,//必传
        flag: 1,
    };
    ajaxgetData('api-finance/v1/qctTalentsInfo/findQctTalentsInfoListPage', obj, function(data) {
        if (data.code == 0) {
            viewModel.calendarList(data.obj.content);
        }
    });
}


// 招聘会专场列表初始化
answersListFun()
function answersListFun() {
    var obj = {
        pageSize: 6,
        pageNum: 1,
        jobfairType: viewModel.jobfairType()=='1'?'线上':'现场',
    };
    ajaxgetData('api-finance/v1/qctTalentsJobFairs/findQctTalentsJobFairsListPage', obj, function(data) {
        if (data.code == 0) {
            viewModel.answersList(data.obj.content);
        }
    });
}
// 岗位超市
// 列表初始化
function initgwList() {
    var obj = {
        pageSize: 6,
        pageNum: 1,
        flag: 1,//最新最热
        status: 1,//必传
    };
    // var obj = JSON.stringify(obj)
    ajaxgetData('api-finance/v1/qctTalentsJobInfo/findQctTalentsJobInfoListPage', obj, function(data){
        if (data.obj.content.length > 0) {
            viewModel.dataList(data.obj.content);
        }
    });
}
initgwList(1)
ko.applyBindings(viewModel, document.getElementById("viewModelBox"));
$('#talentPage').addClass('on')
$(".bannerBox").slide({
    titCell:".hd ul",
    mainCell:".bannerSlide",
    autoPlay:true,
    effect:"fold",
    interTime:5000,
    autoPage:true
});