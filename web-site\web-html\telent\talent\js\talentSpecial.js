
// 公用模块html
headerBar()
footerBar()

var viewModel = {
    numData: ko.observableArray(),//数字统计
    calendarList: ko.observableArray(),//人才超市列表
    goTorccs:function(data){
        window.open('./calendarDetail.html?id='+data.baseId)
    },
    typeVo: ko.observableArray([{
        baseId: '2',
        baseName: '现场招聘会',
    },{
        baseId: '1',
        baseName: '线上招聘会',
    }]),//招聘会专场筛选
    jobfairType: ko.observable(2),//招聘会专场类别
    // linkFun:function(data){//招聘会专场类别事件
    //     $(this).addClass('on').siblings().removeClass('on'); // 显示元素
    //     viewModel.jobfairType(data.baseId)
    //     answersListFun()
    // },
    linkFun:function(data){//招聘会专场类别事件
        $(this).addClass('on').siblings().removeClass('on'); // 显示元素
        viewModel.jobfairType(data.baseId)
        console.log(data.baseId);
        if(viewModel.jobfairType() == 1){
            $('.xsZph').removeClass('none')
            $('.xcZph').addClass('none')
        }else{
            $('.xcZph').removeClass('none')
            $('.xsZph').addClass('none')
        }
        answersListFun()
    },
    answersList: ko.observableArray(),//人才超市列表
    goAnswersDetail:function(data){
        window.open('./answersDetail.html?id='+data.baseId + '&type='+viewModel.jobfairType())
    },
    bagnnerList: ko.observableArray(),
    dataList: ko.observableArray(),
    // 新增招聘信息相关
    jobPostings: ko.observableArray(),//招聘信息列表
    currentJobFilters: {
        keyword: '',
        jobType: '',
        location: '',
        salaryRange: ''
    },
    viewJobDetail: function(data) {
        // 查看招聘详情
        window.open('./jobDetail.html?id=' + data.jobId);
    },
    matchWorkers: function(data, event) {
        // 阻止事件冒泡
        event.stopPropagation();
        // 跳转到匹配页面
        window.open('./jobWorkerMatch.html?jobId=' + data.jobId);
    },
}
Object.assign(viewModel, viewModel1);
// banner管理
function initBanner(){
    var obj={
        pageNum:1,
        pageSize:1,
        bannerModule:4
    }
    ajaxgetDataFull('api-qingdao/v1/sysBannerManager/getFrontList',obj)
    if(getData.code==0){
        if(getData.obj.content.length>0){
            viewModel.bagnnerList(getData.obj.content[0].pictureFileList)
            viewModel.bagnnerList.unshift({fullPath:'./images/ts_bannerBg.jpg'})
        }
    }
}
// initBanner()

// 数字统计
// numDataFun()
function numDataFun() {
    ajaxgetData('api-finance/v1/qctTalentsJobFairs/statistics', '', function(data) {
        if (data.code == 0) {
            viewModel.numData(data.obj);
        }
    });
}

// 人才超市列表初始化
calendarListFun()
function calendarListFun() {
    var obj = {
        pageSize: 6,
        pageNum: 1,
        status: 1,//必传
        flag: 1,
    };
    ajaxgetData('api-finance/v1/qctTalentsInfo/findQctTalentsInfoListPage', obj, function(data) {
        if (data.code == 0) {
            viewModel.calendarList(data.obj.content);
        }
    });
}


// 招聘会专场列表初始化
answersListFun()
function answersListFun() {
    var obj = {
        pageSize: 6,
        pageNum: 1,
        jobfairType: viewModel.jobfairType()=='1'?'线上':'现场',
    };
    ajaxgetData('api-finance/v1/qctTalentsJobFairs/findQctTalentsJobFairsListPage', obj, function(data) {
        if (data.code == 0) {
            viewModel.answersList(data.obj.content);
        }
    });
}
// 岗位超市
// 列表初始化
function initgwList() {
    var obj = {
        pageSize: 6,
        pageNum: 1,
        flag: 1,//最新最热
        status: 1,//必传
    };
    // var obj = JSON.stringify(obj)
    ajaxgetData('api-finance/v1/qctTalentsJobInfo/findQctTalentsJobInfoListPage', obj, function(data){
        if (data.obj.content.length > 0) {
            viewModel.dataList(data.obj.content);
        }
    });
}
initgwList(1)

// 初始化招聘信息列表
loadJobPostings()

ko.applyBindings(viewModel, document.getElementById("viewModelBox"));
$('#talentPage').addClass('on')
$(".bannerBox").slide({
    titCell:".hd ul",
    mainCell:".bannerSlide",
    autoPlay:true,
    effect:"fold",
    interTime:5000,
    autoPage:true
});

// 招聘信息相关功能
function loadJobPostings() {
    console.log('开始加载招聘信息列表...');

    var obj = {
        pageSize: 10,
        pageNum: 1
    };

    // 添加筛选条件
    if (viewModel.currentJobFilters.keyword) {
        obj.keyword = viewModel.currentJobFilters.keyword;
    }
    if (viewModel.currentJobFilters.jobType) {
        obj.jobType = viewModel.currentJobFilters.jobType;
    }
    if (viewModel.currentJobFilters.location) {
        obj.workLocation = viewModel.currentJobFilters.location;
    }

    // 使用自定义Ajax请求函数
    customAjaxRequest('public/job/postings', obj, function(data) {
        console.log('招聘信息API响应:', data);

        if (data.code == 0 || data.code == 200) {
            var jobList = data.rows || data.data || [];
            console.log('获取到招聘信息数量:', jobList.length);

            // 处理数据格式
            jobList.forEach(function(job) {
                // 格式化薪资显示
                if (job.salaryMin && job.salaryMax) {
                    job.salaryRange = '￥' + job.salaryMin + '-' + job.salaryMax + '/' + (job.salaryType || '月');
                } else if (job.salaryMin) {
                    job.salaryRange = '￥' + job.salaryMin + '+/' + (job.salaryType || '月');
                } else {
                    job.salaryRange = '面议';
                }

                // 格式化发布时间
                if (job.createTime) {
                    var createDate = new Date(job.createTime);
                    var now = new Date();
                    var diffTime = now - createDate;
                    var diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

                    if (diffDays === 0) {
                        job.publishTimeText = '今天发布';
                    } else if (diffDays === 1) {
                        job.publishTimeText = '昨天发布';
                    } else if (diffDays < 7) {
                        job.publishTimeText = diffDays + '天前发布';
                    } else {
                        job.publishTimeText = createDate.toLocaleDateString();
                    }
                } else {
                    job.publishTimeText = '发布时间未知';
                }
            });

            viewModel.jobPostings(jobList);
        } else {
            console.error('获取招聘信息失败：', data.msg || data.message);
            // 如果API失败，加载模拟数据用于测试
            loadMockJobPostings();
        }
    });
}

// 自定义Ajax请求函数
function customAjaxRequest(url, params, callback) {
    var baseUrl = 'http://localhost:80/sux-admin/';

    // 构建查询参数
    var queryString = '';
    if (params && typeof params === 'object') {
        var paramArray = [];
        for (var key in params) {
            if (params.hasOwnProperty(key) && params[key] !== null && params[key] !== undefined && params[key] !== '') {
                paramArray.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
            }
        }
        queryString = paramArray.length > 0 ? '?' + paramArray.join('&') : '';
    }

    var xhr = new XMLHttpRequest();
    xhr.open('GET', baseUrl + url + queryString, true);
    xhr.timeout = 30000;
    xhr.setRequestHeader('Content-Type', 'application/json');

    xhr.onreadystatechange = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200) {
                try {
                    var response = JSON.parse(xhr.responseText);
                    if (callback && typeof callback === 'function') {
                        callback(response);
                    }
                } catch (e) {
                    console.error('解析响应数据失败:', e);
                    if (callback && typeof callback === 'function') {
                        callback({
                            code: -1,
                            msg: '解析响应数据失败',
                            data: null
                        });
                    }
                }
            } else {
                console.error('请求失败:', xhr.status, xhr.statusText);
                if (callback && typeof callback === 'function') {
                    callback({
                        code: -1,
                        msg: '请求失败: ' + xhr.status + ' ' + xhr.statusText,
                        data: null
                    });
                }
            }
        }
    };

    xhr.ontimeout = function () {
        console.error('请求超时');
        if (callback && typeof callback === 'function') {
            callback({
                code: -1,
                msg: '请求超时',
                data: null
            });
        }
    };

    xhr.onerror = function () {
        console.error('请求发生错误');
        if (callback && typeof callback === 'function') {
            callback({
                code: -1,
                msg: '网络错误',
                data: null
            });
        }
    };

    xhr.send();
}

// 加载模拟招聘信息数据
function loadMockJobPostings() {
    console.log('加载模拟招聘信息数据');

    var mockJobs = [
        {
            jobId: 1,
            jobTitle: 'Java高级开发工程师',
            jobType: '全职',
            jobCategory: 'IT技术',
            workLocation: '青岛市市南区',
            salaryMin: 8000,
            salaryMax: 15000,
            salaryType: '月',
            salaryRange: '￥8000-15000/月',
            viewCount: 156,
            createTime: new Date().toISOString(),
            publishTimeText: '今天发布'
        },
        {
            jobId: 2,
            jobTitle: '前端开发工程师',
            jobType: '全职',
            jobCategory: 'IT技术',
            workLocation: '青岛市崂山区',
            salaryMin: 6000,
            salaryMax: 12000,
            salaryType: '月',
            salaryRange: '￥6000-12000/月',
            viewCount: 89,
            createTime: new Date(Date.now() - 24*60*60*1000).toISOString(),
            publishTimeText: '昨天发布'
        },
        {
            jobId: 3,
            jobTitle: '餐厅服务员',
            jobType: '兼职',
            jobCategory: '餐饮服务',
            workLocation: '青岛市市北区',
            salaryMin: 20,
            salaryMax: 25,
            salaryType: '小时',
            salaryRange: '￥20-25/小时',
            viewCount: 234,
            createTime: new Date(Date.now() - 2*24*60*60*1000).toISOString(),
            publishTimeText: '2天前发布'
        },
        {
            jobId: 4,
            jobTitle: '保洁员',
            jobType: '临时工',
            jobCategory: '保洁清洁',
            workLocation: '青岛市李沧区',
            salaryMin: 150,
            salaryMax: 200,
            salaryType: '日',
            salaryRange: '￥150-200/日',
            viewCount: 67,
            createTime: new Date(Date.now() - 3*24*60*60*1000).toISOString(),
            publishTimeText: '3天前发布'
        },
        {
            jobId: 5,
            jobTitle: '销售代表',
            jobType: '全职',
            jobCategory: '销售',
            workLocation: '青岛市城阳区',
            salaryMin: 4000,
            salaryMax: 8000,
            salaryType: '月',
            salaryRange: '￥4000-8000/月',
            viewCount: 123,
            createTime: new Date(Date.now() - 5*24*60*60*1000).toISOString(),
            publishTimeText: '5天前发布'
        }
    ];

    viewModel.jobPostings(mockJobs);
}

// 搜索招聘信息
function searchJobs() {
    var keyword = document.getElementById('jobSearchInput').value.trim();
    viewModel.currentJobFilters.keyword = keyword;
    loadJobPostings();
}

// 筛选招聘信息
function filterJobs() {
    var jobType = document.getElementById('jobTypeFilter').value;
    var location = document.getElementById('locationFilter').value;
    var salary = document.getElementById('salaryFilter').value;

    viewModel.currentJobFilters.jobType = jobType;
    viewModel.currentJobFilters.location = location;
    viewModel.currentJobFilters.salaryRange = salary;

    loadJobPostings();
}

// 显示匹配的零工（这个函数现在不再使用，因为直接跳转到匹配页面）
function showMatchedWorkers(jobId) {
    // 直接跳转到匹配页面
    window.open('./jobWorkerMatch.html?jobId=' + jobId);
}

// 关闭匹配模态框
function closeWorkerMatchModal() {
    var modal = document.getElementById('workerMatchModal');
    if (modal) {
        modal.remove();
    }
}

// 联系零工
function contactWorker(workerId) {
    alert('联系功能开发中，零工ID：' + workerId);
}

// 查看零工详情
function viewWorkerDetail(workerId) {
    window.open('./workerDetail.html?id=' + workerId);
}