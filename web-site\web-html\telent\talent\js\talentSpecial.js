
// 公用模块html
headerBar()
footerBar()

var viewModel = {
    numData: ko.observableArray(),//数字统计
    calendarList: ko.observableArray(),//人才超市列表
    goTorccs:function(data){
        window.open('./calendarDetail.html?id='+data.baseId)
    },
    typeVo: ko.observableArray([{
        baseId: '2',
        baseName: '现场招聘会',
    },{
        baseId: '1',
        baseName: '线上招聘会',
    }]),//招聘会专场筛选
    jobfairType: ko.observable(2),//招聘会专场类别
    // linkFun:function(data){//招聘会专场类别事件
    //     $(this).addClass('on').siblings().removeClass('on'); // 显示元素
    //     viewModel.jobfairType(data.baseId)
    //     answersListFun()
    // },
    linkFun:function(data){//招聘会专场类别事件
        $(this).addClass('on').siblings().removeClass('on'); // 显示元素
        viewModel.jobfairType(data.baseId)
        console.log(data.baseId);
        if(viewModel.jobfairType() == 1){
            $('.xsZph').removeClass('none')
            $('.xcZph').addClass('none')
        }else{
            $('.xcZph').removeClass('none')
            $('.xsZph').addClass('none')
        }
        answersListFun()
    },
    answersList: ko.observableArray(),//人才超市列表
    goAnswersDetail:function(data){
        window.open('./answersDetail.html?id='+data.baseId + '&type='+viewModel.jobfairType())
    },
    bagnnerList: ko.observableArray(),
    dataList: ko.observableArray(),
    // 新增招聘信息相关
    jobPostings: ko.observableArray(),//招聘信息列表
    currentJobFilters: {
        keyword: '',
        jobType: '',
        location: '',
        salaryRange: ''
    },
    viewJobDetail: function(data) {
        // 查看招聘详情
        window.open('./jobDetail.html?id=' + data.jobId);
    },
    matchWorkers: function(data, event) {
        // 阻止事件冒泡
        event.stopPropagation();
        // 跳转到匹配页面
        window.open('./jobWorkerMatch.html?jobId=' + data.jobId);
    },
}
Object.assign(viewModel, viewModel1);
// banner管理
function initBanner(){
    var obj={
        pageNum:1,
        pageSize:1,
        bannerModule:4
    }
    ajaxgetDataFull('api-qingdao/v1/sysBannerManager/getFrontList',obj)
    if(getData.code==0){
        if(getData.obj.content.length>0){
            viewModel.bagnnerList(getData.obj.content[0].pictureFileList)
            viewModel.bagnnerList.unshift({fullPath:'./images/ts_bannerBg.jpg'})
        }
    }
}
// initBanner()

// 数字统计
// numDataFun()
function numDataFun() {
    ajaxgetData('api-finance/v1/qctTalentsJobFairs/statistics', '', function(data) {
        if (data.code == 0) {
            viewModel.numData(data.obj);
        }
    });
}

// 人才超市列表初始化
calendarListFun()
function calendarListFun() {
    var obj = {
        pageSize: 6,
        pageNum: 1,
        status: 1,//必传
        flag: 1,
    };
    ajaxgetData('api-finance/v1/qctTalentsInfo/findQctTalentsInfoListPage', obj, function(data) {
        if (data.code == 0) {
            viewModel.calendarList(data.obj.content);
        }
    });
}


// 招聘会专场列表初始化
answersListFun()
function answersListFun() {
    var obj = {
        pageSize: 6,
        pageNum: 1,
        jobfairType: viewModel.jobfairType()=='1'?'线上':'现场',
    };
    ajaxgetData('api-finance/v1/qctTalentsJobFairs/findQctTalentsJobFairsListPage', obj, function(data) {
        if (data.code == 0) {
            viewModel.answersList(data.obj.content);
        }
    });
}
// 岗位超市
// 列表初始化
function initgwList() {
    var obj = {
        pageSize: 6,
        pageNum: 1,
        flag: 1,//最新最热
        status: 1,//必传
    };
    // var obj = JSON.stringify(obj)
    ajaxgetData('api-finance/v1/qctTalentsJobInfo/findQctTalentsJobInfoListPage', obj, function(data){
        if (data.obj.content.length > 0) {
            viewModel.dataList(data.obj.content);
        }
    });
}
initgwList(1)

// 初始化招聘信息列表
loadJobPostings()

ko.applyBindings(viewModel, document.getElementById("viewModelBox"));
$('#talentPage').addClass('on')
$(".bannerBox").slide({
    titCell:".hd ul",
    mainCell:".bannerSlide",
    autoPlay:true,
    effect:"fold",
    interTime:5000,
    autoPage:true
});

// 招聘信息相关功能
function loadJobPostings() {
    var obj = {
        pageSize: 10,
        pageNum: 1,
        status: 'published'
    };

    // 添加筛选条件
    if (viewModel.currentJobFilters.keyword) {
        obj.keyword = viewModel.currentJobFilters.keyword;
    }
    if (viewModel.currentJobFilters.jobType) {
        obj.jobType = viewModel.currentJobFilters.jobType;
    }
    if (viewModel.currentJobFilters.location) {
        obj.workLocation = viewModel.currentJobFilters.location;
    }

    ajaxgetData('public/job/postings', obj, function(data) {
        if (data.code == 0) {
            var jobList = data.rows || [];
            // 处理数据格式
            jobList.forEach(function(job) {
                // 格式化薪资显示
                if (job.salaryMin && job.salaryMax) {
                    job.salaryRange = '￥' + job.salaryMin + '-' + job.salaryMax + '/' + (job.salaryType || '月');
                } else if (job.salaryMin) {
                    job.salaryRange = '￥' + job.salaryMin + '+/' + (job.salaryType || '月');
                } else {
                    job.salaryRange = '面议';
                }

                // 格式化发布时间
                if (job.createTime) {
                    var createDate = new Date(job.createTime);
                    var now = new Date();
                    var diffTime = now - createDate;
                    var diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

                    if (diffDays === 0) {
                        job.publishTimeText = '今天发布';
                    } else if (diffDays === 1) {
                        job.publishTimeText = '昨天发布';
                    } else if (diffDays < 7) {
                        job.publishTimeText = diffDays + '天前发布';
                    } else {
                        job.publishTimeText = createDate.toLocaleDateString();
                    }
                } else {
                    job.publishTimeText = '发布时间未知';
                }
            });

            viewModel.jobPostings(jobList);
        }
    });
}

// 搜索招聘信息
function searchJobs() {
    var keyword = document.getElementById('jobSearchInput').value.trim();
    viewModel.currentJobFilters.keyword = keyword;
    loadJobPostings();
}

// 筛选招聘信息
function filterJobs() {
    var jobType = document.getElementById('jobTypeFilter').value;
    var location = document.getElementById('locationFilter').value;
    var salary = document.getElementById('salaryFilter').value;

    viewModel.currentJobFilters.jobType = jobType;
    viewModel.currentJobFilters.location = location;
    viewModel.currentJobFilters.salaryRange = salary;

    loadJobPostings();
}

// 显示匹配的零工
function showMatchedWorkers(jobId) {
    // 创建模态框显示匹配结果
    var modalHtml = `
        <div id="workerMatchModal" class="modal-overlay">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>匹配的零工</h3>
                    <button class="modal-close" onclick="closeWorkerMatchModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="loading">正在匹配零工...</div>
                    <div id="matchedWorkersList" class="matched-workers-list" style="display:none;"></div>
                </div>
            </div>
        </div>
    `;

    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // 调用API获取匹配的零工
    ajaxgetData('public/job/postings/' + jobId + '/match-workers', {limit: 10}, function(data) {
        var loadingDiv = document.querySelector('#workerMatchModal .loading');
        var listDiv = document.getElementById('matchedWorkersList');

        if (data.code == 0 && data.data && data.data.length > 0) {
            loadingDiv.style.display = 'none';
            listDiv.style.display = 'block';

            var html = '';
            data.data.forEach(function(item) {
                var worker = item.worker;
                var similarity = item.similarityPercentage || 0;

                html += `
                    <div class="worker-match-item">
                        <div class="worker-info">
                            <div class="worker-avatar">
                                <img src="${worker.profilePhoto || '../public/images/default-avatar.png'}" alt="头像">
                            </div>
                            <div class="worker-details">
                                <h4>${worker.realName || worker.nickname || '未知'}</h4>
                                <p class="worker-category">${worker.workCategories || '暂无分类'}</p>
                                <p class="worker-location">${worker.currentLocation || '位置未知'}</p>
                                <p class="worker-experience">工作经验：${worker.workExperienceYears || 0}年</p>
                            </div>
                        </div>
                        <div class="match-score">
                            <div class="similarity-circle">
                                <span class="similarity-text">${similarity}%</span>
                            </div>
                            <p class="match-label">匹配度</p>
                        </div>
                        <div class="worker-actions">
                            <button class="btn-contact" onclick="contactWorker(${worker.workerId})">联系</button>
                            <button class="btn-view" onclick="viewWorkerDetail(${worker.workerId})">查看详情</button>
                        </div>
                    </div>
                `;
            });

            listDiv.innerHTML = html;
        } else {
            loadingDiv.innerHTML = '暂无匹配的零工';
        }
    });
}

// 关闭匹配模态框
function closeWorkerMatchModal() {
    var modal = document.getElementById('workerMatchModal');
    if (modal) {
        modal.remove();
    }
}

// 联系零工
function contactWorker(workerId) {
    alert('联系功能开发中，零工ID：' + workerId);
}

// 查看零工详情
function viewWorkerDetail(workerId) {
    window.open('./workerDetail.html?id=' + workerId);
}