-- 培训报名管理菜单SQL
-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训报名管理', '2000', '4', 'application', 'zhaop/application/index', 1, 0, 'C', '0', '0', 'training:application:list', 'form', 1, sysdate(), 1, sysdate(), '培训报名管理菜单');

-- 获取刚插入的菜单ID
SET @menu_id = LAST_INSERT_ID();

-- 按钮父菜单ID
-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训报名查询', @menu_id, '1',  '#', '', 1, 0, 'F', '0', '0', 'training:application:query',        '#', 1, sysdate(), 1, sysdate(), '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训报名新增', @menu_id, '2',  '#', '', 1, 0, 'F', '0', '0', 'training:application:add',          '#', 1, sysdate(), 1, sysdate(), '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训报名修改', @menu_id, '3',  '#', '', 1, 0, 'F', '0', '0', 'training:application:edit',         '#', 1, sysdate(), 1, sysdate(), '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训报名删除', @menu_id, '4',  '#', '', 1, 0, 'F', '0', '0', 'training:application:remove',       '#', 1, sysdate(), 1, sysdate(), '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训报名导出', @menu_id, '5',  '#', '', 1, 0, 'F', '0', '0', 'training:application:export',       '#', 1, sysdate(), 1, sysdate(), '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训报名审核', @menu_id, '6',  '#', '', 1, 0, 'F', '0', '0', 'training:application:review',       '#', 1, sysdate(), 1, sysdate(), '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_id, create_time, update_id, update_time, remark)
values('培训报名取消', @menu_id, '7',  '#', '', 1, 0, 'F', '0', '0', 'training:application:cancel',       '#', 1, sysdate(), 1, sysdate(), '');

-- 注意：这里的parent_id需要根据实际的招聘管理菜单ID进行调整
-- 如果招聘管理菜单ID不是2000，请查询sys_menu表获取正确的ID并替换上面的2000
