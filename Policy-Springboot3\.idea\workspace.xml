<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="fab85451-d718-4717-aece-3bdf2aa072e6" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/sux-admin/README_POLICY_APPLICATION.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-admin/sql/xml_fixes_summary.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-admin/src/main/java/com/sux/web/controller/job/JobPostingController.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-admin/src/main/java/com/sux/web/controller/job/JobPostingController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-admin/src/main/resources/config/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/sux-admin/src/main/resources/config/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/mapper/TrainingOrderMapper.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/mapper/TrainingOrderMapper.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/IJobPostingService.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/IJobPostingService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/ITrainingOrderService.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/ITrainingOrderService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/impl/JobPostingServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/impl/JobPostingServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/impl/TrainingOrderServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/impl/TrainingOrderServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sux-system/src/main/resources/mapper/training/TrainingOrderMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/sux-system/src/main/resources/mapper/training/TrainingOrderMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/components/TableList/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/components/TableList/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/router/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/router/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/JobPostingFormDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/JobPostingFormDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/WorkerProfileFormDialog.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/WorkerProfileFormDialog.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/worker.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../Policy-Vue3/src/views/zhaop/worker.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/trainingOrderDetail.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/trainingOrderDetail.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/trainingOrderList.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/jiuye/activity/trainingOrderList.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/talent/css/talentSpecial.css" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/talent/css/talentSpecial.css" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/talent/js/talentSpecial.js" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/talent/js/talentSpecial.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../web-site/web-html/telent/talent/talentSpecial.html" beforeDir="false" afterPath="$PROJECT_DIR$/../web-site/web-html/telent/talent/talentSpecial.html" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\maven\apache-maven-3.8.8" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\maven\apache-maven-3.8.8\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 5
}]]></component>
  <component name="ProjectId" id="30GMzlfwvRQkED6xGDKMAnbxrVU" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.SuxAdminApplication.executor": "Debug",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "E:/policy/Policy-Springboot3",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "configurable.group.build",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="SuxAdminApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="sux-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.sux.SuxAdminApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="fab85451-d718-4717-aece-3bdf2aa072e6" name="Changes" comment="" />
      <created>1753249720620</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753249720620</updated>
      <workItem from="1753249721774" duration="3724000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-method">
          <url>file://$PROJECT_DIR$/sux-system/src/main/java/com/sux/system/service/impl/TrainingOrderServiceImpl.java</url>
          <line>171</line>
          <properties class="com.sux.system.service.impl.TrainingOrderServiceImpl" method="updateCurrentParticipants">
            <option name="EMULATED" value="true" />
            <option name="WATCH_EXIT" value="false" />
          </properties>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>