-- 招聘信息表
DROP TABLE IF EXISTS `job_posting`;
CREATE TABLE `job_posting` (
  `job_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '招聘ID',
  `job_title` varchar(200) NOT NULL COMMENT '职位名称',
  `job_description` text COMMENT '职位描述',
  `job_type` varchar(50) NOT NULL COMMENT '工作类型（全职/兼职/临时工/小时工）',
  `job_category` varchar(100) NOT NULL COMMENT '工作类别（服务员/保洁/搬运工/销售等）',
  `work_location` varchar(200) NOT NULL COMMENT '工作地点',
  `work_address` varchar(500) COMMENT '详细工作地址',
  `salary_type` varchar(20) NOT NULL COMMENT '薪资类型（hourly/daily/monthly/piece）',
  `salary_min` decimal(10,2) COMMENT '最低薪资',
  `salary_max` decimal(10,2) COMMENT '最高薪资',
  `currency` varchar(10) DEFAULT 'CNY' COMMENT '货币单位',
  `work_hours_per_day` int(11) COMMENT '每日工作小时数',
  `work_days_per_week` int(11) COMMENT '每周工作天数',
  `start_date` date COMMENT '开始日期',
  `end_date` date COMMENT '结束日期',
  `work_time_flexible` tinyint(1) DEFAULT 0 COMMENT '工作时间是否灵活（0否 1是）',
  `experience_required` varchar(100) COMMENT '经验要求',
  `education_required` varchar(50) COMMENT '学历要求',
  `age_min` int(11) COMMENT '最小年龄要求',
  `age_max` int(11) COMMENT '最大年龄要求',
  `gender_requirement` varchar(10) COMMENT '性别要求（male/female/any）',
  `skills_required` text COMMENT '技能要求（JSON格式）',
  `language_required` varchar(100) COMMENT '语言要求',
  `physical_requirements` text COMMENT '体力要求描述',
  `benefits` text COMMENT '福利待遇',
  `contact_person` varchar(100) COMMENT '联系人',
  `contact_phone` varchar(20) COMMENT '联系电话',
  `contact_email` varchar(100) COMMENT '联系邮箱',
  `contact_wechat` varchar(100) COMMENT '微信号',
  `company_name` varchar(200) COMMENT '公司名称',
  `company_address` varchar(500) COMMENT '公司地址',
  `company_description` text COMMENT '公司描述',
  `urgency_level` varchar(20) DEFAULT 'normal' COMMENT '紧急程度（urgent/high/normal/low）',
  `positions_available` int(11) DEFAULT 1 COMMENT '招聘人数',
  `positions_filled` int(11) DEFAULT 0 COMMENT '已招聘人数',
  `application_deadline` datetime COMMENT '申请截止时间',
  `status` varchar(20) DEFAULT 'draft' COMMENT '状态（draft/published/paused/closed/completed）',
  `view_count` int(11) DEFAULT 0 COMMENT '浏览次数',
  `application_count` int(11) DEFAULT 0 COMMENT '申请次数',
  `publisher_user_id` bigint(20) NOT NULL COMMENT '发布者用户ID',
  `publisher_type` varchar(20) DEFAULT 'employer' COMMENT '发布者类型（employer/agency/individual）',
  `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否已验证（0否 1是）',
  `verification_time` datetime COMMENT '验证时间',
  `featured` tinyint(1) DEFAULT 0 COMMENT '是否推荐（0否 1是）',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`job_id`),
  KEY `idx_job_type` (`job_type`),
  KEY `idx_job_category` (`job_category`),
  KEY `idx_work_location` (`work_location`),
  KEY `idx_salary_range` (`salary_min`, `salary_max`),
  KEY `idx_status` (`status`),
  KEY `idx_publisher` (`publisher_user_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_urgency` (`urgency_level`),
  KEY `idx_featured` (`featured`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='招聘信息表';

-- 零工信息表
DROP TABLE IF EXISTS `worker_profile`;
CREATE TABLE `worker_profile` (
  `worker_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '零工ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `real_name` varchar(100) NOT NULL COMMENT '真实姓名',
  `nickname` varchar(100) COMMENT '昵称',
  `gender` varchar(10) COMMENT '性别（male/female）',
  `birth_date` date COMMENT '出生日期',
  `age` int(11) COMMENT '年龄',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `email` varchar(100) COMMENT '邮箱',
  `wechat` varchar(100) COMMENT '微信号',
  `id_card` varchar(20) COMMENT '身份证号',
  `current_location` varchar(200) COMMENT '当前所在地',
  `current_address` varchar(500) COMMENT '详细地址',
  `work_locations` text COMMENT '可工作地点（JSON数组）',
  `education_level` varchar(50) COMMENT '学历水平',
  `work_experience_years` int(11) COMMENT '工作经验年数',
  `work_categories` text COMMENT '工作类别偏好（JSON数组）',
  `job_types_preferred` text COMMENT '偏好工作类型（JSON数组）',
  `skills` text COMMENT '技能列表（JSON格式）',
  `certifications` text COMMENT '证书资质（JSON格式）',
  `languages` text COMMENT '语言能力（JSON格式）',
  `work_time_preference` varchar(50) COMMENT '工作时间偏好（flexible/fixed/part_time/full_time）',
  `salary_expectation_min` decimal(10,2) COMMENT '期望最低薪资',
  `salary_expectation_max` decimal(10,2) COMMENT '期望最高薪资',
  `salary_type_preference` varchar(20) COMMENT '薪资类型偏好（hourly/daily/monthly/piece）',
  `availability_start_date` date COMMENT '可开始工作日期',
  `availability_end_date` date COMMENT '可工作截止日期',
  `work_days_per_week` int(11) COMMENT '每周可工作天数',
  `work_hours_per_day` int(11) COMMENT '每日可工作小时数',
  `physical_condition` varchar(100) COMMENT '身体状况',
  `health_certificate` tinyint(1) DEFAULT 0 COMMENT '是否有健康证（0否 1是）',
  `criminal_record_check` tinyint(1) DEFAULT 0 COMMENT '是否通过无犯罪记录检查（0否 1是）',
  `emergency_contact_name` varchar(100) COMMENT '紧急联系人姓名',
  `emergency_contact_phone` varchar(20) COMMENT '紧急联系人电话',
  `emergency_contact_relation` varchar(50) COMMENT '紧急联系人关系',
  `profile_photo` varchar(500) COMMENT '头像照片URL',
  `resume_file` varchar(500) COMMENT '简历文件URL',
  `portfolio_files` text COMMENT '作品集文件URLs（JSON数组）',
  `work_history` text COMMENT '工作履历（JSON格式）',
  `references` text COMMENT '推荐人信息（JSON格式）',
  `self_introduction` text COMMENT '自我介绍',
  `special_notes` text COMMENT '特殊说明',
  `rating_average` decimal(3,2) DEFAULT 0.00 COMMENT '平均评分',
  `rating_count` int(11) DEFAULT 0 COMMENT '评分次数',
  `completed_jobs` int(11) DEFAULT 0 COMMENT '完成工作数量',
  `success_rate` decimal(5,2) DEFAULT 0.00 COMMENT '成功率',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态（active/inactive/suspended/banned）',
  `is_verified` tinyint(1) DEFAULT 0 COMMENT '是否已实名验证（0否 1是）',
  `verification_time` datetime COMMENT '验证时间',
  `last_active_time` datetime COMMENT '最后活跃时间',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`worker_id`),
  UNIQUE KEY `uk_user_id` (`user_id`),
  KEY `idx_real_name` (`real_name`),
  KEY `idx_phone` (`phone`),
  KEY `idx_current_location` (`current_location`),
  KEY `idx_education_level` (`education_level`),
  KEY `idx_work_experience` (`work_experience_years`),
  KEY `idx_salary_expectation` (`salary_expectation_min`, `salary_expectation_max`),
  KEY `idx_availability` (`availability_start_date`, `availability_end_date`),
  KEY `idx_status` (`status`),
  KEY `idx_rating` (`rating_average`),
  KEY `idx_verified` (`is_verified`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='零工信息表';

-- 工作申请表（记录零工申请招聘的记录）
DROP TABLE IF EXISTS `job_application`;
CREATE TABLE `job_application` (
  `application_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `job_id` bigint(20) NOT NULL COMMENT '招聘ID',
  `worker_id` bigint(20) NOT NULL COMMENT '零工ID',
  `application_status` varchar(20) DEFAULT 'pending' COMMENT '申请状态（pending/accepted/rejected/withdrawn/completed）',
  `application_message` text COMMENT '申请留言',
  `employer_response` text COMMENT '雇主回复',
  `interview_time` datetime COMMENT '面试时间',
  `interview_location` varchar(500) COMMENT '面试地点',
  `interview_notes` text COMMENT '面试备注',
  `start_work_time` datetime COMMENT '开始工作时间',
  `end_work_time` datetime COMMENT '结束工作时间',
  `actual_salary` decimal(10,2) COMMENT '实际薪资',
  `work_rating` decimal(3,2) COMMENT '工作评分',
  `work_feedback` text COMMENT '工作反馈',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`application_id`),
  UNIQUE KEY `uk_job_worker` (`job_id`, `worker_id`),
  KEY `idx_job_id` (`job_id`),
  KEY `idx_worker_id` (`worker_id`),
  KEY `idx_status` (`application_status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作申请表';

-- 匹配记录表（记录系统推荐的匹配结果）
DROP TABLE IF EXISTS `job_match_record`;
CREATE TABLE `job_match_record` (
  `match_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '匹配ID',
  `job_id` bigint(20) NOT NULL COMMENT '招聘ID',
  `worker_id` bigint(20) NOT NULL COMMENT '零工ID',
  `match_score` decimal(5,2) NOT NULL COMMENT '匹配分数（0-100）',
  `match_factors` text COMMENT '匹配因素详情（JSON格式）',
  `match_type` varchar(20) DEFAULT 'system' COMMENT '匹配类型（system/manual）',
  `is_viewed_by_employer` tinyint(1) DEFAULT 0 COMMENT '雇主是否已查看（0否 1是）',
  `is_viewed_by_worker` tinyint(1) DEFAULT 0 COMMENT '零工是否已查看（0否 1是）',
  `employer_interest` varchar(20) COMMENT '雇主兴趣（interested/not_interested/contacted）',
  `worker_interest` varchar(20) COMMENT '零工兴趣（interested/not_interested/applied）',
  `create_id` bigint(20) COMMENT '创建者ID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_id` bigint(20) COMMENT '更新者ID',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`match_id`),
  UNIQUE KEY `uk_job_worker_match` (`job_id`, `worker_id`),
  KEY `idx_job_id` (`job_id`),
  KEY `idx_worker_id` (`worker_id`),
  KEY `idx_match_score` (`match_score`),
  KEY `idx_match_type` (`match_type`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_del_flag` (`del_flag`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作匹配记录表';

-- 插入一些示例数据
INSERT INTO `job_posting` (`job_title`, `job_description`, `job_type`, `job_category`, `work_location`, `work_address`, `salary_type`, `salary_min`, `salary_max`, `work_hours_per_day`, `work_days_per_week`, `start_date`, `experience_required`, `age_min`, `age_max`, `gender_requirement`, `contact_person`, `contact_phone`, `company_name`, `positions_available`, `status`, `publisher_user_id`, `create_id`) VALUES
('餐厅服务员', '负责餐厅日常服务工作，包括点餐、上菜、收银等', '兼职', '服务员', '北京市朝阳区', '北京市朝阳区三里屯太古里', 'hourly', 25.00, 35.00, 8, 5, '2025-08-01', '无经验要求', 18, 45, 'any', '张经理', '13800138001', '美味餐厅', 3, 'published', 1, 1),
('保洁员', '负责办公楼保洁工作，包括地面清洁、垃圾清理等', '全职', '保洁', '上海市浦东新区', '上海市浦东新区陆家嘴金融中心', 'monthly', 4000.00, 5000.00, 8, 6, '2025-08-05', '有相关经验优先', 25, 55, 'any', '李主管', '13900139001', '清洁服务公司', 2, 'published', 2, 2);

-- ----------------------------
-- 招聘管理模块菜单数据
-- ----------------------------

-- 主菜单：招聘管理
INSERT INTO `sys_menu` VALUES (5000, '招聘管理', 0, 3, 'job', NULL, '', '', 1, 0, 'M', '0', '0', '', 'peoples', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '招聘管理目录');

-- 二级菜单：招聘信息管理
INSERT INTO `sys_menu` VALUES (5001, '招聘信息', 5000, 1, 'posting', 'zhaop/index', '', '', 1, 0, 'C', '0', '0', 'job:posting:list', 'post', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '招聘信息管理菜单');

-- 二级菜单：零工管理
INSERT INTO `sys_menu` VALUES (5002, '零工管理', 5000, 2, 'worker', 'zhaop/worker', '', '', 1, 0, 'C', '0', '0', 'job:worker:list', 'user', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '零工管理菜单');

-- 二级菜单：智能匹配
INSERT INTO `sys_menu` VALUES (5003, '智能匹配', 5000, 3, 'match', 'zhaop/match', '', '', 1, 0, 'C', '0', '0', 'job:match:list', 'link', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '智能匹配菜单');

-- 二级菜单：申请管理
INSERT INTO `sys_menu` VALUES (5004, '申请管理', 5000, 4, 'application', 'zhaop/application', '', '', 1, 0, 'C', '0', '0', 'job:application:list', 'form', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '申请管理菜单');

-- 招聘信息管理权限
INSERT INTO `sys_menu` VALUES (5010, '招聘信息查询', 5001, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:query', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5011, '招聘信息新增', 5001, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:add', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5012, '招聘信息修改', 5001, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:edit', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5013, '招聘信息删除', 5001, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:remove', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5014, '招聘信息导出', 5001, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:export', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5015, '招聘信息发布', 5001, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:publish', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5016, '招聘信息暂停', 5001, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:pause', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5017, '招聘信息关闭', 5001, 8, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:close', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5018, '招聘信息完成', 5001, 9, '', '', '', '', 1, 0, 'F', '0', '0', 'job:posting:complete', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');

-- 零工管理权限
INSERT INTO `sys_menu` VALUES (5020, '零工信息查询', 5002, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:query', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5021, '零工信息新增', 5002, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:add', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5022, '零工信息修改', 5002, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:edit', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5023, '零工信息删除', 5002, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:remove', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5024, '零工信息导出', 5002, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:export', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5025, '零工信息激活', 5002, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:activate', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5026, '零工信息停用', 5002, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:deactivate', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5027, '零工信息暂停', 5002, 8, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:suspend', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5028, '零工信息禁用', 5002, 9, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:ban', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5029, '零工信息验证', 5002, 10, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:verify', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5030, '零工评分', 5002, 11, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:rate', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5031, '零工统计更新', 5002, 12, '', '', '', '', 1, 0, 'F', '0', '0', 'job:worker:update-stats', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');

-- 智能匹配权限
INSERT INTO `sys_menu` VALUES (5040, '智能匹配查询', 5003, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'job:match:query', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5041, '智能匹配执行', 5003, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'job:match:execute', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5042, '匹配结果查看', 5003, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'job:match:view', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5043, '匹配记录导出', 5003, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'job:match:export', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');

-- 申请管理权限
INSERT INTO `sys_menu` VALUES (5050, '申请信息查询', 5004, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'job:application:query', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5051, '申请信息新增', 5004, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'job:application:add', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5052, '申请信息修改', 5004, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'job:application:edit', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5053, '申请信息删除', 5004, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'job:application:remove', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5054, '申请信息导出', 5004, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'job:application:export', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5055, '申请审核通过', 5004, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'job:application:accept', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5056, '申请审核拒绝', 5004, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'job:application:reject', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5057, '申请撤回', 5004, 8, '', '', '', '', 1, 0, 'F', '0', '0', 'job:application:withdraw', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
INSERT INTO `sys_menu` VALUES (5058, '申请完成', 5004, 9, '', '', '', '', 1, 0, 'F', '0', '0', 'job:application:complete', '#', 1, '2025-07-22 00:00:00', 1, '2025-07-22 00:00:00', '');
