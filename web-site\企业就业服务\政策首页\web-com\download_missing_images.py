import urllib.request
import urllib.parse
import os
import ssl
from pathlib import Path

# 忽略SSL证书验证
ssl._create_default_https_context = ssl._create_unverified_context

def download_image(url, output_path, base_url):
    """下载单个图片文件"""
    try:
        # 处理相对路径
        if url.startswith('//'):
            full_url = 'https:' + url
        elif url.startswith('/'):
            base_domain = urllib.parse.urljoin(base_url, '/')
            full_url = urllib.parse.urljoin(base_domain, url)
        elif not url.startswith('http'):
            full_url = urllib.parse.urljoin(base_url, url)
        else:
            full_url = url
        
        print(f"正在下载: {full_url}")
        
        # 创建目录
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 下载文件
        req = urllib.request.Request(full_url)
        req.add_header('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36')
        req.add_header('Referer', 'https://hrss.qingdao.gov.cn/')
        
        with urllib.request.urlopen(req, timeout=30) as response:
            content = response.read()
        
        # 保存文件
        with open(output_path, 'wb') as f:
            f.write(content)
        
        print(f"已保存到: {output_path}")
        return True
        
    except Exception as e:
        print(f"下载失败 {url}: {e}")
        return False

def main():
    base_url = 'https://hrss.qingdao.gov.cn/cy/policy/'
    output_dir = "static_files"
    
    # 需要下载的图片列表
    images = [
        # 背景图片
        {'url': '../images/lb_bg.jpg', 'path': 'images/lb_bg.jpg'},
        {'url': '../images/baseEnterOn.png', 'path': 'images/baseEnterOn.png'},
        {'url': '../images/tips.png', 'path': 'images/tips.png'},
        {'url': '../images/tipson.png', 'path': 'images/tipson.png'},
        
        # 图标文件
        {'url': '../../index/indexImg/baseEnterIcon1.png', 'path': 'images/baseEnterIcon1.png'},
        {'url': '../../index/indexImg/baseEnterIcon1on.png', 'path': 'images/baseEnterIcon1on.png'},
        {'url': '../../index/indexImg/baseEnterIcon2.png', 'path': 'images/baseEnterIcon2.png'},
        {'url': '../../index/indexImg/baseEnterIcon2on.png', 'path': 'images/baseEnterIcon2on.png'},
        {'url': '../../index/indexImg/baseEnterIcon3.png', 'path': 'images/baseEnterIcon3.png'},
        {'url': '../../index/indexImg/baseEnterIcon3on.png', 'path': 'images/baseEnterIcon3on.png'},
        {'url': '../../index/indexImg/baseEnterIcon4.png', 'path': 'images/baseEnterIcon4.png'},
        {'url': '../../index/indexImg/baseEnterIcon4on.png', 'path': 'images/baseEnterIcon4on.png'},
        {'url': '../../index/indexImg/baseEnterIcon5.png', 'path': 'images/baseEnterIcon5.png'},
        {'url': '../../index/indexImg/baseEnterIcon5on.png', 'path': 'images/baseEnterIcon5on.png'},
        {'url': '../../index/indexImg/baseEnterIcon6.png', 'path': 'images/baseEnterIcon6.png'},
        {'url': '../../index/indexImg/baseEnterIcon6on.png', 'path': 'images/baseEnterIcon6on.png'},
        {'url': '../../index/indexImg/baseEnterIcon7.png', 'path': 'images/baseEnterIcon7.png'},
        {'url': '../../index/indexImg/baseEnterIcon7on.png', 'path': 'images/baseEnterIcon7on.png'},
        
        # Banner图片
        {'url': '../images/policySBanner.jpg', 'path': 'images/policySBanner.jpg'},
        {'url': '../images/logo.png', 'path': 'images/logo.png'},
    ]
    
    # 下载所有图片
    success_count = 0
    for img in images:
        output_path = os.path.join(output_dir, img['path'])
        if download_image(img['url'], output_path, base_url):
            success_count += 1
    
    print(f"\n下载完成！成功下载 {success_count}/{len(images)} 个图片文件")

if __name__ == "__main__":
    main()
