// Mock Data for Policy Special Page
// 替代api.js的假数据

// 全局变量
var orgUrl = 'https://hrss.qingdao.gov.cn/'
var fileUrl = 'https://file.zhenghe.cn/'
var fileToken = 'iAmRtlarUMenj2tKcPI5x4tYwWuUqLi8VM1qzzEz+cQ='
jQuery.support.cors = true;
var pageIndex = 0;
var pageSize = 12;
var token = localStorage.getItem("token")
var PostData = ''
var getData = ''
var deleteData = ''
var putData = ''

// Mock数据存储
var mockData = {
    // 地域数据
    regions: {
        code: 0,
        obj: {
            place: { baseName: "中央", baseId: "central" },
            province: { baseName: "山东省", baseId: "shandong" },
            city: { baseName: "青岛市", baseId: "qingdao" },
            district: [
                { baseName: "市南区", baseId: "shinan" },
                { baseName: "市北区", baseId: "shibei" },
                { baseName: "李沧区", baseId: "licang" },
                { baseName: "崂山区", baseId: "laoshan" },
                { baseName: "西海岸新区", baseId: "xihaian" },
                { baseName: "城阳区", baseId: "chengyang" },
                { baseName: "即墨区", baseId: "jimo" },
                { baseName: "胶州市", baseId: "jiaozhou" },
                { baseName: "平度市", baseId: "pingdu" },
                { baseName: "莱西市", baseId: "laixi" }
            ]
        }
    },
    
    // 政策类型数据
    policyTypes: {
        code: 0,
        obj: [
            { baseName: "创业补贴", baseId: "cybt" },
            { baseName: "创业贷款", baseId: "cydk" },
            { baseName: "税收优惠", baseId: "ssyh" },
            { baseName: "场地支持", baseId: "cdzc" },
            { baseName: "培训补贴", baseId: "pxbt" },
            { baseName: "社保补贴", baseId: "sbbt" },
            { baseName: "人才引进", baseId: "rcyj" },
            { baseName: "科技创新", baseId: "kjcx" }
        ]
    },
    
    // 政策支持方式数据
    supportModes: {
        code: 0,
        obj: [
            { baseName: "资金补贴", baseId: "zjbt" },
            { baseName: "贷款贴息", baseId: "dktx" },
            { baseName: "税收减免", baseId: "ssjm" },
            { baseName: "场地租金减免", baseId: "cdzjjm" },
            { baseName: "培训费用补贴", baseId: "pxfybt" },
            { baseName: "社保费补贴", baseId: "sbfbt" },
            { baseName: "奖励资金", baseId: "jlzj" },
            { baseName: "技术支持", baseId: "jszc" }
        ]
    },
    
    // Banner数据
    banners: {
        code: 0,
        msg: "操作成功",
        obj: {
            totalCount: 1,
            content: [{
                baseId: "banner001",
                bannerModule: "1",
                bannerModuleName: "找政策",
                pictureFileList: [
                    { fullPath: './images/ps_bannerBg.jpg' }
                ]
            }]
        }
    },
    
    // 政策申报列表数据
    calendarList: {
        code: 0,
        obj: {
            content: [
                {
                    baseId: "cal001",
                    sname: "2024年青岛市一次性创业补贴申报",
                    competentDepartment: "青岛市人力资源和社会保障局",
                    applytime: "2024-01-01 至 2024-12-31",
                    projectTypeName: "创业补贴",
                    areaName: "青岛市",
                    rewardMoney: "10",
                    statusText: "1",
                    detailStatusName: "申请中",
                    applyUrl: "javascript:;",
                    financeMeasureList: [
                        "符合条件的创业者可申请一次性创业补贴1万元",
                        "创业带动就业补贴每人2000元，最高不超过3万元",
                        "优秀创业项目可获得5-20万元资助"
                    ]
                },
                {
                    baseId: "cal002",
                    sname: "大学生创业担保贷款申请项目",
                    competentDepartment: "青岛市财政局、人社局",
                    applytime: "2024-02-01 至 2024-11-30",
                    projectTypeName: "创业贷款",
                    areaName: "青岛市",
                    rewardMoney: "50",
                    statusText: "1",
                    detailStatusName: "申请中",
                    applyUrl: "javascript:;",
                    financeMeasureList: [
                        "个人最高可申请20万元创业担保贷款",
                        "小微企业最高可申请300万元创业担保贷款",
                        "符合条件的可享受全额贴息"
                    ]
                },
                {
                    baseId: "cal003",
                    sname: "小微企业税收减免优惠政策",
                    competentDepartment: "青岛市税务局",
                    applytime: "2024-03-01 至 2024-10-31",
                    projectTypeName: "税收优惠",
                    areaName: "青岛市",
                    methodName: "税收减免",
                    statusText: "5",
                    detailStatusName: "待开始",
                    applyUrl: "javascript:;",
                    financeMeasureList: [
                        "小规模纳税人增值税起征点提高至月销售额15万元",
                        "小微企业所得税减按25%计入应纳税所得额",
                        "符合条件的可享受研发费用加计扣除"
                    ]
                },
                {
                    baseId: "cal004",
                    sname: "青岛市创业孵化基地场地支持",
                    competentDepartment: "青岛市科技局",
                    applytime: "2024-04-01 至 2024-09-30",
                    projectTypeName: "场地支持",
                    areaName: "青岛市",
                    methodName: "场地租金减免",
                    statusText: "1",
                    detailStatusName: "申请中",
                    applyUrl: "javascript:;",
                    financeMeasureList: [
                        "入驻创业孵化基地可享受2年免费场地",
                        "优秀项目可延长至3年场地支持",
                        "提供水电网络等基础设施支持"
                    ]
                },
                {
                    baseId: "cal005",
                    sname: "青岛市高校毕业生创业培训补贴",
                    competentDepartment: "青岛市人力资源和社会保障局",
                    applytime: "2024-05-01 至 2024-12-31",
                    projectTypeName: "培训补贴",
                    areaName: "青岛市",
                    rewardMoney: "5",
                    statusText: "1",
                    detailStatusName: "申请中",
                    applyUrl: "javascript:;",
                    financeMeasureList: [
                        "创业培训补贴每人最高5000元",
                        "网络创业培训补贴每人最高2000元",
                        "创业实训补贴每人最高3000元"
                    ]
                },
                {
                    baseId: "cal006",
                    sname: "青岛市科技型企业研发费用补助",
                    competentDepartment: "青岛市科技局",
                    applytime: "2024-06-01 至 2024-10-31",
                    projectTypeName: "科技创新",
                    areaName: "青岛市",
                    rewardMoney: "100",
                    statusText: "5",
                    detailStatusName: "待开始",
                    applyUrl: "javascript:;",
                    financeMeasureList: [
                        "研发费用补助最高100万元",
                        "高新技术企业认定奖励10万元",
                        "专利申请费用全额补贴"
                    ]
                },
                {
                    baseId: "cal007",
                    sname: "青岛市人才引进住房补贴",
                    competentDepartment: "青岛市人才工作领导小组办公室",
                    applytime: "2024-07-01 至 2024-11-30",
                    projectTypeName: "人才引进",
                    areaName: "青岛市",
                    rewardMoney: "30",
                    statusText: "1",
                    detailStatusName: "申请中",
                    applyUrl: "javascript:;",
                    financeMeasureList: [
                        "博士学位人才住房补贴最高30万元",
                        "硕士学位人才住房补贴最高15万元",
                        "本科学位人才住房补贴最高10万元"
                    ]
                },
                {
                    baseId: "cal008",
                    sname: "青岛市创业带动就业补贴",
                    competentDepartment: "青岛市人力资源和社会保障局",
                    applytime: "2024-08-01 至 2024-12-31",
                    projectTypeName: "创业补贴",
                    areaName: "青岛市",
                    rewardMoney: "3",
                    statusText: "1",
                    detailStatusName: "申请中",
                    applyUrl: "javascript:;",
                    financeMeasureList: [
                        "每带动1人就业补贴2000元",
                        "最高补贴3万元",
                        "连续补贴3年"
                    ]
                },
                {
                    baseId: "cal009",
                    sname: "青岛市社会保险补贴",
                    competentDepartment: "青岛市人力资源和社会保障局",
                    applytime: "2024-09-01 至 2024-12-31",
                    projectTypeName: "社保补贴",
                    areaName: "青岛市",
                    methodName: "社保费补贴",
                    statusText: "9",
                    detailStatusName: "已结束",
                    applyUrl: "javascript:;",
                    financeMeasureList: [
                        "创业者社保补贴每月最高1000元",
                        "员工社保补贴每人每月最高500元",
                        "最长补贴3年"
                    ]
                }
            ]
        }
    }
};

// Mock AJAX函数
function ajaxgetDataFull(url, params) {
    console.log('Mock AJAX GET:', url, params);
    
    if (url.includes('sysArea/sysAreaAndChildsByIp')) {
        getData = mockData.regions;
    } else if (url.includes('sysDictData/sysDictDataList')) {
        if (url.includes('2a2acbab-0b5e-11ee-9603-0050569e68c8')) {
            getData = mockData.policyTypes;
        } else if (url.includes('7892e4eb-0bde-11ee-9603-0050569e68c8')) {
            getData = mockData.supportModes;
        }
    } else if (url.includes('sysBannerManager/getFrontList')) {
        getData = mockData.banners;
    }
}

function ajaxgetData(url, params, callback) {
    console.log('Mock AJAX GET with callback:', url, params);
    
    setTimeout(() => {
        let data = { code: -1 };
        
        if (url.includes('subject/dSubjectInfoListPage')) {
            data = mockData.calendarList;
        } else if (url.includes('zctPolicy/policyListPage')) {
            data = generateFileList();
        } else if (url.includes('zctRead/zctReadListPage')) {
            data = generateReadList();
        }
        
        callback(data);
    }, 100);
}

function ajaxPostData(url, params, callback) {
    console.log('Mock AJAX POST:', url, params);
    
    setTimeout(() => {
        let data = { code: -1 };
        
        if (url.includes('policyAreaPunishApp/allPolicyAreaList')) {
            data = generateColumnList();
        } else if (url.includes('frontPolicyVideo/policyVideoListPage')) {
            data = generateVideoList();
        }
        
        callback(data);
    }, 100);
}

// 生成政策文件列表数据
function generateFileList() {
    return {
        code: 0,
        obj: {
            content: [
                {
                    baseId: "file001",
                    title: "青岛市一次性创业补贴申请办法",
                    policyTypeName: "创业补贴",
                    supportmodeName: "资金补贴",
                    publishTime: "2024-01-15"
                },
                {
                    baseId: "file002",
                    title: "关于进一步支持大学生创业的若干措施",
                    policyTypeName: "创业贷款",
                    supportmodeName: "贷款贴息",
                    publishTime: "2024-01-10"
                },
                {
                    baseId: "file003",
                    title: "青岛市小微企业税收优惠政策实施细则",
                    policyTypeName: "税收优惠",
                    supportmodeName: "税收减免",
                    publishTime: "2024-01-08"
                },
                {
                    baseId: "file004",
                    title: "青岛市创业孵化基地管理办法",
                    policyTypeName: "场地支持",
                    supportmodeName: "场地租金减免",
                    publishTime: "2024-02-05"
                },
                {
                    baseId: "file005",
                    title: "青岛市创业培训补贴实施细则",
                    policyTypeName: "培训补贴",
                    supportmodeName: "资金补贴",
                    publishTime: "2024-02-10"
                },
                {
                    baseId: "file006",
                    title: "青岛市高校毕业生社保补贴政策",
                    policyTypeName: "社保补贴",
                    supportmodeName: "社保费补贴",
                    publishTime: "2024-02-15"
                },
                {
                    baseId: "file007",
                    title: "青岛市科技型企业研发费用补助办法",
                    policyTypeName: "科技创新",
                    supportmodeName: "资金补贴",
                    publishTime: "2024-02-20"
                },
                {
                    baseId: "file008",
                    title: "青岛市人才引进住房补贴实施细则",
                    policyTypeName: "人才引进",
                    supportmodeName: "资金补贴",
                    publishTime: "2024-02-25"
                },
                {
                    baseId: "file009",
                    title: "青岛市创业示范基地认定管理办法",
                    policyTypeName: "场地支持",
                    supportmodeName: "技术支持",
                    publishTime: "2024-03-01"
                },
                {
                    baseId: "file010",
                    title: "青岛市小微企业创业担保贷款实施细则",
                    policyTypeName: "创业贷款",
                    supportmodeName: "贷款贴息",
                    publishTime: "2024-03-05"
                },
                {
                    baseId: "file011",
                    title: "青岛市创业带动就业补贴实施办法",
                    policyTypeName: "创业补贴",
                    supportmodeName: "资金补贴",
                    publishTime: "2024-03-10"
                },
                {
                    baseId: "file012",
                    title: "青岛市创业项目评选奖励办法",
                    policyTypeName: "创业补贴",
                    supportmodeName: "奖励资金",
                    publishTime: "2024-03-15"
                }
            ]
        }
    };
}

// 生成政策解读列表数据
function generateReadList() {
    return {
        code: 0,
        obj: {
            content: [
                {
                    baseId: "read001",
                    title: "创业补贴政策解读：如何申请一次性创业补贴",
                    picUrl: "./images/policyReadNoPic.png",
                    publishTime: "2024-01-20",
                    policy: {
                        source: "青岛市人社局"
                    }
                },
                {
                    baseId: "read002",
                    title: "大学生创业贷款政策详解及申请流程",
                    picUrl: "./images/policyReadNoPic.png",
                    publishTime: "2024-01-18",
                    policy: {
                        source: "青岛市财政局"
                    }
                },
                // {
                //     baseId: "read003",
                //     title: "小微企业税收优惠政策解读：如何享受税收减免",
                //     picUrl: "./images/policyReadNoPic.png",
                //     publishTime: "2024-02-05",
                //     policy: {
                //         source: "青岛市税务局"
                //     }
                // },
                // {
                //     baseId: "read004",
                //     title: "创业孵化基地入驻指南：场地支持政策详解",
                //     picUrl: "./images/policyReadNoPic.png",
                //     publishTime: "2024-02-10",
                //     policy: {
                //         source: "青岛市科技局"
                //     }
                // },
                // {
                //     baseId: "read005",
                //     title: "创业培训补贴申请指南：如何获得免费培训",
                //     picUrl: "./images/policyReadNoPic.png",
                //     publishTime: "2024-02-15",
                //     policy: {
                //         source: "青岛市人社局"
                //     }
                // },
                // {
                //     baseId: "read006",
                //     title: "社保补贴政策解读：创业企业如何减轻社保负担",
                //     picUrl: "./images/policyReadNoPic.png",
                //     publishTime: "2024-02-20",
                //     policy: {
                //         source: "青岛市人社局"
                //     }
                // },
                // {
                //     baseId: "read007",
                //     title: "科技创新补助政策解读：研发费用如何获得补贴",
                //     picUrl: "./images/policyReadNoPic.png",
                //     publishTime: "2024-02-25",
                //     policy: {
                //         source: "青岛市科技局"
                //     }
                // },
                // {
                //     baseId: "read008",
                //     title: "人才引进政策解读：如何享受住房补贴",
                //     picUrl: "./images/policyReadNoPic.png",
                //     publishTime: "2024-03-01",
                //     policy: {
                //         source: "青岛市人才办"
                //     }
                // }
            ]
        }
    };
}

// 生成政策专区列表数据
function generateColumnList() {
    return {
        code: 0,
        obj: {
            content: [
                {
                    baseId: "column001",
                    areaName: "创业补贴专区",
                    description: "一站式创业补贴申请服务"
                },
                {
                    baseId: "column002",
                    areaName: "创业贷款专区",
                    description: "创业担保贷款申请指南"
                },
                {
                    baseId: "column003",
                    areaName: "税收优惠专区",
                    description: "小微企业税收减免政策"
                },
                {
                    baseId: "column004",
                    areaName: "场地支持专区",
                    description: "创业孵化基地入驻服务"
                },
                {
                    baseId: "column005",
                    areaName: "培训补贴专区",
                    description: "创业培训课程及补贴申请"
                },
                {
                    baseId: "column006",
                    areaName: "人才引进专区",
                    description: "高层次人才引进政策"
                }
            ]
        }
    };
}

// 生成短视频列表数据
function generateVideoList() {
    return {
        code: 0,
        obj: {
            content: [
                {
                    baseId: "video001",
                    videoName: "创业补贴申请流程详解",
                    sysAttachmentVo: null,
                    coverAttachmentVo: {
                        fullPath: "./images/pic_noDetail.png"
                    }
                },
                // {
                //     baseId: "video002",
                //     videoName: "大学生创业政策解读",
                //     sysAttachmentVo: null,
                //     coverAttachmentVo: {
                //         fullPath: "./images/pic_noDetail.png"
                //     }
                // },
                // {
                //     baseId: "video003",
                //     videoName: "小微企业扶持政策介绍",
                //     sysAttachmentVo: null,
                //     coverAttachmentVo: {
                //         fullPath: "./images/pic_noDetail.png"
                //     }
                // },
                // {
                //     baseId: "video004",
                //     videoName: "创业担保贷款申请指南",
                //     sysAttachmentVo: null,
                //     coverAttachmentVo: {
                //         fullPath: "./images/pic_noDetail.png"
                //     }
                // },
                // {
                //     baseId: "video005",
                //     videoName: "税收优惠政策详解",
                //     sysAttachmentVo: null,
                //     coverAttachmentVo: {
                //         fullPath: "./images/pic_noDetail.png"
                //     }
                // },
                // {
                //     baseId: "video006",
                //     videoName: "创业孵化基地入驻流程",
                //     sysAttachmentVo: null,
                //     coverAttachmentVo: {
                //         fullPath: "./images/pic_noDetail.png"
                //     }
                // },
                // {
                //     baseId: "video007",
                //     videoName: "创业培训课程介绍",
                //     sysAttachmentVo: null,
                //     coverAttachmentVo: {
                //         fullPath: "./images/pic_noDetail.png"
                //     }
                // },
                // {
                //     baseId: "video008",
                //     videoName: "社保补贴申请攻略",
                //     sysAttachmentVo: null,
                //     coverAttachmentVo: {
                //         fullPath: "./images/pic_noDetail.png"
                //     }
                // },
                // {
                //     baseId: "video009",
                //     videoName: "科技创新项目申报指南",
                //     sysAttachmentVo: null,
                //     coverAttachmentVo: {
                //         fullPath: "./images/pic_noDetail.png"
                //     }
                // },
                // {
                //     baseId: "video010",
                //     videoName: "人才引进政策解读",
                //     sysAttachmentVo: null,
                //     coverAttachmentVo: {
                //         fullPath: "./images/pic_noDetail.png"
                //     }
                // }
            ]
        }
    };
}

// 添加更多mock函数来支持页面功能

// Mock带token的AJAX函数
function ajaxPostDataFull_token(url, params) {
    console.log('Mock AJAX POST with token:', url, params);
    PostData = { code: 0, msg: "操作成功" };
}

function ajaxgetData_token(url, params, callback) {
    console.log('Mock AJAX GET with token:', url, params);
    setTimeout(() => {
        callback({ code: 0, msg: "操作成功" });
    }, 100);
}

function ajaxPostData_token(url, params, callback) {
    console.log('Mock AJAX POST with token:', url, params);
    setTimeout(() => {
        callback({ code: 0, msg: "操作成功" });
    }, 100);
}

function ajaxgetDataFull_token(url, params) {
    console.log('Mock AJAX GET Full with token:', url, params);
    getData = { code: 0, msg: "操作成功" };
}

function ajaxPostDataFull(url, params) {
    console.log('Mock AJAX POST Full:', url, params);
    PostData = { code: 0, msg: "操作成功" };
}

// Mock政策查询相关数据
var zccxMockData = {
    regionList: [
        { baseName: "全部", baseId: "" },
        { baseName: "中央", baseId: "central" },
        { baseName: "山东省", baseId: "shandong" },
        { baseName: "青岛市", baseId: "qingdao" },
        { baseName: "市南区", baseId: "shinan" },
        { baseName: "市北区", baseId: "shibei" },
        { baseName: "李沧区", baseId: "licang" },
        { baseName: "崂山区", baseId: "laoshan" }
    ],
    policyTypeList: [
        { baseName: "全部", baseId: "" },
        { baseName: "创业补贴", baseId: "cybt" },
        { baseName: "创业贷款", baseId: "cydk" },
        { baseName: "税收优惠", baseId: "ssyh" },
        { baseName: "场地支持", baseId: "cdzc" },
        { baseName: "培训补贴", baseId: "pxbt" }
    ],
    supportmodeList: [
        { baseName: "全部", baseId: "" },
        { baseName: "资金补贴", baseId: "zjbt" },
        { baseName: "贷款贴息", baseId: "dktx" },
        { baseName: "税收减免", baseId: "ssjm" },
        { baseName: "场地租金减免", baseId: "cdzjjm" }
    ],
    chainList: [
        { baseName: "全部", baseId: "" },
        { baseName: "高新技术", baseId: "gxjs" },
        { baseName: "现代服务业", baseId: "xdfwy" },
        { baseName: "制造业", baseId: "zzy" },
        { baseName: "文化创意", baseId: "whcy" },
        { baseName: "电子商务", baseId: "dzss" }
    ],
    statusLevelList: [
        { name: "全部", value: "" },
        { name: "申报中", value: "1" },
        { name: "待开始", value: "5" },
        { name: "已结束", value: "9" }
    ]
};

// 政策查询相关的初始化函数
function zccxinitregion() {
    // Mock政策查询地域初始化
    if (typeof window.viewModel !== 'undefined' && window.viewModel.zccxregionList) {
        window.viewModel.zccxregionList(zccxMockData.regionList);
        console.log('zccxinitregion loaded:', zccxMockData.regionList.length, 'items');
    }
}

function zccxinitpolicyType() {
    // Mock政策查询政策类型初始化
    if (typeof window.viewModel !== 'undefined' && window.viewModel.zccxpolicyTypeList) {
        window.viewModel.zccxpolicyTypeList(zccxMockData.policyTypeList);
        console.log('zccxinitpolicyType loaded:', zccxMockData.policyTypeList.length, 'items');
    }
}

function zccxinitsupportmode() {
    // Mock政策查询支持方式初始化
    if (typeof window.viewModel !== 'undefined' && window.viewModel.zccxsupportmodeList) {
        window.viewModel.zccxsupportmodeList(zccxMockData.supportmodeList);
        console.log('zccxinitsupportmode loaded:', zccxMockData.supportmodeList.length, 'items');
    }
}

function zccxinitchain() {
    // Mock政策查询产业分类初始化
    if (typeof window.viewModel !== 'undefined' && window.viewModel.zccxchainList) {
        window.viewModel.zccxchainList(zccxMockData.chainList);
        console.log('zccxinitchain loaded:', zccxMockData.chainList.length, 'items');
    }
}

function zccxinitstatusLevel() {
    // Mock政策查询申报时间初始化
    if (typeof window.viewModel !== 'undefined' && window.viewModel.zccxstatusLevelList) {
        window.viewModel.zccxstatusLevelList(zccxMockData.statusLevelList);
        console.log('zccxinitstatusLevel loaded:', zccxMockData.statusLevelList.length, 'items');
    }
}

// 生成GUID的mock函数
function guid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0,
            v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

// Mock用户相关数据
var mockUserData = {
    mine: function() {
        return {
            userType: '2', // 个人用户
            nickname: '测试用户',
            isLogin: false
        };
    }
};

// 检查登录状态的mock函数
function checkLogin() {
    return false; // 默认未登录状态，可以修改为true来测试登录状态
}

// 添加到全局viewModel1
if (typeof window !== 'undefined') {
    window.viewModel1 = mockUserData;
}

// 扩展viewModel的方法，而不是重新创建
// 这些函数将在policySpecial.js加载后被调用

// Mock headerBar和footerBar函数
function headerBar() {
    console.log('Mock headerBar loaded');
    $('#headerBar').html('<div class="mock-header">Mock Header Bar</div>');
}

function footerBar() {
    console.log('Mock footerBar loaded');
    $('#footerBar').html('<div class="mock-footer">Mock Footer Bar</div>');
}

// Mock其他工具函数
function delHtmlTag(str) {
    if (!str) return '';
    return str.replace(/<[^>]+>/g, '');
}

function getQueryString(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return decodeURI(r[2]);
    return null;
}

function alertShow(selector) {
    $(selector).show();
}

function alertClose(selector) {
    $(selector).hide();
}

// 添加jQuery插件的mock方法
if (typeof jQuery !== 'undefined') {
    // Mock slide插件
    jQuery.fn.slide = function(options) {
        console.log('Mock slide plugin called with options:', options);
        return this;
    };

    // Mock jBox插件
    if (!jQuery.jBox) {
        jQuery.jBox = {
            tip: function(message) {
                console.log('Mock jBox tip:', message);
                alert(message); // 简单的alert替代
            }
        };
    }
}

// 添加一些页面初始化后需要的数据补充
$(document).ready(function() {
    // 等待policySpecial.js加载完成后再初始化数据
    setTimeout(function() {
        console.log('Starting mock data initialization...');

        // 初始化所有数据
        initregion();
        initpolicyType();
        initsupportmode();
        calendarListFun();
        fileListFun();
        readListFun();
        dspListFun();
        columnListFun();

        // 初始化政策查询数据
        zccxinitregion();
        zccxinitpolicyType();
        zccxinitsupportmode();
        zccxinitchain();
        zccxinitstatusLevel();

        console.log('Mock data initialization completed');
    }, 1000); // 增加延迟时间，确保policySpecial.js完全加载
});

// 添加一些可能缺失的全局函数
window.moreList = window.moreList || function(type) {
    console.log('Mock moreList called with type:', type);
    // 可以在这里添加跳转逻辑
};

window.gozcppPage = window.gozcppPage || function() {
    console.log('Mock gozcppPage called');
    // 可以在这里添加政策匹配页面跳转逻辑
};

// 添加一些可能缺失的工具函数
window.initNum = window.initNum || function(type) {
    console.log('Mock initNum called with type:', type);
};

// 确保localStorage清理函数存在
window.clearsx = window.clearsx || function() {
    localStorage.removeItem('zcplace');
    localStorage.removeItem('zcprovince');
    localStorage.removeItem('zccity');
    localStorage.removeItem('zcdistrict');
    localStorage.removeItem('zcpolicyType');
    localStorage.removeItem('zcsupportmode');
    console.log('Mock clearsx called');
};

// 添加主要的数据加载函数
function initregion() {
    if (typeof window.viewModel !== 'undefined' && window.viewModel.regionList) {
        var arr = mockData.regions.obj.district.slice();
        arr.unshift({
            baseName: mockData.regions.obj.city.baseName,
            baseId: mockData.regions.obj.city.baseId
        });
        arr.unshift({
            baseName: mockData.regions.obj.province.baseName,
            baseId: mockData.regions.obj.province.baseId
        });
        arr.unshift({
            baseName: mockData.regions.obj.place.baseName,
            baseId: mockData.regions.obj.place.baseId
        });
        arr.unshift({
            baseName: '全部',
            baseId: ''
        });
        window.viewModel.regionList(arr);
        console.log('initregion loaded:', arr.length, 'items');
    }
}

function initpolicyType() {
    if (typeof window.viewModel !== 'undefined' && window.viewModel.typeList) {
        var arr = mockData.policyTypes.obj.slice();
        arr.unshift({
            baseName: '全部',
            baseId: ''
        });
        window.viewModel.typeList(arr);
        console.log('initpolicyType loaded:', arr.length, 'items');
    }
}

function initsupportmode() {
    if (typeof window.viewModel !== 'undefined' && window.viewModel.supportmodeList) {
        var arr = mockData.supportModes.obj.slice();
        arr.unshift({
            baseName: '全部',
            baseId: ''
        });
        window.viewModel.supportmodeList(arr);
        console.log('initsupportmode loaded:', arr.length, 'items');
    }
}

function calendarListFun() {
    if (typeof window.viewModel !== 'undefined' && window.viewModel.calendarList) {
        window.viewModel.calendarList(mockData.calendarList.obj.content);
        console.log('calendarListFun loaded:', mockData.calendarList.obj.content.length, 'items');
    }
}

function fileListFun() {
    if (typeof window.viewModel !== 'undefined' && window.viewModel.fileList) {
        var data = generateFileList();
        window.viewModel.fileList(data.obj.content.slice(0, 3));
        console.log('fileListFun loaded:', data.obj.content.slice(0, 3).length, 'items');
    }
}

function readListFun() {
    if (typeof window.viewModel !== 'undefined' && window.viewModel.readList) {
        var data = generateReadList();
        window.viewModel.readList(data.obj.content.slice(0, 2)); // 只显示2个政策解读
        console.log('readListFun loaded:', data.obj.content.slice(0, 2).length, 'items');
    }
}

function dspListFun() {
    if (typeof window.viewModel !== 'undefined' && window.viewModel.dspList) {
        var data = generateVideoList();
        window.viewModel.dspList(data.obj.content.slice(0, 3));
        console.log('dspListFun loaded:', data.obj.content.slice(0, 3).length, 'items');
    }
}

function columnListFun() {
    if (typeof window.viewModel !== 'undefined' && window.viewModel.columnList) {
        var data = generateColumnList();
        window.viewModel.columnList(data.obj.content);
        console.log('columnListFun loaded:', data.obj.content.length, 'items');
    }
}

console.log('Mock Data loaded successfully!');
