# Spring配置
spring:
  profiles:
    active: dev

# 日志配置
logging:
  level:
    com.sux: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# MyBatisPlus配置
mybatis-plus:
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 自定义TypeHandler
  type-handlers-package: com.sux.common.config.typehandler
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.sux.**.domain,com.sux.**.entity
  global-config:
    #是否控制台
    banner: false
    db-config:
      #主键类型
      id-type: auto

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*