// 公用模块html
headerBar()
footerBar()
var id = getQueryString('id')
var companyId = getQueryString('companyId')
//分享到
$('#share-2').share({
    sites: ['qq', 'qzone', 'wechat', 'weibo']
});
var viewModel = {
    companyId: ko.observable(companyId?companyId:''), 
    companyName: ko.observable(),
    infoData: ko.observableArray(),//详情
    recommendList: ko.observableArray(),
    isColl: ko.observable(0), //是否已经收藏过
    handerColl: function (data) {
        if (checkLogin()) {
            // 收藏，取消收藏
            addOrCanleCollection('项目日历')
        } else {
            $.jBox.tip("请先登录");
        }
    },
    provinceList: ko.observableArray(),//指标省
    cityList: ko.observableArray(),
    districtList: ko.observableArray(),
    provinceFun:function(data){
        ajaxgetDataFull('api-match/v2/front/sysArea/sysAreaLink?baseParentId='+ viewModel.indicatorsForms().province)
        if(getData.code==0){
            viewModel.cityList(getData.obj)
        }
    },
    cityFun:function(data){
        ajaxgetDataFull('api-match/v2/front/sysArea/sysAreaLink?baseParentId='+viewModel.indicatorsForms().city)
        if(getData.code==0){
            viewModel.districtList(getData.obj)
        }
    },
    belongIndustryList: ko.observableArray(),//所属行业一级
    belongIndustryTwoList: ko.observableArray(),//所属行业二级
    industryFun:function(data){
        var obj={
            typeId: "3B953B8C-9EAB-43E1-86DF-167080A39602",
            baseParentId: viewModel.indicatorsForms().fristDictId,
        }
        ajaxgetDataFull('api-match/v2/front/sysDictData/sysDictDataList',obj)
        if(getData.code==0){
            viewModel.belongIndustryTwoList(getData.obj)
        }
    },
    scaleList: ko.observableArray(),//企业规模
    qualificationsList: ko.observableArray(),//企业资质
    yesnoList: ko.observableArray([{baseName:'是',baseId:true},{baseName:'否',baseId:false}]),//是否
    indicatorsForms: ko.observableArray(),
    editIndicators:function(data){
        // indexValueType  指标值类型(1三级下拉联动 2两级下拉联动 3 checkBox 4 数值 5 radio 6  一级下拉框 7字符串 8日期)',
        var obj={
            companyCode: viewModel.mine().socialCreditCode,
            indexId: data.indexId,
        }
        ajaxgetDataFull_token('api-match/v2/front/sysCompanyIndex/companyIndexByCompanyIdAndIndexId',obj)
        if(getData.code==0){
            var data =getData.obj
            if (data.indexValueType == "1") {
                //省市区
                if (data.realValList && data.realValList.length) {
                    data.province=data.realValList[0]
                    data.city=data.realValList[1]
                    data.district=data.realValList[2]
                    /*省市区处理*/
                    if (data.province) {
                        viewModel.cityList([]);
                        viewModel.districtList([]);
                        ajaxgetDataFull('api-match/v2/front/sysArea/sysAreaLink?baseParentId='+data.province)
                        if(getData.code==0){
                            viewModel.cityList(getData.obj)
                        }
                    }
                    if (data.city) {
                        viewModel.districtList([]);
                        ajaxgetDataFull('api-match/v2/front/sysArea/sysAreaLink?baseParentId='+data.city)
                        if(getData.code==0){
                            viewModel.districtList(getData.obj)
                        }
                    }
                } else {
                    data.province=''
                    data.city=''
                    data.district=''
                }
            } else if (data.indexValueType == "2") {
                //所属行业
                if (data.realValList && data.realValList.length) {
                    data.fristDictId=data.realValList[0]
                    data.secondDictId=data.realValList[1]
                    /*二级产业*/
                    if (data.fristDictId) {
                        var obj={
                            typeId: "3B953B8C-9EAB-43E1-86DF-167080A39602",
                            baseParentId: data.fristDictId,
                        }
                        ajaxgetDataFull('api-match/v2/front/sysDictData/sysDictDataList',obj)
                        if(getData.code==0){
                            viewModel.belongIndustryTwoList(getData.obj)
                        }
                  }
                } else {
                    data.fristDictId=''
                    data.secondDictId=''
                }
            } else if (data.indexValueType == "3") {
                //企业资质
                if (data.realValList && data.realValList.length) {
                    data.qualification=data.realValList
                }
            } else if (data.indexValueType == "6") {
                //企业规模
                data.scale=data.realVal
                if (data.indexValueFilterType) {
                    ajaxgetDataFull('api-match/v2/front/sysDictData/sysDictDataList?typeId='+data.indexValueFilterType)
                    if(getData.code==0){
                        viewModel.scaleList(getData.obj)
                    }
                }
            } else {
                //input输入框
                data.realVal=data.realVal;
                data.realName=data.realName
            }
            if(data.baseId=='1dc15286-4ca0-49ff-a904-3a0a7ca33c7b'){
                $.jBox.tip("不可更改注册资本");return false;
            }
            if(data.baseId=='1dc15286-4ca0-49ff-a904-3a0a7ca33c7c'){
                $.jBox.tip("不可更改注册时间");return false;
            }
            viewModel.indicatorsForms(data)
            alertShow('.alertBg')
        }else{
            $.jBox.tip(getData.obj);
        }
    },
    relationPolicysList: ko.observableArray(),//相关政策
    relationPolicysList1: ko.observableArray(),
    relationPolicysList2: ko.observableArray(),
    relationPolicysList3: ko.observableArray(),
    approvedEnterprisesList: ko.observableArray(),
    videoList: ko.observableArray(),
    musicList: ko.observableArray(),
    picList: ko.observableArray(),
    videoUrl: ko.observable(),
    videoFun: function (data) {
        viewModel.videoUrl(data.fullPath)
        alertShow('.videoPop')
    },
    musicUrl: ko.observable(),
    musicFun: function (data) {
        viewModel.musicUrl(data.fullPath)
        alertShow('.audioPop')
    },
}
Object.assign(viewModel, viewModel1);
function stopFun() {
    viewModel.musicUrl('')
    viewModel.videoUrl('')
}
// 初始化省
initProvince()
function initProvince(){
    ajaxgetDataFull('api-match/v2/front/sysArea/sysAreaLink?baseParentId=')
    if(getData.code==0){
        viewModel.provinceList(getData.obj)
    }
}
// 初始化行业类型
initIndustry()
function initIndustry(){
    ajaxgetDataFull('api-match/v2/front/sysDictData/sysDictDataList?typeId=3B953B8C-9EAB-43E1-86DF-167080A39602')
    if(getData.code==0){
        viewModel.belongIndustryList(getData.obj)
    }
}
// 初始化企业资质
initqualificat()
function initqualificat(){
    ajaxgetDataFull('api-match/v2/front/sysDictData/sysDictDataList?typeId=4391d84c157d4586b8d5f38304a9191d')
    if(getData.code==0){
        viewModel.qualificationsList(getData.obj)
    }
}
//详情
function initinfo(e) {
    var obj={
        subjectId:id,
        companyId:e==1?viewModel.companyId():''
    }
    ajaxgetDataFull_token('api-match/v2/front/viewProjectlist/viewProjectlistInfo',obj)
    if (getData.code == 0 && getData.obj) {
        viewModel.infoData(getData.obj)
       
    } else {
        $.jBox.tip(getData.obj)
    }
}
// initinfo()
// 相关政策
getRelationPolicyList()
function getRelationPolicyList(){
    ajaxgetDataFull('api-manage/v2/front/subject/getRelationPolicyList/'+id)
    if (getData.code == 0) {
        viewModel.relationPolicysList(getData.obj.policyBasis); //政策依据
        viewModel.relationPolicysList1(getData.obj.relPolicy); //政策通知
        viewModel.relationPolicysList2(getData.obj.relPublicity); //相关公示
        viewModel.relationPolicysList3(getData.obj.relRead); //相关解读
    }
}
// 解读
function initread() {
    // 视频
    ajaxPostDataFull('api-qingdao/v1/projectAddQuestion/listUploadFile?baseId=' + id + '&type=1')
    if (PostData.code == 0 && PostData.obj) {
        viewModel.videoList(PostData.obj)
    } else {
        $.jBox.tip(PostData.obj)
    }
    // 音频
    ajaxPostDataFull('api-qingdao/v1/projectAddQuestion/listUploadFile?baseId=' + id + '&type=2')
    if (PostData.code == 0 && PostData.obj) {
        viewModel.musicList(PostData.obj)
    } else {
        $.jBox.tip(PostData.obj)
    }
    // 图片
    ajaxPostDataFull('api-qingdao/v1/projectAddQuestion/listUploadFile?baseId=' + id + '&type=3')
    if (PostData.code == 0 && PostData.obj) {
        viewModel.picList(PostData.obj)
    } else {
        $.jBox.tip(PostData.obj)
    }
}
initread()
// 获批企业名单
function initPolicyList(page) {
    pageIndex = page;
    var obj = {
        pageSize: pageSize,
        pageNum: pageIndex,
        subjectId:id
    };
    ajaxgetData_token('api-manage/v2/front/subject/approvedCompanyList', obj, listCallBlack);
}
function listCallBlack(data) {
    if(data.code==0){
        if (data.obj.content.length>0) {
            $('.qymd').show()
            $(".pager").pagination(data.obj.totalCount, {
                num_edge_entries: 2, //两侧首尾分页条目数
                num_display_entries: 4, //连续分页主体部分分页条目数
                items_per_page: pageSize,
                current_page: pageIndex - 1, //当前页索引
                prev_text: "上一页",
                next_text: '下一页',
                callback: PageCallback,
            });
        }else{
            $('.qymd').hide()
        }
        viewModel.approvedEnterprisesList(data.obj.content);
    }else{
        $.jBox.tip(data.obj)
    }
    
    
}
//分页回调
function PageCallback(index) {
    initPolicyList(index + 1);
}
initPolicyList(1)
//推荐列表
function tjList() {
    var obj={
        id:id,
        companyId:viewModel.companyId(),
        pageNum: 1,
        pageSize: 5
    }
    ajaxgetDataFull_token('api-manage/v2/front/subject/getHotSubjectList',obj)
    if (getData.code == 0) {
        viewModel.recommendList(getData.obj)
    }
}
tjList()

// 判断是否收藏
function checkCollection() {
    ajaxgetDataFull_token('api-app/v1/qctCollection/' + id)
    if (getData.code == 0) {
        viewModel.isColl(getData.obj)
    }
}
$(document).ready(function() {
if (checkLogin()) {
    checkCollection()
    // 绑定企业
    if(viewModel.mine().userType=='1'){
        viewModel.companyId(viewModel.mine().socialCreditCode)
    }
    if(viewModel.companyId()){
        ajaxgetDataFull_token('api-match/v2/front/viewProjectlist/viewProjectMatch?companyId='+viewModel.companyId())
        if (getData.code == 0 && getData.obj) {
            initinfo(1)
        }
    }else{
        initinfo(2)
    }
    findRelCompanyListByUserId()
}else{
    initinfo(2)
}
})
// 指标编辑提交
$(document).on('click','.btnSubmit',function(){
    // 校验
    if(viewModel.indicatorsForms().indexValueType == '1'){
        if(!viewModel.indicatorsForms().province){
            $.jBox.tip("请选择省");return false;
        }
        if(!viewModel.indicatorsForms().city){
            $.jBox.tip("请选择市");return false;
        }
        if(!viewModel.indicatorsForms().district){
            $.jBox.tip("请选择区");return false;
        }
    }else if(viewModel.indicatorsForms().indexValueType == '2'){
        if(!viewModel.indicatorsForms().fristDictId){
            $.jBox.tip("请选择");return false;
        }
        if(!viewModel.indicatorsForms().secondDictId){
            $.jBox.tip("请选择");return false;
        }
    }else if(viewModel.indicatorsForms().indexValueType == '3'){
        if(viewModel.indicatorsForms().qualification.length==0){
            $.jBox.tip("请选择");return false;
        }
    }else if(viewModel.indicatorsForms().indexValueType == '4'){
        if(!viewModel.indicatorsForms().realVal){
            $.jBox.tip("请输入");return false;
        }
    }else if(viewModel.indicatorsForms().indexValueType == '5'){
        if(viewModel.indicatorsForms().realVal!=true&&viewModel.indicatorsForms().realVal!=false){
            $.jBox.tip("请选择");return false;
        }
    }else if(viewModel.indicatorsForms().indexValueType == '6'){
        if(!viewModel.indicatorsForms().scale){
            $.jBox.tip("请选择");return false;
        }
    }else if(viewModel.indicatorsForms().indexValueType == '7'){
        if(!viewModel.indicatorsForms().realVal){
            $.jBox.tip("请输入");return false;
        }
    }else if(viewModel.indicatorsForms().indexValueType == '8'){
        if(!viewModel.indicatorsForms().realVal){
            $.jBox.tip("请选择");return false;
        }
    }
    // 处理参数
    if(viewModel.indicatorsForms().indexValueType == '1'){
        //省市区
        var tempArr = [
            viewModel.indicatorsForms().province,
            viewModel.indicatorsForms().city,
            viewModel.indicatorsForms().district,
          ];
          viewModel.indicatorsForms().realVal = tempArr.join(",");
    }else if(viewModel.indicatorsForms().indexValueType == '2'){
        //所属行业
        var tempArr = [
            viewModel.indicatorsForms().fristDictId,
            viewModel.indicatorsForms().secondDictId,
          ];
          viewModel.indicatorsForms().realVal = tempArr.join(",");
    }else if(viewModel.indicatorsForms().indexValueType == '3'){
        //企业资质
        viewModel.indicatorsForms().realVal = viewModel.indicatorsForms().qualification.join(",");
    }else if(viewModel.indicatorsForms().indexValueType == '6'){
        //企业规模
        viewModel.indicatorsForms().realVal = viewModel.indicatorsForms().scale;
    }
    var tempObj = {
        baseId :viewModel.indicatorsForms().baseId,
        companyId : viewModel.indicatorsForms().companyId?viewModel.indicatorsForms().companyId:viewModel.mine().socialCreditCode,
        indexId: viewModel.indicatorsForms().indexId,
        realVal :viewModel.indicatorsForms().realVal,
    };
    ajaxPostDataFull_token('api-match/v2/front/sysCompanyIndex/addOrSaveSysCompanyIndex',JSON.stringify(tempObj)) 
    if(PostData.code==0){
        $.jBox.tip('编辑成功');
        initinfo(1)
        alertClose('.alertBg')
    }else{
        $.jBox.tip(PostData.obj);
    }
})
// 匹配历史
function findRelCompanyListByUserId(){
    ajaxgetDataFull_token('api-match/v2/front/companyMatch/getHistoryMatchCompany')
    if(getData.code==0){
        if(getData.obj.length>0){
            for(var i=0;i<getData.obj.length;i++){
                if(viewModel.companyId()==getData.obj[i].companyId){
                    viewModel.companyName(getData.obj[i].companyName)
                }
            }
        }
    }
}
ko.applyBindings(viewModel, document.getElementById("viewModelBox"));
$('#policyPage').addClass('on')
$(document).ready(function() {
    $(".contentBox").slide({
        titCell: ".hd ul li",
        mainCell: ".bd",
        trigger: "click"
    });
})

   var str = window.location.href;
var position = str.indexOf('policy');
var erweima =str.substr(0, position)+(str.substr(0, position)=='https://hrss.qingdao.gov.cn/cy/'?'qct/policy/calendarDetail.html?id='+id:'h5site/policy/calendarDetail.html?id='+id);
var qrcode = new QRCode("erweima", {
    text: erweima,
    width: 130,
    height: 130,
});