package com.sux.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 招聘信息对象 job_posting
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@TableName("job_posting")
public class JobPosting extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 招聘ID */
    @TableId(type = IdType.AUTO)
    private Long jobId;

    /** 职位名称 */
    @Excel(name = "职位名称")
    @NotBlank(message = "职位名称不能为空")
    @Size(min = 0, max = 200, message = "职位名称不能超过200个字符")
    private String jobTitle;

    /** 职位描述 */
    @Excel(name = "职位描述")
    private String jobDescription;

    /** 工作类型（全职/兼职/临时工/小时工） */
    @Excel(name = "工作类型")
    @NotBlank(message = "工作类型不能为空")
    @Size(min = 0, max = 50, message = "工作类型不能超过50个字符")
    private String jobType;

    /** 工作类别（服务员/保洁/搬运工/销售等） */
    @Excel(name = "工作类别")
    @NotBlank(message = "工作类别不能为空")
    @Size(min = 0, max = 100, message = "工作类别不能超过100个字符")
    private String jobCategory;

    /** 工作地点 */
    @Excel(name = "工作地点")
    @NotBlank(message = "工作地点不能为空")
    @Size(min = 0, max = 200, message = "工作地点不能超过200个字符")
    private String workLocation;

    /** 详细工作地址 */
    @Excel(name = "详细工作地址")
    @Size(min = 0, max = 500, message = "详细工作地址不能超过500个字符")
    private String workAddress;

    /** 薪资类型（hourly/daily/monthly/piece） */
    @Excel(name = "薪资类型")
    @NotBlank(message = "薪资类型不能为空")
    private String salaryType;

    /** 最低薪资 */
    @Excel(name = "最低薪资")
    private BigDecimal salaryMin;

    /** 最高薪资 */
    @Excel(name = "最高薪资")
    private BigDecimal salaryMax;

    /** 货币单位 */
    @Excel(name = "货币单位")
    private String currency;

    /** 每日工作小时数 */
    @Excel(name = "每日工作小时数")
    private Integer workHoursPerDay;

    /** 每周工作天数 */
    @Excel(name = "每周工作天数")
    private Integer workDaysPerWeek;

    /** 开始日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date startDate;

    /** 结束日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endDate;

    /** 工作时间是否灵活（0否 1是） */
    @Excel(name = "工作时间是否灵活", readConverterExp = "0=否,1=是")
    private Integer workTimeFlexible;

    /** 经验要求 */
    @Excel(name = "经验要求")
    @Size(min = 0, max = 100, message = "经验要求不能超过100个字符")
    private String experienceRequired;

    /** 学历要求 */
    @Excel(name = "学历要求")
    @Size(min = 0, max = 50, message = "学历要求不能超过50个字符")
    private String educationRequired;

    /** 最小年龄要求 */
    @Excel(name = "最小年龄要求")
    private Integer ageMin;

    /** 最大年龄要求 */
    @Excel(name = "最大年龄要求")
    private Integer ageMax;

    /** 性别要求（male/female/any） */
    @Excel(name = "性别要求")
    private String genderRequirement;

    /** 技能要求（JSON格式） */
    @Excel(name = "技能要求")
    private String skillsRequired;

    /** 语言要求 */
    @Excel(name = "语言要求")
    @Size(min = 0, max = 100, message = "语言要求不能超过100个字符")
    private String languageRequired;

    /** 体力要求描述 */
    @Excel(name = "体力要求描述")
    private String physicalRequirements;

    /** 福利待遇 */
    @Excel(name = "福利待遇")
    private String benefits;

    /** 联系人 */
    @Excel(name = "联系人")
    @Size(min = 0, max = 100, message = "联系人不能超过100个字符")
    private String contactPerson;

    /** 联系电话 */
    @Excel(name = "联系电话")
    @Size(min = 0, max = 20, message = "联系电话不能超过20个字符")
    private String contactPhone;

    /** 联系邮箱 */
    @Excel(name = "联系邮箱")
    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 100, message = "联系邮箱不能超过100个字符")
    private String contactEmail;

    /** 微信号 */
    @Excel(name = "微信号")
    @Size(min = 0, max = 100, message = "微信号不能超过100个字符")
    private String contactWechat;

    /** 公司名称 */
    @Excel(name = "公司名称")
    @Size(min = 0, max = 200, message = "公司名称不能超过200个字符")
    private String companyName;

    /** 公司地址 */
    @Excel(name = "公司地址")
    @Size(min = 0, max = 500, message = "公司地址不能超过500个字符")
    private String companyAddress;

    /** 公司描述 */
    @Excel(name = "公司描述")
    private String companyDescription;

    /** 紧急程度（urgent/high/normal/low） */
    @Excel(name = "紧急程度")
    private String urgencyLevel;

    /** 招聘人数 */
    @Excel(name = "招聘人数")
    @Min(value = 1, message = "招聘人数不能小于1")
    private Integer positionsAvailable;

    /** 已招聘人数 */
    @Excel(name = "已招聘人数")
    private Integer positionsFilled;

    /** 申请截止时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date applicationDeadline;

    /** 状态（draft/published/paused/closed/completed） */
    @Excel(name = "状态", readConverterExp = "draft=草稿,published=已发布,paused=已暂停,closed=已关闭,completed=已完成")
    private String status;

    /** 浏览次数 */
    @Excel(name = "浏览次数")
    private Integer viewCount;

    /** 申请次数 */
    @Excel(name = "申请次数")
    private Integer applicationCount;

    /** 发布者用户ID */
    @Excel(name = "发布者用户ID")
    @NotNull(message = "发布者用户ID不能为空")
    private Long publisherUserId;

    /** 发布者类型（employer/agency/individual） */
    @Excel(name = "发布者类型")
    private String publisherType;

    /** 是否已验证（0否 1是） */
    @Excel(name = "是否已验证", readConverterExp = "0=否,1=是")
    private Integer isVerified;

    /** 验证时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "验证时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date verificationTime;

    /** 是否推荐（0否 1是） */
    @Excel(name = "是否推荐", readConverterExp = "0=否,1=是")
    private Integer featured;

    /** 删除标志（0代表存在 2代表删除） */
    @TableField("del_flag")
    private String delFlag;

    // 关联查询字段
    /** 发布者用户名 */
    @TableField(exist = false)
    private String publisherUserName;

    /** 发布者昵称 */
    @TableField(exist = false)
    private String publisherNickName;

    /** 发布者手机号 */
    @TableField(exist = false)
    private String publisherPhone;

    // Getters and Setters
    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public String getJobDescription() {
        return jobDescription;
    }

    public void setJobDescription(String jobDescription) {
        this.jobDescription = jobDescription;
    }

    public String getJobType() {
        return jobType;
    }

    public void setJobType(String jobType) {
        this.jobType = jobType;
    }

    public String getJobCategory() {
        return jobCategory;
    }

    public void setJobCategory(String jobCategory) {
        this.jobCategory = jobCategory;
    }

    public String getWorkLocation() {
        return workLocation;
    }

    public void setWorkLocation(String workLocation) {
        this.workLocation = workLocation;
    }

    public String getWorkAddress() {
        return workAddress;
    }

    public void setWorkAddress(String workAddress) {
        this.workAddress = workAddress;
    }

    public String getSalaryType() {
        return salaryType;
    }

    public void setSalaryType(String salaryType) {
        this.salaryType = salaryType;
    }

    public BigDecimal getSalaryMin() {
        return salaryMin;
    }

    public void setSalaryMin(BigDecimal salaryMin) {
        this.salaryMin = salaryMin;
    }

    public BigDecimal getSalaryMax() {
        return salaryMax;
    }

    public void setSalaryMax(BigDecimal salaryMax) {
        this.salaryMax = salaryMax;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Integer getWorkHoursPerDay() {
        return workHoursPerDay;
    }

    public void setWorkHoursPerDay(Integer workHoursPerDay) {
        this.workHoursPerDay = workHoursPerDay;
    }

    public Integer getWorkDaysPerWeek() {
        return workDaysPerWeek;
    }

    public void setWorkDaysPerWeek(Integer workDaysPerWeek) {
        this.workDaysPerWeek = workDaysPerWeek;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getWorkTimeFlexible() {
        return workTimeFlexible;
    }

    public void setWorkTimeFlexible(Integer workTimeFlexible) {
        this.workTimeFlexible = workTimeFlexible;
    }

    public String getExperienceRequired() {
        return experienceRequired;
    }

    public void setExperienceRequired(String experienceRequired) {
        this.experienceRequired = experienceRequired;
    }

    public String getEducationRequired() {
        return educationRequired;
    }

    public void setEducationRequired(String educationRequired) {
        this.educationRequired = educationRequired;
    }

    public Integer getAgeMin() {
        return ageMin;
    }

    public void setAgeMin(Integer ageMin) {
        this.ageMin = ageMin;
    }

    public Integer getAgeMax() {
        return ageMax;
    }

    public void setAgeMax(Integer ageMax) {
        this.ageMax = ageMax;
    }

    public String getGenderRequirement() {
        return genderRequirement;
    }

    public void setGenderRequirement(String genderRequirement) {
        this.genderRequirement = genderRequirement;
    }

    public String getSkillsRequired() {
        return skillsRequired;
    }

    public void setSkillsRequired(String skillsRequired) {
        this.skillsRequired = skillsRequired;
    }

    public String getLanguageRequired() {
        return languageRequired;
    }

    public void setLanguageRequired(String languageRequired) {
        this.languageRequired = languageRequired;
    }

    public String getPhysicalRequirements() {
        return physicalRequirements;
    }

    public void setPhysicalRequirements(String physicalRequirements) {
        this.physicalRequirements = physicalRequirements;
    }

    public String getBenefits() {
        return benefits;
    }

    public void setBenefits(String benefits) {
        this.benefits = benefits;
    }

    public String getContactPerson() {
        return contactPerson;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getContactEmail() {
        return contactEmail;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getContactWechat() {
        return contactWechat;
    }

    public void setContactWechat(String contactWechat) {
        this.contactWechat = contactWechat;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getCompanyDescription() {
        return companyDescription;
    }

    public void setCompanyDescription(String companyDescription) {
        this.companyDescription = companyDescription;
    }

    public String getUrgencyLevel() {
        return urgencyLevel;
    }

    public void setUrgencyLevel(String urgencyLevel) {
        this.urgencyLevel = urgencyLevel;
    }

    public Integer getPositionsAvailable() {
        return positionsAvailable;
    }

    public void setPositionsAvailable(Integer positionsAvailable) {
        this.positionsAvailable = positionsAvailable;
    }

    public Integer getPositionsFilled() {
        return positionsFilled;
    }

    public void setPositionsFilled(Integer positionsFilled) {
        this.positionsFilled = positionsFilled;
    }

    public Date getApplicationDeadline() {
        return applicationDeadline;
    }

    public void setApplicationDeadline(Date applicationDeadline) {
        this.applicationDeadline = applicationDeadline;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getViewCount() {
        return viewCount;
    }

    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }

    public Integer getApplicationCount() {
        return applicationCount;
    }

    public void setApplicationCount(Integer applicationCount) {
        this.applicationCount = applicationCount;
    }

    public Long getPublisherUserId() {
        return publisherUserId;
    }

    public void setPublisherUserId(Long publisherUserId) {
        this.publisherUserId = publisherUserId;
    }

    public String getPublisherType() {
        return publisherType;
    }

    public void setPublisherType(String publisherType) {
        this.publisherType = publisherType;
    }

    public Integer getIsVerified() {
        return isVerified;
    }

    public void setIsVerified(Integer isVerified) {
        this.isVerified = isVerified;
    }

    public Date getVerificationTime() {
        return verificationTime;
    }

    public void setVerificationTime(Date verificationTime) {
        this.verificationTime = verificationTime;
    }

    public Integer getFeatured() {
        return featured;
    }

    public void setFeatured(Integer featured) {
        this.featured = featured;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getPublisherUserName() {
        return publisherUserName;
    }

    public void setPublisherUserName(String publisherUserName) {
        this.publisherUserName = publisherUserName;
    }

    public String getPublisherNickName() {
        return publisherNickName;
    }

    public void setPublisherNickName(String publisherNickName) {
        this.publisherNickName = publisherNickName;
    }

    public String getPublisherPhone() {
        return publisherPhone;
    }

    public void setPublisherPhone(String publisherPhone) {
        this.publisherPhone = publisherPhone;
    }

    @Override
    public String toString() {
        return "JobPosting{" +
                "jobId=" + jobId +
                ", jobTitle='" + jobTitle + '\'' +
                ", jobDescription='" + jobDescription + '\'' +
                ", jobType='" + jobType + '\'' +
                ", jobCategory='" + jobCategory + '\'' +
                ", workLocation='" + workLocation + '\'' +
                ", workAddress='" + workAddress + '\'' +
                ", salaryType='" + salaryType + '\'' +
                ", salaryMin=" + salaryMin +
                ", salaryMax=" + salaryMax +
                ", currency='" + currency + '\'' +
                ", workHoursPerDay=" + workHoursPerDay +
                ", workDaysPerWeek=" + workDaysPerWeek +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", workTimeFlexible=" + workTimeFlexible +
                ", experienceRequired='" + experienceRequired + '\'' +
                ", educationRequired='" + educationRequired + '\'' +
                ", ageMin=" + ageMin +
                ", ageMax=" + ageMax +
                ", genderRequirement='" + genderRequirement + '\'' +
                ", skillsRequired='" + skillsRequired + '\'' +
                ", languageRequired='" + languageRequired + '\'' +
                ", physicalRequirements='" + physicalRequirements + '\'' +
                ", benefits='" + benefits + '\'' +
                ", contactPerson='" + contactPerson + '\'' +
                ", contactPhone='" + contactPhone + '\'' +
                ", contactEmail='" + contactEmail + '\'' +
                ", contactWechat='" + contactWechat + '\'' +
                ", companyName='" + companyName + '\'' +
                ", companyAddress='" + companyAddress + '\'' +
                ", companyDescription='" + companyDescription + '\'' +
                ", urgencyLevel='" + urgencyLevel + '\'' +
                ", positionsAvailable=" + positionsAvailable +
                ", positionsFilled=" + positionsFilled +
                ", applicationDeadline=" + applicationDeadline +
                ", status='" + status + '\'' +
                ", viewCount=" + viewCount +
                ", applicationCount=" + applicationCount +
                ", publisherUserId=" + publisherUserId +
                ", publisherType='" + publisherType + '\'' +
                ", isVerified=" + isVerified +
                ", verificationTime=" + verificationTime +
                ", featured=" + featured +
                ", delFlag='" + delFlag + '\'' +
                '}';
    }
}
