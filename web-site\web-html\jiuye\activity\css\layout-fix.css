/* 布局修复专用CSS */

/* 强制重置主要布局容器 */
.main-layout-container {
    display: flex !important;
    gap: 20px !important;
    align-items: flex-start !important;
    width: 100% !important;
    max-width: 1400px !important;
    margin: 0 auto !important;
    box-sizing: border-box !important;
    position: relative !important;
    overflow: visible !important;
    clear: both !important;
}

/* 左侧内容区域 */
.left-main-content {
    flex: 1 !important;
    min-width: 0 !important;
    width: calc(100% - 420px) !important;
    max-width: calc(100% - 420px) !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    float: none !important;
    clear: none !important;
    position: relative !important;
}

/* 右侧边栏 */
.right-sidebar-content {
    flex: 0 0 400px !important;
    width: 400px !important;
    min-width: 400px !important;
    max-width: 400px !important;
    position: sticky !important;
    top: 20px !important;
    align-self: flex-start !important;
    height: fit-content !important;
    background: rgba(255, 255, 255, 0.95) !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    z-index: 10 !important;
    float: none !important;
    clear: none !important;
}

/* 左侧内容盒子 */
.leftBox {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    position: relative !important;
    float: none !important;
    clear: none !important;
}

/* 信息网格 */
.info-grid-new {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 15px !important;
}

.info-item-new {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
}

/* 标题区域 */
.title-section-new,
.training-title-new {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    overflow: hidden !important;
    word-wrap: break-word !important;
}

/* 推荐项目 */
.recommendation-item-new {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    word-wrap: break-word !important;
    overflow: hidden !important;
}

.recommendation-title-new {
    width: 100% !important;
    max-width: 100% !important;
    box-sizing: border-box !important;
    word-wrap: break-word !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    display: -webkit-box !important;
    -webkit-line-clamp: 2 !important;
    -webkit-box-orient: vertical !important;
}

/* 清除浮动 */
.main-layout-container::after {
    content: "" !important;
    display: table !important;
    clear: both !important;
}

/* 容器重置 */
.conAuto3 {
    overflow: visible !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .main-layout-container {
        gap: 15px !important;
        max-width: 100% !important;
        padding: 0 10px !important;
    }

    .left-main-content {
        width: calc(100% - 380px) !important;
        max-width: calc(100% - 380px) !important;
    }

    .right-sidebar-content {
        flex: 0 0 360px !important;
        width: 360px !important;
        min-width: 360px !important;
        max-width: 360px !important;
    }
}

@media (max-width: 1200px) {
    .main-layout-container {
        gap: 12px !important;
        padding: 0 8px !important;
    }

    .left-main-content {
        width: calc(100% - 340px) !important;
        max-width: calc(100% - 340px) !important;
    }

    .right-sidebar-content {
        flex: 0 0 320px !important;
        width: 320px !important;
        min-width: 320px !important;
        max-width: 320px !important;
    }
}

@media (max-width: 768px) {
    .main-layout-container {
        flex-direction: column !important;
        gap: 20px !important;
    }

    .left-main-content,
    .right-sidebar-content {
        flex: none !important;
        min-width: auto !important;
        max-width: none !important;
        width: 100% !important;
        position: static !important;
    }

    .right-sidebar-content {
        order: 2 !important;
        margin-top: 20px !important;
    }

    .left-main-content {
        order: 1 !important;
    }

    .info-grid-new {
        grid-template-columns: 1fr !important;
    }
}

@media (max-width: 480px) {
    .main-layout-container {
        padding: 0 5px !important;
    }
    
    .info-grid-new {
        gap: 10px !important;
    }
}
