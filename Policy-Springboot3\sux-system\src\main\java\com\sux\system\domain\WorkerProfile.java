package com.sux.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;
import jakarta.validation.constraints.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 零工信息对象 worker_profile
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@TableName("worker_profile")
public class WorkerProfile extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 零工ID */
    @TableId(type = IdType.AUTO)
    private Long workerId;

    /** 用户ID */
    @Excel(name = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /** 真实姓名 */
    @Excel(name = "真实姓名")
    @NotBlank(message = "真实姓名不能为空")
    @Size(min = 0, max = 100, message = "真实姓名不能超过100个字符")
    private String realName;

    /** 昵称 */
    @Excel(name = "昵称")
    @Size(min = 0, max = 100, message = "昵称不能超过100个字符")
    private String nickname;

    /** 性别（male/female） */
    @Excel(name = "性别")
    private String gender;

    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date birthDate;

    /** 年龄 */
    @Excel(name = "年龄")
    @Min(value = 16, message = "年龄不能小于16岁")
    @Max(value = 80, message = "年龄不能大于80岁")
    private Integer age;

    /** 手机号 */
    @Excel(name = "手机号")
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    /** 邮箱 */
    @Excel(name = "邮箱")
    @Email(message = "邮箱格式不正确")
    @Size(min = 0, max = 100, message = "邮箱不能超过100个字符")
    private String email;

    /** 微信号 */
    @Excel(name = "微信号")
    @Size(min = 0, max = 100, message = "微信号不能超过100个字符")
    private String wechat;

    /** 身份证号 */
    @Excel(name = "身份证号")
    @Pattern(regexp = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$", 
             message = "身份证号格式不正确")
    private String idCard;

    /** 当前所在地 */
    @Excel(name = "当前所在地")
    @Size(min = 0, max = 200, message = "当前所在地不能超过200个字符")
    private String currentLocation;

    /** 详细地址 */
    @Excel(name = "详细地址")
    @Size(min = 0, max = 500, message = "详细地址不能超过500个字符")
    private String currentAddress;

    /** 可工作地点（JSON数组） */
    @Excel(name = "可工作地点")
    private String workLocations;

    /** 学历水平 */
    @Excel(name = "学历水平")
    @Size(min = 0, max = 50, message = "学历水平不能超过50个字符")
    private String educationLevel;

    /** 工作经验年数 */
    @Excel(name = "工作经验年数")
    @Min(value = 0, message = "工作经验年数不能小于0")
    private Integer workExperienceYears;

    /** 工作类别偏好（JSON数组） */
    @Excel(name = "工作类别偏好")
    private String workCategories;

    /** 偏好工作类型（JSON数组） */
    @Excel(name = "偏好工作类型")
    private String jobTypesPreferred;

    /** 技能列表（JSON格式） */
    @Excel(name = "技能列表")
    private String skills;

    /** 证书资质（JSON格式） */
    @Excel(name = "证书资质")
    private String certifications;

    /** 语言能力（JSON格式） */
    @Excel(name = "语言能力")
    private String languages;

    /** 工作时间偏好（flexible/fixed/part_time/full_time） */
    @Excel(name = "工作时间偏好")
    private String workTimePreference;

    /** 期望最低薪资 */
    @Excel(name = "期望最低薪资")
    @DecimalMin(value = "0.0", message = "期望最低薪资不能小于0")
    private BigDecimal salaryExpectationMin;

    /** 期望最高薪资 */
    @Excel(name = "期望最高薪资")
    @DecimalMin(value = "0.0", message = "期望最高薪资不能小于0")
    private BigDecimal salaryExpectationMax;

    /** 薪资类型偏好（hourly/daily/monthly/piece） */
    @Excel(name = "薪资类型偏好")
    private String salaryTypePreference;

    /** 可开始工作日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "可开始工作日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date availabilityStartDate;

    /** 可工作截止日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "可工作截止日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date availabilityEndDate;

    /** 每周可工作天数 */
    @Excel(name = "每周可工作天数")
    @Min(value = 1, message = "每周可工作天数不能小于1")
    @Max(value = 7, message = "每周可工作天数不能大于7")
    private Integer workDaysPerWeek;

    /** 每日可工作小时数 */
    @Excel(name = "每日可工作小时数")
    @Min(value = 1, message = "每日可工作小时数不能小于1")
    @Max(value = 24, message = "每日可工作小时数不能大于24")
    private Integer workHoursPerDay;

    /** 身体状况 */
    @Excel(name = "身体状况")
    @Size(min = 0, max = 100, message = "身体状况不能超过100个字符")
    private String physicalCondition;

    /** 是否有健康证（0否 1是） */
    @Excel(name = "是否有健康证", readConverterExp = "0=否,1=是")
    private Integer healthCertificate;

    /** 是否通过无犯罪记录检查（0否 1是） */
    @Excel(name = "是否通过无犯罪记录检查", readConverterExp = "0=否,1=是")
    private Integer criminalRecordCheck;

    /** 紧急联系人姓名 */
    @Excel(name = "紧急联系人姓名")
    @Size(min = 0, max = 100, message = "紧急联系人姓名不能超过100个字符")
    private String emergencyContactName;

    /** 紧急联系人电话 */
    @Excel(name = "紧急联系人电话")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "紧急联系人电话格式不正确")
    private String emergencyContactPhone;

    /** 紧急联系人关系 */
    @Excel(name = "紧急联系人关系")
    @Size(min = 0, max = 50, message = "紧急联系人关系不能超过50个字符")
    private String emergencyContactRelation;

    /** 头像照片URL */
    @Excel(name = "头像照片URL")
    @Size(min = 0, max = 500, message = "头像照片URL不能超过500个字符")
    private String profilePhoto;

    /** 简历文件URL */
    @Excel(name = "简历文件URL")
    @Size(min = 0, max = 500, message = "简历文件URL不能超过500个字符")
    private String resumeFile;

    /** 作品集文件URLs（JSON数组） */
    @Excel(name = "作品集文件URLs")
    private String portfolioFiles;

    /** 工作履历（JSON格式） */
    @Excel(name = "工作履历")
    private String workHistory;

    /** 推荐人信息（JSON格式） */
    @Excel(name = "推荐人信息")
    private String references;

    /** 自我介绍 */
    @Excel(name = "自我介绍")
    private String selfIntroduction;

    /** 特殊说明 */
    @Excel(name = "特殊说明")
    private String specialNotes;

    /** 平均评分 */
    @Excel(name = "平均评分")
    @DecimalMin(value = "0.0", message = "平均评分不能小于0")
    @DecimalMax(value = "5.0", message = "平均评分不能大于5")
    private BigDecimal ratingAverage;

    /** 评分次数 */
    @Excel(name = "评分次数")
    @Min(value = 0, message = "评分次数不能小于0")
    private Integer ratingCount;

    /** 完成工作数量 */
    @Excel(name = "完成工作数量")
    @Min(value = 0, message = "完成工作数量不能小于0")
    private Integer completedJobs;

    /** 成功率 */
    @Excel(name = "成功率")
    @DecimalMin(value = "0.0", message = "成功率不能小于0")
    @DecimalMax(value = "100.0", message = "成功率不能大于100")
    private BigDecimal successRate;

    /** 状态（active/inactive/suspended/banned） */
    @Excel(name = "状态", readConverterExp = "active=活跃,inactive=不活跃,suspended=暂停,banned=禁用")
    private String status;

    /** 是否已实名验证（0否 1是） */
    @Excel(name = "是否已实名验证", readConverterExp = "0=否,1=是")
    private Integer isVerified;

    /** 验证时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "验证时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date verificationTime;

    /** 最后活跃时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后活跃时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastActiveTime;

    /** 删除标志（0代表存在 2代表删除） */
    @TableField("del_flag")
    private String delFlag;

    // 关联查询字段
    /** 用户名 */
    @TableField(exist = false)
    private String userName;

    /** 用户昵称 */
    @TableField(exist = false)
    private String userNickName;

    // Getters and Setters
    public Long getWorkerId() {
        return workerId;
    }

    public void setWorkerId(Long workerId) {
        this.workerId = workerId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Date getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(Date birthDate) {
        this.birthDate = birthDate;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getWechat() {
        return wechat;
    }

    public void setWechat(String wechat) {
        this.wechat = wechat;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getCurrentLocation() {
        return currentLocation;
    }

    public void setCurrentLocation(String currentLocation) {
        this.currentLocation = currentLocation;
    }

    public String getCurrentAddress() {
        return currentAddress;
    }

    public void setCurrentAddress(String currentAddress) {
        this.currentAddress = currentAddress;
    }

    public String getWorkLocations() {
        return workLocations;
    }

    public void setWorkLocations(String workLocations) {
        this.workLocations = workLocations;
    }

    public String getEducationLevel() {
        return educationLevel;
    }

    public void setEducationLevel(String educationLevel) {
        this.educationLevel = educationLevel;
    }

    public Integer getWorkExperienceYears() {
        return workExperienceYears;
    }

    public void setWorkExperienceYears(Integer workExperienceYears) {
        this.workExperienceYears = workExperienceYears;
    }

    public String getWorkCategories() {
        return workCategories;
    }

    public void setWorkCategories(String workCategories) {
        this.workCategories = workCategories;
    }

    public String getJobTypesPreferred() {
        return jobTypesPreferred;
    }

    public void setJobTypesPreferred(String jobTypesPreferred) {
        this.jobTypesPreferred = jobTypesPreferred;
    }

    public String getSkills() {
        return skills;
    }

    public void setSkills(String skills) {
        this.skills = skills;
    }

    public String getCertifications() {
        return certifications;
    }

    public void setCertifications(String certifications) {
        this.certifications = certifications;
    }

    public String getLanguages() {
        return languages;
    }

    public void setLanguages(String languages) {
        this.languages = languages;
    }

    public String getWorkTimePreference() {
        return workTimePreference;
    }

    public void setWorkTimePreference(String workTimePreference) {
        this.workTimePreference = workTimePreference;
    }

    public BigDecimal getSalaryExpectationMin() {
        return salaryExpectationMin;
    }

    public void setSalaryExpectationMin(BigDecimal salaryExpectationMin) {
        this.salaryExpectationMin = salaryExpectationMin;
    }

    public BigDecimal getSalaryExpectationMax() {
        return salaryExpectationMax;
    }

    public void setSalaryExpectationMax(BigDecimal salaryExpectationMax) {
        this.salaryExpectationMax = salaryExpectationMax;
    }

    public String getSalaryTypePreference() {
        return salaryTypePreference;
    }

    public void setSalaryTypePreference(String salaryTypePreference) {
        this.salaryTypePreference = salaryTypePreference;
    }

    public Date getAvailabilityStartDate() {
        return availabilityStartDate;
    }

    public void setAvailabilityStartDate(Date availabilityStartDate) {
        this.availabilityStartDate = availabilityStartDate;
    }

    public Date getAvailabilityEndDate() {
        return availabilityEndDate;
    }

    public void setAvailabilityEndDate(Date availabilityEndDate) {
        this.availabilityEndDate = availabilityEndDate;
    }

    public Integer getWorkDaysPerWeek() {
        return workDaysPerWeek;
    }

    public void setWorkDaysPerWeek(Integer workDaysPerWeek) {
        this.workDaysPerWeek = workDaysPerWeek;
    }

    public Integer getWorkHoursPerDay() {
        return workHoursPerDay;
    }

    public void setWorkHoursPerDay(Integer workHoursPerDay) {
        this.workHoursPerDay = workHoursPerDay;
    }

    public String getPhysicalCondition() {
        return physicalCondition;
    }

    public void setPhysicalCondition(String physicalCondition) {
        this.physicalCondition = physicalCondition;
    }

    public Integer getHealthCertificate() {
        return healthCertificate;
    }

    public void setHealthCertificate(Integer healthCertificate) {
        this.healthCertificate = healthCertificate;
    }

    public Integer getCriminalRecordCheck() {
        return criminalRecordCheck;
    }

    public void setCriminalRecordCheck(Integer criminalRecordCheck) {
        this.criminalRecordCheck = criminalRecordCheck;
    }

    public String getEmergencyContactName() {
        return emergencyContactName;
    }

    public void setEmergencyContactName(String emergencyContactName) {
        this.emergencyContactName = emergencyContactName;
    }

    public String getEmergencyContactPhone() {
        return emergencyContactPhone;
    }

    public void setEmergencyContactPhone(String emergencyContactPhone) {
        this.emergencyContactPhone = emergencyContactPhone;
    }

    public String getEmergencyContactRelation() {
        return emergencyContactRelation;
    }

    public void setEmergencyContactRelation(String emergencyContactRelation) {
        this.emergencyContactRelation = emergencyContactRelation;
    }

    public String getProfilePhoto() {
        return profilePhoto;
    }

    public void setProfilePhoto(String profilePhoto) {
        this.profilePhoto = profilePhoto;
    }

    public String getResumeFile() {
        return resumeFile;
    }

    public void setResumeFile(String resumeFile) {
        this.resumeFile = resumeFile;
    }

    public String getPortfolioFiles() {
        return portfolioFiles;
    }

    public void setPortfolioFiles(String portfolioFiles) {
        this.portfolioFiles = portfolioFiles;
    }

    public String getWorkHistory() {
        return workHistory;
    }

    public void setWorkHistory(String workHistory) {
        this.workHistory = workHistory;
    }

    public String getReferences() {
        return references;
    }

    public void setReferences(String references) {
        this.references = references;
    }

    public String getSelfIntroduction() {
        return selfIntroduction;
    }

    public void setSelfIntroduction(String selfIntroduction) {
        this.selfIntroduction = selfIntroduction;
    }

    public String getSpecialNotes() {
        return specialNotes;
    }

    public void setSpecialNotes(String specialNotes) {
        this.specialNotes = specialNotes;
    }

    public BigDecimal getRatingAverage() {
        return ratingAverage;
    }

    public void setRatingAverage(BigDecimal ratingAverage) {
        this.ratingAverage = ratingAverage;
    }

    public Integer getRatingCount() {
        return ratingCount;
    }

    public void setRatingCount(Integer ratingCount) {
        this.ratingCount = ratingCount;
    }

    public Integer getCompletedJobs() {
        return completedJobs;
    }

    public void setCompletedJobs(Integer completedJobs) {
        this.completedJobs = completedJobs;
    }

    public BigDecimal getSuccessRate() {
        return successRate;
    }

    public void setSuccessRate(BigDecimal successRate) {
        this.successRate = successRate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getIsVerified() {
        return isVerified;
    }

    public void setIsVerified(Integer isVerified) {
        this.isVerified = isVerified;
    }

    public Date getVerificationTime() {
        return verificationTime;
    }

    public void setVerificationTime(Date verificationTime) {
        this.verificationTime = verificationTime;
    }

    public Date getLastActiveTime() {
        return lastActiveTime;
    }

    public void setLastActiveTime(Date lastActiveTime) {
        this.lastActiveTime = lastActiveTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserNickName() {
        return userNickName;
    }

    public void setUserNickName(String userNickName) {
        this.userNickName = userNickName;
    }

    @Override
    public String toString() {
        return "WorkerProfile{" +
                "workerId=" + workerId +
                ", userId=" + userId +
                ", realName='" + realName + '\'' +
                ", nickname='" + nickname + '\'' +
                ", gender='" + gender + '\'' +
                ", birthDate=" + birthDate +
                ", age=" + age +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", wechat='" + wechat + '\'' +
                ", idCard='" + idCard + '\'' +
                ", currentLocation='" + currentLocation + '\'' +
                ", currentAddress='" + currentAddress + '\'' +
                ", workLocations='" + workLocations + '\'' +
                ", educationLevel='" + educationLevel + '\'' +
                ", workExperienceYears=" + workExperienceYears +
                ", workCategories='" + workCategories + '\'' +
                ", jobTypesPreferred='" + jobTypesPreferred + '\'' +
                ", skills='" + skills + '\'' +
                ", certifications='" + certifications + '\'' +
                ", languages='" + languages + '\'' +
                ", workTimePreference='" + workTimePreference + '\'' +
                ", salaryExpectationMin=" + salaryExpectationMin +
                ", salaryExpectationMax=" + salaryExpectationMax +
                ", salaryTypePreference='" + salaryTypePreference + '\'' +
                ", availabilityStartDate=" + availabilityStartDate +
                ", availabilityEndDate=" + availabilityEndDate +
                ", workDaysPerWeek=" + workDaysPerWeek +
                ", workHoursPerDay=" + workHoursPerDay +
                ", physicalCondition='" + physicalCondition + '\'' +
                ", healthCertificate=" + healthCertificate +
                ", criminalRecordCheck=" + criminalRecordCheck +
                ", emergencyContactName='" + emergencyContactName + '\'' +
                ", emergencyContactPhone='" + emergencyContactPhone + '\'' +
                ", emergencyContactRelation='" + emergencyContactRelation + '\'' +
                ", profilePhoto='" + profilePhoto + '\'' +
                ", resumeFile='" + resumeFile + '\'' +
                ", portfolioFiles='" + portfolioFiles + '\'' +
                ", workHistory='" + workHistory + '\'' +
                ", references='" + references + '\'' +
                ", selfIntroduction='" + selfIntroduction + '\'' +
                ", specialNotes='" + specialNotes + '\'' +
                ", ratingAverage=" + ratingAverage +
                ", ratingCount=" + ratingCount +
                ", completedJobs=" + completedJobs +
                ", successRate=" + successRate +
                ", status='" + status + '\'' +
                ", isVerified=" + isVerified +
                ", verificationTime=" + verificationTime +
                ", lastActiveTime=" + lastActiveTime +
                ", delFlag='" + delFlag + '\'' +
                '}';
    }
}
