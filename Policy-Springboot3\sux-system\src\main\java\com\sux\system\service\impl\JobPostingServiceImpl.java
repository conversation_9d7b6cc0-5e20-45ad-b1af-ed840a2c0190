package com.sux.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sux.common.utils.DateUtils;
import com.sux.common.utils.SecurityUtils;
import com.sux.common.utils.StringUtils;
import com.sux.system.domain.JobPosting;
import com.sux.system.domain.WorkerProfile;
import com.sux.system.mapper.JobPostingMapper;
import com.sux.system.mapper.WorkerProfileMapper;
import com.sux.system.service.IJobPostingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 招聘信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
public class JobPostingServiceImpl extends ServiceImpl<JobPostingMapper, JobPosting> implements IJobPostingService
{
    @Autowired
    private JobPostingMapper jobPostingMapper;
    
    @Autowired
    private WorkerProfileMapper workerProfileMapper;

    /**
     * 查询招聘信息列表
     * 
     * @param jobPosting 招聘信息
     * @return 招聘信息
     */
    @Override
    public List<JobPosting> selectJobPostingList(JobPosting jobPosting)
    {
        return jobPostingMapper.selectJobPostingList(jobPosting);
    }

    /**
     * 查询招聘信息
     * 
     * @param jobId 招聘信息主键
     * @return 招聘信息
     */
    @Override
    public JobPosting selectJobPostingByJobId(Long jobId)
    {
        return jobPostingMapper.selectJobPostingByJobId(jobId);
    }

    /**
     * 新增招聘信息
     * 
     * @param jobPosting 招聘信息
     * @return 结果
     */
    @Override
    public int insertJobPosting(JobPosting jobPosting)
    {
        jobPosting.setCreateId(SecurityUtils.getUserId());
        jobPosting.setCreateTime(DateUtils.getNowDate());
        jobPosting.setPublisherUserId(SecurityUtils.getUserId());
        
        // 设置默认值
        if (StringUtils.isEmpty(jobPosting.getStatus())) {
            jobPosting.setStatus("draft");
        }
        if (StringUtils.isEmpty(jobPosting.getCurrency())) {
            jobPosting.setCurrency("CNY");
        }
        if (StringUtils.isEmpty(jobPosting.getUrgencyLevel())) {
            jobPosting.setUrgencyLevel("normal");
        }
        if (StringUtils.isEmpty(jobPosting.getPublisherType())) {
            jobPosting.setPublisherType("employer");
        }
        if (jobPosting.getPositionsAvailable() == null) {
            jobPosting.setPositionsAvailable(1);
        }
        if (jobPosting.getPositionsFilled() == null) {
            jobPosting.setPositionsFilled(0);
        }
        if (jobPosting.getViewCount() == null) {
            jobPosting.setViewCount(0);
        }
        if (jobPosting.getApplicationCount() == null) {
            jobPosting.setApplicationCount(0);
        }
        if (jobPosting.getIsVerified() == null) {
            jobPosting.setIsVerified(0);
        }
        if (jobPosting.getFeatured() == null) {
            jobPosting.setFeatured(0);
        }
        if (jobPosting.getWorkTimeFlexible() == null) {
            jobPosting.setWorkTimeFlexible(0);
        }
        
        return jobPostingMapper.insertJobPosting(jobPosting);
    }

    /**
     * 修改招聘信息
     * 
     * @param jobPosting 招聘信息
     * @return 结果
     */
    @Override
    public int updateJobPosting(JobPosting jobPosting)
    {
        jobPosting.setUpdateId(SecurityUtils.getUserId());
        jobPosting.setUpdateTime(DateUtils.getNowDate());
        return jobPostingMapper.updateJobPosting(jobPosting);
    }

    /**
     * 批量删除招聘信息
     * 
     * @param jobIds 需要删除的招聘信息主键
     * @return 结果
     */
    @Override
    public int deleteJobPostingByJobIds(Long[] jobIds)
    {
        return jobPostingMapper.deleteJobPostingByJobIds(jobIds);
    }

    /**
     * 删除招聘信息信息
     * 
     * @param jobId 招聘信息主键
     * @return 结果
     */
    @Override
    public int deleteJobPostingByJobId(Long jobId)
    {
        return jobPostingMapper.deleteJobPostingByJobId(jobId);
    }

    /**
     * 查询我发布的招聘信息列表
     * 
     * @param jobPosting 招聘信息
     * @param publisherUserId 发布者用户ID
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectMyJobPostingList(JobPosting jobPosting, Long publisherUserId)
    {
        return jobPostingMapper.selectMyJobPostingList(jobPosting, publisherUserId);
    }

    /**
     * 查询已发布的招聘信息列表（公开接口）
     * 
     * @param jobPosting 招聘信息
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectPublishedJobPostingList(JobPosting jobPosting)
    {
        return jobPostingMapper.selectPublishedJobPostingList(jobPosting);
    }

    /**
     * 发布招聘信息
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    @Override
    public int publishJobPosting(Long jobId)
    {
        JobPosting jobPosting = new JobPosting();
        jobPosting.setJobId(jobId);
        jobPosting.setStatus("published");
        jobPosting.setUpdateId(SecurityUtils.getUserId());
        jobPosting.setUpdateTime(DateUtils.getNowDate());
        return jobPostingMapper.updateJobPosting(jobPosting);
    }

    /**
     * 暂停招聘信息
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    @Override
    public int pauseJobPosting(Long jobId)
    {
        JobPosting jobPosting = new JobPosting();
        jobPosting.setJobId(jobId);
        jobPosting.setStatus("paused");
        jobPosting.setUpdateId(SecurityUtils.getUserId());
        jobPosting.setUpdateTime(DateUtils.getNowDate());
        return jobPostingMapper.updateJobPosting(jobPosting);
    }

    /**
     * 关闭招聘信息
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    @Override
    public int closeJobPosting(Long jobId)
    {
        JobPosting jobPosting = new JobPosting();
        jobPosting.setJobId(jobId);
        jobPosting.setStatus("closed");
        jobPosting.setUpdateId(SecurityUtils.getUserId());
        jobPosting.setUpdateTime(DateUtils.getNowDate());
        return jobPostingMapper.updateJobPosting(jobPosting);
    }

    /**
     * 完成招聘信息
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    @Override
    public int completeJobPosting(Long jobId)
    {
        JobPosting jobPosting = new JobPosting();
        jobPosting.setJobId(jobId);
        jobPosting.setStatus("completed");
        jobPosting.setUpdateId(SecurityUtils.getUserId());
        jobPosting.setUpdateTime(DateUtils.getNowDate());
        return jobPostingMapper.updateJobPosting(jobPosting);
    }

    /**
     * 增加招聘信息浏览次数
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    @Override
    public int increaseViewCount(Long jobId)
    {
        return jobPostingMapper.updateJobPostingViewCount(jobId);
    }

    /**
     * 增加招聘信息申请次数
     * 
     * @param jobId 招聘信息ID
     * @return 结果
     */
    @Override
    public int increaseApplicationCount(Long jobId)
    {
        return jobPostingMapper.updateJobPostingApplicationCount(jobId);
    }

    /**
     * 更新招聘信息已招聘人数
     * 
     * @param jobId 招聘信息ID
     * @param positionsFilled 已招聘人数
     * @return 结果
     */
    @Override
    public int updatePositionsFilled(Long jobId, Integer positionsFilled)
    {
        return jobPostingMapper.updateJobPostingPositionsFilled(jobId, positionsFilled);
    }

    /**
     * 查询热门招聘信息
     * 
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectHotJobPostingList(Integer limit)
    {
        return jobPostingMapper.selectHotJobPostingList(limit);
    }

    /**
     * 查询推荐招聘信息
     * 
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectFeaturedJobPostingList(Integer limit)
    {
        return jobPostingMapper.selectFeaturedJobPostingList(limit);
    }

    /**
     * 查询紧急招聘信息
     * 
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectUrgentJobPostingList(Integer limit)
    {
        return jobPostingMapper.selectUrgentJobPostingList(limit);
    }

    /**
     * 根据工作类别统计招聘信息数量
     * 
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> selectJobPostingCountByCategory()
    {
        return jobPostingMapper.selectJobPostingCountByCategory();
    }

    /**
     * 根据工作地点统计招聘信息数量
     * 
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> selectJobPostingCountByLocation()
    {
        return jobPostingMapper.selectJobPostingCountByLocation();
    }

    /**
     * 根据薪资范围统计招聘信息数量
     * 
     * @return 统计结果
     */
    @Override
    public List<Map<String, Object>> selectJobPostingCountBySalaryRange()
    {
        return jobPostingMapper.selectJobPostingCountBySalaryRange();
    }

    /**
     * 查询即将截止的招聘信息
     * 
     * @param days 天数
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectJobPostingExpiringSoon(Integer days)
    {
        return jobPostingMapper.selectJobPostingExpiringSoon(days);
    }

    /**
     * 查询招聘信息详情（包含发布者信息）
     * 
     * @param jobId 招聘信息ID
     * @return 招聘信息详情
     */
    @Override
    public JobPosting selectJobPostingDetailByJobId(Long jobId)
    {
        return jobPostingMapper.selectJobPostingDetailByJobId(jobId);
    }

    /**
     * 根据关键词搜索招聘信息
     * 
     * @param keyword 关键词
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectJobPostingByKeyword(String keyword)
    {
        return jobPostingMapper.selectJobPostingByKeyword(keyword);
    }

    /**
     * 查询相似的招聘信息
     * 
     * @param jobPosting 招聘信息
     * @param limit 限制数量
     * @return 招聘信息集合
     */
    @Override
    public List<JobPosting> selectSimilarJobPostingList(JobPosting jobPosting, Integer limit)
    {
        return jobPostingMapper.selectSimilarJobPostingList(jobPosting, limit);
    }

    /**
     * 批量更新招聘信息状态
     * 
     * @param jobIds 招聘信息ID数组
     * @param status 状态
     * @return 结果
     */
    @Override
    public int batchUpdateJobPostingStatus(Long[] jobIds, String status)
    {
        return jobPostingMapper.batchUpdateJobPostingStatus(jobIds, status);
    }

    /**
     * 查询招聘信息统计数据
     *
     * @param publisherUserId 发布者用户ID（可选）
     * @return 统计数据
     */
    @Override
    public Map<String, Object> selectJobPostingStatistics(Long publisherUserId)
    {
        return jobPostingMapper.selectJobPostingStatistics(publisherUserId);
    }

    /**
     * 根据零工信息匹配招聘信息
     *
     * @param workerId 零工ID
     * @param limit 限制数量
     * @return 匹配的招聘信息集合
     */
    @Override
    public List<JobPosting> matchJobPostingForWorker(Long workerId, Integer limit)
    {
        // 获取零工信息
        WorkerProfile workerProfile = workerProfileMapper.selectWorkerProfileByWorkerId(workerId);
        if (workerProfile == null) {
            return null;
        }

        // 构建匹配参数
        Map<String, Object> matchParams = new java.util.HashMap<>();

        // 工作地点匹配
        if (StringUtils.isNotEmpty(workerProfile.getCurrentLocation())) {
            matchParams.put("workLocation", workerProfile.getCurrentLocation());
        }

        // 工作类别匹配
        if (StringUtils.isNotEmpty(workerProfile.getWorkCategories())) {
            matchParams.put("workCategories", workerProfile.getWorkCategories());
        }

        // 薪资期望匹配
        if (workerProfile.getSalaryExpectationMin() != null) {
            matchParams.put("salaryMin", workerProfile.getSalaryExpectationMin());
        }
        if (workerProfile.getSalaryExpectationMax() != null) {
            matchParams.put("salaryMax", workerProfile.getSalaryExpectationMax());
        }
        if (StringUtils.isNotEmpty(workerProfile.getSalaryTypePreference())) {
            matchParams.put("salaryType", workerProfile.getSalaryTypePreference());
        }

        // 工作时间偏好匹配
        if (StringUtils.isNotEmpty(workerProfile.getJobTypesPreferred())) {
            matchParams.put("jobTypes", workerProfile.getJobTypesPreferred());
        }

        // 技能匹配
        if (StringUtils.isNotEmpty(workerProfile.getSkills())) {
            matchParams.put("skills", workerProfile.getSkills());
        }

        // 可工作时间匹配
        if (workerProfile.getAvailabilityStartDate() != null) {
            matchParams.put("availabilityStartDate", workerProfile.getAvailabilityStartDate());
        }
        if (workerProfile.getAvailabilityEndDate() != null) {
            matchParams.put("availabilityEndDate", workerProfile.getAvailabilityEndDate());
        }

        matchParams.put("limit", limit);
        matchParams.put("status", "published");

        return jobPostingMapper.selectJobPostingByMatchParams(matchParams);
    }

    /**
     * 智能推荐招聘信息
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 推荐的招聘信息集合
     */
    @Override
    public List<JobPosting> recommendJobPostingForUser(Long userId, Integer limit)
    {
        // 获取用户的零工信息
        WorkerProfile workerProfile = workerProfileMapper.selectWorkerProfileByUserId(userId);
        if (workerProfile != null) {
            return matchJobPostingForWorker(workerProfile.getWorkerId(), limit);
        }

        // 如果没有零工信息，返回推荐招聘信息
        return selectFeaturedJobPostingList(limit);
    }

    /**
     * 校验招聘信息标题是否唯一
     *
     * @param jobPosting 招聘信息
     * @return 结果
     */
    @Override
    public boolean checkJobTitleUnique(JobPosting jobPosting)
    {
        Long jobId = StringUtils.isNull(jobPosting.getJobId()) ? -1L : jobPosting.getJobId();
        JobPosting info = jobPostingMapper.selectOne(
            new com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper<JobPosting>()
                .eq(JobPosting::getJobTitle, jobPosting.getJobTitle())
                .eq(JobPosting::getPublisherUserId, jobPosting.getPublisherUserId())
                .eq(JobPosting::getDelFlag, "0")
        );
        if (StringUtils.isNotNull(info) && info.getJobId().longValue() != jobId.longValue())
        {
            return false;
        }
        return true;
    }

    /**
     * 校验招聘信息是否可以编辑
     *
     * @param jobId 招聘信息ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public boolean checkJobPostingEditable(Long jobId, Long userId)
    {
        JobPosting jobPosting = selectJobPostingByJobId(jobId);
        if (jobPosting == null) {
            return false;
        }

        // 只有发布者可以编辑
        if (!jobPosting.getPublisherUserId().equals(userId)) {
            return false;
        }

        // 已完成的招聘信息不能编辑
        if ("completed".equals(jobPosting.getStatus())) {
            return false;
        }

        return true;
    }

    /**
     * 校验招聘信息是否可以删除
     *
     * @param jobId 招聘信息ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public boolean checkJobPostingDeletable(Long jobId, Long userId)
    {
        JobPosting jobPosting = selectJobPostingByJobId(jobId);
        if (jobPosting == null) {
            return false;
        }

        // 只有发布者可以删除
        if (!jobPosting.getPublisherUserId().equals(userId)) {
            return false;
        }

        // 已有申请的招聘信息不能删除
        if (jobPosting.getApplicationCount() != null && jobPosting.getApplicationCount() > 0) {
            return false;
        }

        return true;
    }
}
