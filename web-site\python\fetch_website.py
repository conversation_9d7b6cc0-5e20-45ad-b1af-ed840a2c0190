import urllib.request
import urllib.parse
import os
import re
from pathlib import Path
import mimetypes

def download_resource(url, output_path, base_url):
    """下载单个资源文件"""
    try:
        # 处理相对路径
        if url.startswith('//'):
            full_url = 'https:' + url
        elif url.startswith('/'):
            base_domain = urllib.parse.urljoin(base_url, '/')
            full_url = urllib.parse.urljoin(base_domain, url)
        elif not url.startswith('http'):
            full_url = urllib.parse.urljoin(base_url, url)
        else:
            full_url = url

        print(f"正在下载: {full_url}")

        # 创建目录
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # 下载文件
        with urllib.request.urlopen(full_url) as response:
            content = response.read()

        # 保存文件
        with open(output_path, 'wb') as f:
            f.write(content)

        print(f"已保存到: {output_path}")
        return True

    except Exception as e:
        print(f"下载失败 {url}: {e}")
        return False

def fetch_website(url, output_dir="static_files"):
    """获取网页所有静态资源并保存到本地"""

    # 创建输出目录
    Path(output_dir).mkdir(exist_ok=True)

    # 获取HTML内容
    print(f"正在获取HTML: {url}")
    with urllib.request.urlopen(url) as response:
        html_content = response.read().decode('utf-8')

    # 保存原始HTML文件
    html_file = os.path.join(output_dir, "index.html")
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    print(f"HTML已保存到: {html_file}")

    # 创建各种资源目录
    css_dir = os.path.join(output_dir, "css")
    js_dir = os.path.join(output_dir, "js")
    img_dir = os.path.join(output_dir, "images")
    fonts_dir = os.path.join(output_dir, "fonts")

    Path(css_dir).mkdir(exist_ok=True)
    Path(js_dir).mkdir(exist_ok=True)
    Path(img_dir).mkdir(exist_ok=True)
    Path(fonts_dir).mkdir(exist_ok=True)

    # 提取CSS链接
    css_links = re.findall(r'<link[^>]*href=["\']([^"\']*\.css[^"\']*)["\'][^>]*>', html_content)
    print(f"找到 {len(css_links)} 个CSS文件")

    # 下载CSS文件
    for css_link in css_links:
        css_filename = os.path.basename(css_link.split('?')[0])
        if not css_filename.endswith('.css'):
            css_filename += '.css'
        css_path = os.path.join(css_dir, css_filename)
        download_resource(css_link, css_path, url)

    # 提取JavaScript链接
    js_links = re.findall(r'<script[^>]*src=["\']([^"\']*\.js[^"\']*)["\'][^>]*>', html_content)
    print(f"找到 {len(js_links)} 个JavaScript文件")

    # 下载JavaScript文件
    for js_link in js_links:
        js_filename = os.path.basename(js_link.split('?')[0])
        if not js_filename.endswith('.js'):
            js_filename += '.js'
        js_path = os.path.join(js_dir, js_filename)
        download_resource(js_link, js_path, url)

    # 提取图片链接
    img_patterns = [
        r'<img[^>]*src=["\']([^"\']*)["\'][^>]*>',
        r'background-image:\s*url\(["\']?([^"\')\s]+)["\']?\)',
        r'background:\s*url\(["\']?([^"\')\s]+)["\']?\)',
    ]

    img_links = []
    for pattern in img_patterns:
        img_links.extend(re.findall(pattern, html_content))

    # 去重
    img_links = list(set(img_links))
    print(f"找到 {len(img_links)} 个图片文件")

    # 下载图片文件
    for img_link in img_links:
        if img_link.startswith('data:'):  # 跳过base64图片
            continue

        img_filename = os.path.basename(img_link.split('?')[0])
        if not img_filename:
            continue

        # 如果没有扩展名，尝试从URL推断
        if '.' not in img_filename:
            img_filename += '.jpg'  # 默认扩展名

        img_path = os.path.join(img_dir, img_filename)
        download_resource(img_link, img_path, url)

    # 下载CSS文件中引用的资源
    print("正在检查CSS文件中的资源...")
    for css_file in os.listdir(css_dir):
        if css_file.endswith('.css'):
            css_file_path = os.path.join(css_dir, css_file)
            try:
                with open(css_file_path, 'r', encoding='utf-8') as f:
                    css_content = f.read()

                # 提取CSS中的图片和字体
                css_resources = re.findall(r'url\(["\']?([^"\')\s]+)["\']?\)', css_content)

                for resource_url in css_resources:
                    if resource_url.startswith('data:'):  # 跳过base64
                        continue

                    resource_filename = os.path.basename(resource_url.split('?')[0])
                    if not resource_filename:
                        continue

                    # 根据文件扩展名决定保存目录
                    ext = os.path.splitext(resource_filename)[1].lower()
                    if ext in ['.woff', '.woff2', '.ttf', '.eot', '.otf']:
                        resource_path = os.path.join(fonts_dir, resource_filename)
                    elif ext in ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp', '.ico']:
                        resource_path = os.path.join(img_dir, resource_filename)
                    else:
                        resource_path = os.path.join(output_dir, 'other', resource_filename)
                        os.makedirs(os.path.dirname(resource_path), exist_ok=True)

                    download_resource(resource_url, resource_path, url)

            except Exception as e:
                print(f"处理CSS文件 {css_file} 时出错: {e}")

    # 更新HTML文件中的路径
    print("正在更新HTML文件中的资源路径...")
    updated_html = html_content

    # 更新CSS路径
    for css_link in css_links:
        css_filename = os.path.basename(css_link.split('?')[0])
        if not css_filename.endswith('.css'):
            css_filename += '.css'
        local_css_path = f"css/{css_filename}"
        updated_html = updated_html.replace(css_link, local_css_path)

    # 更新JS路径
    for js_link in js_links:
        js_filename = os.path.basename(js_link.split('?')[0])
        if not js_filename.endswith('.js'):
            js_filename += '.js'
        local_js_path = f"js/{js_filename}"
        updated_html = updated_html.replace(js_link, local_js_path)

    # 更新图片路径
    for img_link in img_links:
        if img_link.startswith('data:'):
            continue
        img_filename = os.path.basename(img_link.split('?')[0])
        if img_filename:
            local_img_path = f"images/{img_filename}"
            updated_html = updated_html.replace(img_link, local_img_path)

    # 保存更新后的HTML文件
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(updated_html)

    print(f"所有静态资源已保存到目录: {output_dir}")
    print("HTML文件中的资源路径已更新为本地路径")

if __name__ == "__main__":
    url = "https://hrss.qingdao.gov.cn/cy/policy/policySpecial.html"
    fetch_website(url)