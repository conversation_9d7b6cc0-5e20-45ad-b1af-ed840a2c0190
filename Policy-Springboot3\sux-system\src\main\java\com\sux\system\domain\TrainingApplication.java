package com.sux.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sux.common.annotation.Excel;
import com.sux.common.core.domain.BaseEntity;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.Date;

/**
 * 培训报名对象 training_application
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@TableName("training_application")
public class TrainingApplication extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 报名ID */
    @TableId(type = IdType.AUTO)
    private Long applicationId;

    /** 培训订单ID */
    @Excel(name = "培训订单ID")
    @NotNull(message = "培训订单ID不能为空")
    private Long orderId;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 报名人姓名 */
    @Excel(name = "报名人姓名")
    @NotBlank(message = "报名人姓名不能为空")
    @Size(min = 0, max = 50, message = "报名人姓名不能超过50个字符")
    private String applicantName;

    /** 报名人手机号 */
    @Excel(name = "报名人手机号")
    @NotBlank(message = "报名人手机号不能为空")
    @Size(min = 0, max = 20, message = "报名人手机号不能超过20个字符")
    private String applicantPhone;

    /** 报名人邮箱 */
    @Excel(name = "报名人邮箱")
    @Size(min = 0, max = 100, message = "报名人邮箱不能超过100个字符")
    private String applicantEmail;

    /** 报名人身份证号 */
    @Excel(name = "报名人身份证号")
    @Size(min = 0, max = 18, message = "报名人身份证号不能超过18个字符")
    private String applicantIdCard;

    /** 报名人性别 */
    @Excel(name = "报名人性别")
    @Size(min = 0, max = 10, message = "报名人性别不能超过10个字符")
    private String applicantGender;

    /** 报名人年龄 */
    @Excel(name = "报名人年龄")
    private Integer applicantAge;

    /** 报名人学历 */
    @Excel(name = "报名人学历")
    @Size(min = 0, max = 20, message = "报名人学历不能超过20个字符")
    private String applicantEducation;

    /** 报名人工作经验 */
    @Excel(name = "报名人工作经验")
    @Size(min = 0, max = 500, message = "报名人工作经验不能超过500个字符")
    private String applicantExperience;

    /** 报名人地址 */
    @Excel(name = "报名人地址")
    @Size(min = 0, max = 200, message = "报名人地址不能超过200个字符")
    private String applicantAddress;

    /** 报名状态（0待审核 1已通过 2已拒绝 3已取消） */
    @Excel(name = "报名状态", readConverterExp = "0=待审核,1=已通过,2=已拒绝,3=已取消")
    private String applicationStatus;

    /** 报名时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date applicationTime;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reviewTime;

    /** 审核人 */
    @Excel(name = "审核人")
    @Size(min = 0, max = 50, message = "审核人不能超过50个字符")
    private String reviewer;

    /** 审核意见 */
    @Excel(name = "审核意见")
    @Size(min = 0, max = 500, message = "审核意见不能超过500个字符")
    private String reviewComment;

    /** 报名备注 */
    @Excel(name = "报名备注")
    @Size(min = 0, max = 500, message = "报名备注不能超过500个字符")
    private String applicationNote;

    /** 删除标志（0代表存在 2代表删除） */
    @TableField("del_flag")
    private String delFlag;

    // 关联字段 - 培训订单信息
    @TableField(exist = false)
    private String orderTitle;

    @TableField(exist = false)
    private String trainingType;

    @TableField(exist = false)
    private Date startDate;

    @TableField(exist = false)
    private Date endDate;

    public Long getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public Long getOrderId() {
        return orderId;
    }

    public void setOrderId(Long orderId) {
        this.orderId = orderId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getApplicantName() {
        return applicantName;
    }

    public void setApplicantName(String applicantName) {
        this.applicantName = applicantName;
    }

    public String getApplicantPhone() {
        return applicantPhone;
    }

    public void setApplicantPhone(String applicantPhone) {
        this.applicantPhone = applicantPhone;
    }

    public String getApplicantEmail() {
        return applicantEmail;
    }

    public void setApplicantEmail(String applicantEmail) {
        this.applicantEmail = applicantEmail;
    }

    public String getApplicantIdCard() {
        return applicantIdCard;
    }

    public void setApplicantIdCard(String applicantIdCard) {
        this.applicantIdCard = applicantIdCard;
    }

    public String getApplicantGender() {
        return applicantGender;
    }

    public void setApplicantGender(String applicantGender) {
        this.applicantGender = applicantGender;
    }

    public Integer getApplicantAge() {
        return applicantAge;
    }

    public void setApplicantAge(Integer applicantAge) {
        this.applicantAge = applicantAge;
    }

    public String getApplicantEducation() {
        return applicantEducation;
    }

    public void setApplicantEducation(String applicantEducation) {
        this.applicantEducation = applicantEducation;
    }

    public String getApplicantExperience() {
        return applicantExperience;
    }

    public void setApplicantExperience(String applicantExperience) {
        this.applicantExperience = applicantExperience;
    }

    public String getApplicantAddress() {
        return applicantAddress;
    }

    public void setApplicantAddress(String applicantAddress) {
        this.applicantAddress = applicantAddress;
    }

    public String getApplicationStatus() {
        return applicationStatus;
    }

    public void setApplicationStatus(String applicationStatus) {
        this.applicationStatus = applicationStatus;
    }

    public Date getApplicationTime() {
        return applicationTime;
    }

    public void setApplicationTime(Date applicationTime) {
        this.applicationTime = applicationTime;
    }

    public Date getReviewTime() {
        return reviewTime;
    }

    public void setReviewTime(Date reviewTime) {
        this.reviewTime = reviewTime;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    public String getReviewComment() {
        return reviewComment;
    }

    public void setReviewComment(String reviewComment) {
        this.reviewComment = reviewComment;
    }

    public String getApplicationNote() {
        return applicationNote;
    }

    public void setApplicationNote(String applicationNote) {
        this.applicationNote = applicationNote;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getOrderTitle() {
        return orderTitle;
    }

    public void setOrderTitle(String orderTitle) {
        this.orderTitle = orderTitle;
    }

    public String getTrainingType() {
        return trainingType;
    }

    public void setTrainingType(String trainingType) {
        this.trainingType = trainingType;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    @Override
    public String toString() {
        return "TrainingApplication{" +
                "applicationId=" + applicationId +
                ", orderId=" + orderId +
                ", userId=" + userId +
                ", applicantName='" + applicantName + '\'' +
                ", applicantPhone='" + applicantPhone + '\'' +
                ", applicantEmail='" + applicantEmail + '\'' +
                ", applicantIdCard='" + applicantIdCard + '\'' +
                ", applicantGender='" + applicantGender + '\'' +
                ", applicantAge=" + applicantAge +
                ", applicantEducation='" + applicantEducation + '\'' +
                ", applicantExperience='" + applicantExperience + '\'' +
                ", applicantAddress='" + applicantAddress + '\'' +
                ", applicationStatus='" + applicationStatus + '\'' +
                ", applicationTime=" + applicationTime +
                ", reviewTime=" + reviewTime +
                ", reviewer='" + reviewer + '\'' +
                ", reviewComment='" + reviewComment + '\'' +
                ", applicationNote='" + applicationNote + '\'' +
                ", delFlag='" + delFlag + '\'' +
                '}';
    }
}
