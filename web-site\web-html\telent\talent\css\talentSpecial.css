.text-ff6000 {
    color: #ff6000;
}
.text-0052d9 {
    color: #0052d9;
}
/* banner start */
.bannerBox {
    height: 260px;
}
.bannerBox .hd{
    position: absolute;
    bottom: 0;
    margin-left: 50%;
}
.bannerBox .hd ul{
    text-align: center;
    display: block;
    margin: 0 auto;
}
.bannerBox .hd li{
    display: inline-block;
    width: 9px;
    height: 9px;
    overflow: hidden;
    margin-right: 5px;
    text-indent: -999px;
    cursor: pointer;
    background:#e2e2e2;
    border-radius: 50px;
}
.bannerBox .hd li.on{
    background: #0052d9;
}
/* banner 轮播 */
.bannerSlide{
    width: 100% !important;
    height: 260px;
    top: 0;
    left: 0;
    min-width: 1400px;
    position: absolute !important;
}
.bannerSlide img{
    display: block;
    width: 100% !important;
    height: 100%;
    min-width: 1400px;
}
.bannerBox .leftBox {
    padding-top: 95px;
}

.bannerBox .leftBox .textP {
    line-height: 40px;
    font-size: 24px;
}

.bannerBox .rightBox {
    width: 642px;
    height: 449px;
    margin-right: 20px;
    margin-top: -50px;
    overflow: hidden;
    visibility: visible;
    background: url(../images/ts_bannerPic1.png) no-repeat center;
}
.bannerBox .rightBox .tagcloud{
    width: 642px;
    height: 400px;
    top: 50px;
    left: 0;
}
.bannerBox .rightBox a{
    position: absolute;
    top: 0;
    left: 0;
    padding-bottom: 19px;
    background: url(../images/ts_bannerIcon1.png) no-repeat center bottom;
}


.titleP {
    font-size: 30px;
    padding-bottom: 18px;
    background: url(../images/ps_boxTitleBg.png) repeat-x center bottom;
}
.box5 .titleP {
    font-size: 30px;
    padding-bottom: 18px;
    background: url(../images/ts_title5.png) repeat-x center bottom;
}

.titleP em {
    font-weight: bold;
    color: #0052d9;
}

.moreBtn {
    height: 30px;
    padding-right: 12px;
    border-radius: 30px;
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
}

.moreBtn span {
    line-height: 30px;
    padding-right: 20px;
    padding-left: 11px;
    background: url(../images/ps_boxMore.png) no-repeat right center;
}

.moreBtn:hover {
    animation: moreBtn 0.5s 1 forwards;
    -webkit-animation: moreBtn 0.5s 1 forwards;
    box-shadow: 0px 0px 10px 3px rgba(0, 77, 198, 0.35);
}

@keyframes moreBtn {
    from {
        background-position: 0;
    }

    to {
        background-position: 71px;
    }
}

.moreBtn.moreFour:hover {
    animation: moreFour 0.5s 1 forwards;
    -webkit-animation: moreFour 0.5s 1 forwards;
    box-shadow: 0px 0px 10px 3px rgba(0, 77, 198, 0.35);
}

@keyframes moreFour {
    from {
        background-position: 0;
    }

    to {
        background-position: 100px;
    }
}

/* box1 start */
.box1 {
    width: 100%;
    min-width: 1400px;
    box-sizing: border-box;
    padding-bottom: 40px;
    background: url(../images/ts_box2Bg.jpg) no-repeat center;
}

.box1 a {
    width: 328px;
    height: 120px;
    box-sizing: border-box;
    border-radius: 6px;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -ms-border-radius: 6px;
    -o-border-radius: 6px;
    box-shadow: 0px 0px 10px 3px #eff4fd;
    margin-top: 40px;
    margin-right: 28px;
}
.box1 .numBox1{
    background: url(../images/index_enter1.png) no-repeat;
}
.box1 .numBox2{
    background: url(../images/index_enter2.png) no-repeat;
}
.box1 .numBox3{
    background: url(../images/index_enter3.png) no-repeat;
}
.box1 .numBox4{
    background: url(../images/index_enter4.png) no-repeat;
}
.box1 .numBox:hover{
    box-shadow: 0px 0px 15px 2px #d9e3f4;

}
.box1 .numBox:last-child {
    margin-right: 0;
}

.box1 .numBox img {
    transform: scale(1.2);
    animation: imgBs 2s infinite;
    -webkit-animation: imgBs 2s infinite;
}

@keyframes imgBs {

    0%,
    100% {
        transform: scale(1);
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -ms-transform: scale(1);
        -o-transform: scale(1);
    }

    50% {
        transform: scale(0.9);
        -webkit-transform: scale(0.9);
        -moz-transform: scale(0.9);
        -ms-transform: scale(0.9);
        -o-transform: scale(0.9);
    }
}

.box1 .numBox .numRight {
    padding-right: 38px;
    height: 105px;
    box-sizing: border-box;
    padding-top: 23px;
}
.box1 .numBox .numRight img{
    display: block;
    width: 31px;
    height: 8px;
    margin-top: 25px;
}

/* box1 end */
/* box3 start */
.box3 {
    min-width: 1400px;
    box-sizing: border-box;
    padding-top: 30px;
    padding-bottom: 10px;
}
/* 人才超市 start */
.rccsList{
    width: 1435px;
    margin-top: 20px;
}
.rccsList li{
    width: 407px;
    height: 60px;
    background: url(../images/zrc_libg.png) no-repeat;
    margin: 0 30px 30px 0;
    box-shadow: 0px 0px 10px 0px rgba(0, 82, 217, 0.14);
    padding: 20px;
    border-radius: 8px;
    cursor: pointer;
}
.rccsList li:hover{
    background: url(../images/zrc_libgOn.png) no-repeat;
}
.rccsList li .name{
    font-size: 20px;
    font-weight: bold;
    color: #0052d9;
}
.rccsList li .infop{
    color: #666;
    margin-top: 14px;
}
/* 人才超市 end */

/* box3 end */



/* box5 start */
.box5 {
    width: 100%;
    min-width: 1400px;
    height: 431px;
    box-sizing: border-box;
    padding-top: 35px;
    padding-bottom: 40px;
    background: url(../images/ps_box5Bg.jpg) no-repeat center;
    margin-top: 40px;
}

.box5 .sxUl li {
    cursor: pointer;
    padding: 0 17px;
    height: 34px;
    line-height: 34px;
    border-radius: 30px;
    border: 1px solid #dddddd;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -ms-border-radius: 30px;
    -o-border-radius: 30px;
    margin-top: 4px;
    background-color: #f0f1f4;
}

.box5 .sxUl li.on {
    color: #0052d9;
    background-color: #dce6f7;
    border: 1px solid #0052d9;
}
.zphList{
    width: 1435px;
    margin-top: 20px;
}
.zphList li{
    width: 447px;
    height: 124px;
    margin: 0 30px 30px 0;
}
.zphList .li{
    width: 447px;
    height: 109px;
    background: url(../images/zrc_libg2.png) no-repeat;
    box-shadow: 0px 0px 10px 0px rgba(0, 82, 217, 0.14);
    bottom: 0;
    left: 0;
    padding: 15px 0 0;
    border-radius: 8px;
    cursor: pointer;
}
.zphList .li:hover{
    height: 149px;
    background: url(../images/zrc_libg2On.png) no-repeat;
    overflow: hidden;
}
.zphList .jobfairName{
    font-size: 18px;
    padding: 0 20px;
}
.zphList .time{
    background: url(../images/zpzc_listTimeIcon.png) no-repeat 20px center;
    color: #999;
    padding-left: 43px;
    padding-right: 20px;
    margin-top: 16px;
}
.zphList .add{
    background: url(../images/zpzc_listAddIcon.png) no-repeat  20px center;
    color: #999;
    padding-left: 43px;
    margin-top: 10px;
    padding-right: 20px;
}
.zphList .li .btns{
    display: none;
    margin-top: 21px;
}
.zphList .li:hover .btns{
    display: block;
    height: 40px;
    line-height: 40px;
    padding: 0 0 0 183px;
    border-radius: 0 0 8px 8px;
    color: #fff;
    background: url(../images/zrc_libg2btn.png) no-repeat;
}
/* box5 end */
/* box6 start */
.box6{
    padding-top: 26px;
}
/* box6 end */
.enterOut .enter1{
    width: 690px;
    height: 200px;
    background: url(../images/enternew1.jpg) no-repeat;
}
.enterOut .enter2{
    width: 690px;
    height: 200px;
    background: url(../images/enter2.jpg) no-repeat;
}
.enterOut a{
    color: #fff;
    display: block;
    width: 134px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    margin: 126px 0 0 30px;
    font-size: 16px;
    text-align: center;
}
/* 岗位超市 */
.zpgwList{
    width: 1432px;
    margin-top: 20px;
}
.zpgwList li{
    float: left;
  width: 445px;
  height: 180px;
  padding: 0 1px;
    border-radius: 6px;
    margin-right: 30px;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -ms-border-radius: 6px;
    -o-border-radius: 6px;
    margin-bottom: 30px;
    box-shadow: 0px 0px 10px 0px rgba(0, 82, 217, 0.14);
    background: url(../images/ts_box3LiBg.png) no-repeat center;
    background-size: 100% 100%;
}
.zpgwList li:hover{
    background: url(../images/detaile_zpgwLiBg.png) no-repeat center;
    background-size: 100% 100%;
    box-shadow: 0px 0px 15px 8px rgba(0, 82, 217, 0.15);
    transform: translateY(-10px);
    -webkit-transform: translateY(-10px);
    -moz-transform: translateY(-10px);
    -ms-transform: translateY(-10px);
    -o-transform: translateY(-10px);
}
.zpgwList li:hover .linkA{
    color: #0052d9;
}
.zpgwList li .intDetBox{
    display: block;
}
.zpgwList li .topBox {
    box-sizing: border-box;
    padding: 17px 17px 0 17px;
}

.zpgwList li .topBox .iconP1 {
    height: 33px;
    line-height: 33px;
    padding-left: 24px;
    background: url(../images/ts_box3Icon1.png) no-repeat left center;
    width: 45%;
}

.zpgwList li .topBox .moneyP {
    height: 33px;
    line-height: 33px;
    margin-bottom: 29px;
    width: 45%;
    text-align: right;
}

.zpgwList li .bottomBox {
    padding: 0 15px;
    line-height: 48px;
    box-sizing: border-box;
    background-color: #f1f5fd;
    border-radius: 0 0 6px 6px;
    -webkit-border-radius: 0 0 6px 6px;
    -moz-border-radius: 0 0 6px 6px;
    -ms-border-radius: 0 0 6px 6px;
    -o-border-radius: 0 0 6px 6px;
    margin-top: 11px;
}

.zpgwList li .bottomBox .timeP {
    padding-left: 22px;
    background: url(../images/ts_box3Icon2.png) no-repeat left center;
}
.f30 {
    font-size: 30px;
}
.text-ff6000 {
    color: #ff6000;
}
.text-0052d9 {
    color: #0052d9;
}
