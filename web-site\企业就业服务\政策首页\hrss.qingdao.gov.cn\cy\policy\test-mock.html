<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Mock Data Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .data-item { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .title { font-weight: bold; color: #333; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <h1>Mock Data Test Page</h1>
    
    <div class="section">
        <h2>Mock Data Loading Test</h2>
        <div id="loadTest"></div>
    </div>
    
    <div class="section">
        <h2>Policy Files Data</h2>
        <div id="fileData"></div>
    </div>
    
    <div class="section">
        <h2>Calendar Data</h2>
        <div id="calendarData"></div>
    </div>
    
    <div class="section">
        <h2>Read List Data</h2>
        <div id="readData"></div>
    </div>
    
    <div class="section">
        <h2>Video List Data</h2>
        <div id="videoData"></div>
    </div>

    <script src="../public/js/jquery-3.5.0.min.js"></script>
    <script src="../public/js/knockout.js"></script>
    <script src="js/mockData.js"></script>
    
    <script>
        $(document).ready(function() {
            // 测试mock数据加载
            $('#loadTest').html('<div class="success">Mock Data loaded successfully!</div>');
            
            // 测试政策文件数据
            var fileData = generateFileList();
            if (fileData && fileData.obj && fileData.obj.content) {
                var html = '<div class="success">Found ' + fileData.obj.content.length + ' policy files:</div>';
                fileData.obj.content.forEach(function(item, index) {
                    if (index < 3) { // 只显示前3个
                        html += '<div class="data-item">';
                        html += '<div class="title">' + item.title + '</div>';
                        html += '<div>Type: ' + item.policyTypeName + '</div>';
                        html += '<div>Support: ' + item.supportmodeName + '</div>';
                        html += '<div>Date: ' + item.publishTime + '</div>';
                        html += '</div>';
                    }
                });
                $('#fileData').html(html);
            } else {
                $('#fileData').html('<div class="error">Failed to load file data</div>');
            }
            
            // 测试政策申报数据
            if (mockData && mockData.calendarList && mockData.calendarList.obj) {
                var calData = mockData.calendarList.obj.content;
                var html = '<div class="success">Found ' + calData.length + ' calendar items:</div>';
                calData.slice(0, 3).forEach(function(item) {
                    html += '<div class="data-item">';
                    html += '<div class="title">' + item.sname + '</div>';
                    html += '<div>Department: ' + item.competentDepartment + '</div>';
                    html += '<div>Time: ' + item.applytime + '</div>';
                    html += '<div>Status: ' + item.detailStatusName + '</div>';
                    html += '</div>';
                });
                $('#calendarData').html(html);
            } else {
                $('#calendarData').html('<div class="error">Failed to load calendar data</div>');
            }
            
            // 测试政策解读数据
            var readData = generateReadList();
            if (readData && readData.obj && readData.obj.content) {
                var html = '<div class="success">Found ' + readData.obj.content.length + ' read items:</div>';
                readData.obj.content.slice(0, 3).forEach(function(item) {
                    html += '<div class="data-item">';
                    html += '<div class="title">' + item.title + '</div>';
                    html += '<div>Source: ' + (item.policy ? item.policy.source : 'N/A') + '</div>';
                    html += '<div>Date: ' + item.publishTime + '</div>';
                    html += '</div>';
                });
                $('#readData').html(html);
            } else {
                $('#readData').html('<div class="error">Failed to load read data</div>');
            }
            
            // 测试短视频数据
            var videoData = generateVideoList();
            if (videoData && videoData.obj && videoData.obj.content) {
                var html = '<div class="success">Found ' + videoData.obj.content.length + ' video items:</div>';
                videoData.obj.content.slice(0, 3).forEach(function(item) {
                    html += '<div class="data-item">';
                    html += '<div class="title">' + item.videoName + '</div>';
                    html += '<div>Has Video: ' + (item.sysAttachmentVo ? 'Yes' : 'No') + '</div>';
                    html += '<div>Has Cover: ' + (item.coverAttachmentVo ? 'Yes' : 'No') + '</div>';
                    html += '</div>';
                });
                $('#videoData').html(html);
            } else {
                $('#videoData').html('<div class="error">Failed to load video data</div>');
            }
        });
    </script>
</body>
</html>
