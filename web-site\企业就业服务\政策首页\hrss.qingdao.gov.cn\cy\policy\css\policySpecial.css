.conAuto1400 {
    width: 1400px;
    min-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

/* å¼¹çª— */

.alertBg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 13;
}

.alertCont {
    width: 818px;
    background-color: #ffffff;
    margin: 5% auto 0;
    border-radius: 5px;
    /* overflow: hidden; */
}

.alertTitle {
    line-height: 67px;
    color: #ffffff;
    font-size: 20px;
    font-weight: bold;
    background: url(../../place/image/new_cycdPop1.png) no-repeat;
    text-align: center;
    position: relative;
}

.alertClose {
    width: 15px;
    height: 15px;
    position: absolute;
    right: 20px;
    top: 26px;
}

.alertClose:hover {
    cursor: pointer;
}

.alertClose img {
    display: block;
    width: 100%;
}

.alertUl2 {
    max-height: 560px;
    padding: 30px;
    background: url(../../place/image/new_cycdPop2.png) no-repeat bottom center;
}

/* å¼¹çª— start */
.cxPopBox {
    position: fixed;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 11;
}

.cxPopBox .popBox {
    width: 860px;
    top: 5%;
    left: 50%;
    margin-left: -430px;
    background: #fff url(../images/ps_PopBg2.jpg) no-repeat center bottom;
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
}

.cxPopBox .popBox .titleBox {
    height: 80px;
    box-sizing: border-box;
    padding-top: 10px;
    background: url(../images/ps_popBg2.png) no-repeat center;
}

.cxPopBox .popBox .closeBtn {
    width: 16px;
    height: 15px;
    right: 20px;
    top: 28px;
}

.cxPopBox .popBox .closeBtn:hover {
    transform: rotate(90deg);
}

.cxPopBox .popBox .titleBox .bTitle {
    font-size: 24px;
    color: #fff;
}

.cxPopBox .popBox .sxBox {
    width: 820px;
    max-height: 420px;
    margin-top: 30px;
    overflow-x: hidden;
    overflow-y: scroll;
    box-sizing: border-box;
    padding-bottom: 10px;
}

.cxPopBox .popBox .sxBox::-webkit-scrollbar {
    width: 5px;
    height: 4px;
}

.cxPopBox .popBox .sxBox::-webkit-scrollbar-thumb {
    border-radius: 5px;
    -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background-color: #dddddd;
}

.cxPopBox .popBox .sxBox::-webkit-scrollbar-track {
    border-radius: 0;
}

.cxPopBox .popBox .sxBox .sxDiv {
    padding-right: 20px;
    padding-bottom: 20px;
    box-sizing: border-box;
    box-shadow: 0px 0px 10px 5px #deecfe;
}

.cxPopBox .popBox .sxBox .sxDiv .sxTitle {
    padding-left: 37px;
    padding-right: 18px;
    border-radius: 6px 0 16px 0;
    height: 36px;
    line-height: 36px;
    background: linear-gradient(to bottom, #12abbf, #0155d8);
    -webkit-border-radius: 6px 0 16px 0;
    -moz-border-radius: 6px 0 16px 0;
    -ms-border-radius: 6px 0 16px 0;
    -o-border-radius: 6px 0 16px 0;
}

.cxPopBox .popBox .sxBox .sxDiv .sxTitle img {
    left: 14px;
    top: 10px;
}

.cxPopBox .popBox .sxBox .sxDiv .sxUl li {
    min-width: 100px;
    height: 30px;
    line-height: 30px;
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px;
    border: 1px solid #dddddd;
    padding: 0 10px;
    box-sizing: border-box;
    background-color: #fff;
    cursor: pointer;
}

.cxPopBox .popBox .sxBox .sxDiv .sxUl li.on {
    color: #0052d9;
    border: 1px solid #0052d9;
    background-color: #eef3fc;
}

.cxPopBox .popBox .ksA {
    width: 140px;
    height: 40px;
    line-height: 40px;
    border-radius: 30px;
    background: linear-gradient(to right, #12abbf, #0155d8);
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -ms-border-radius: 30px;
    -o-border-radius: 30px;
}

/* å¼¹çª— end */
/* banner start */
.bannerBg {
    width: 100%;
    height: 260px;
    min-width: 1400px;
    background: url(../images/policySBanner.jpg) no-repeat center;
}

/* box2 start */
.box2 {
    width: 100%;
    height: 358px;
    min-width: 1400px;
    box-sizing: border-box;
    padding-top: 40px;
    background: url(../images/policySBg2_1.jpg) no-repeat center;
}

.titleP {
    font-size: 30px;
    padding-bottom: 18px;
    background: url(../images/ps_boxTitleBg.png) repeat-x center bottom;
}

.titleP em {
    font-weight: bold;
    color: #0052d9;
}

.moreBtn {
    height: 30px;
    padding-right: 12px;
    border-radius: 30px;
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
}

.moreBtn span {
    line-height: 30px;
    padding-right: 20px;
    padding-left: 11px;
    background: url(../images/ps_boxMore.png) no-repeat right center;
}

.moreBtn:hover {
    animation: moreBtn 0.5s 1 forwards;
    -webkit-animation: moreBtn 0.5s 1 forwards;
    box-shadow: 0px 0px 10px 3px rgba(0, 77, 198, 0.35);
}

@keyframes moreBtn {
    from {
        background-position: 0;
    }

    to {
        background-position: 71px;
    }
}

.moreBtn.moreFour:hover {
    animation: moreFour 0.5s 1 forwards;
    -webkit-animation: moreFour 0.5s 1 forwards;
    box-shadow: 0px 0px 10px 3px rgba(0, 77, 198, 0.35);
}

@keyframes moreFour {
    from {
        background-position: 0;
    }

    to {
        background-position: 100px;
    }
}

.box2 .hd {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
}

.box2 .hd a {
    position: absolute;
    height: 36px;
    width: 36px;
    top: 102px;
}

.box2 .hd a.prev {
    left: 0;
    background: url(../images/policySIcon1.png) no-repeat center;
}

.box2 .hd a.prev:hover {
    background: url(../images/policySIcon1On.png) no-repeat center;
}

.box2 .hd a.next {
    right: 0;
    background: url(../images/policySIcon2.png) no-repeat center;
}

.box2 .hd a.next:hover {
    background: url(../images/policySIcon2On.png) no-repeat center;
}

.box2 .bd {
    box-sizing: border-box;
    padding-left: 48px;
    height: 243px;
    width: 1350px;
    overflow: hidden;
}

.box2 .sbUl li {
    width: 619px !important;
    box-shadow: 0px 0px 10px 3px #d9e4f4;
    border-radius: 9px;
    box-sizing: border-box;
    /* border: 2px solid #fff; */
    -webkit-border-radius: 9px;
    -moz-border-radius: 9px;
    -ms-border-radius: 9px;
    -o-border-radius: 9px;
    background: url(../images/policySLiBg1.png) no-repeat right top;
    background-size: cover;
    margin: 20px 17px ;
}

.box2 .sbUl li:hover {
    background: url(../images/policySLiBg1On.png) no-repeat right top;
    box-shadow: 0px 0px 5px 5px #d9e4f7;
}

.box2 .sbUl li a:hover {
    color: #0050ff;
}

.box2 .sbUl li .topBox {
    box-sizing: border-box;
    padding: 22px 17px 0 18px;
}

.box2 .sbUl li .topBox .sqSpan {
    width: 61px;
    height: 24px;
    line-height: 24px;
    padding-right: 7px;
    background: url(../images/ps_box2Icon1.png) no-repeat center;
}

.box2 .sbUl li .topBox .sqSpan.icon2 {
    background: url(../images/ps_box2Icon2.png) no-repeat center;
}

.box2 .sbUl li .topBox .sqSpan.icon3 {
    background: url(../images/ps_box2Icon3.png) no-repeat center;
}

.box2 .sbUl li .topBox .linkA {
    width: 486px;
    height: 24px;
    line-height: 24px;
}

.box2 .sbUl li .topBox .lBox {
    width: 267px;
}

.box2 .sbUl li .topBox .fwP,
.box2 .sbUl li .topBox .timeP {
    height: 16px;
    line-height: 16px;
    padding-left: 22px;
    margin-top: 12px;
    box-sizing: border-box;
    background: url(../images/ps_box2Icon5.png) no-repeat left center;
}

.box2 .sbUl li .topBox .timeP {
    background: url(../images/ps_box2Icon4.png) no-repeat left center;
}

.box2 .sbUl li .topBox .spanBox {
    height: 30px;
}

.box2 .sbUl li .topBox .spanBox span {
    height: 30px;
    line-height: 30px;
    padding: 0 12px;
    color: #0050ff;
    background-color: #e4eaf9;
    margin-right: 9px;
    border-radius: 0 30px 30px 30px;
    -webkit-border-radius: 0 30px 30px 30px;
    -moz-border-radius: 0 30px 30px 30px;
    -ms-border-radius: 0 30px 30px 30px;
    -o-border-radius: 0 30px 30px 30px;
    max-width: 35%;
}

.box2 .sbUl li .topBox .spanBox span:last-child {
    margin-right: 0;
}

.box2 .sbUl li .topBox .numBox {
    width: 310px;
    height: 65px;
    margin-top: 14px;
}

.box2 .sbUl li .topBox .numBox.on {
    background: url(../images/ps_box2Icon6.png) no-repeat center;
}

.detailStatusName {
    padding-top: 25px;
    color: #999;
}

.detailStatusName i {
    color: #0052d9 !important;
    font-style: normal;
    font-size: 24px !important;
}

.f30 {
    font-size: 30px;
}

.f15 {
    font-size: 15px;
}

.text-ff8a00 {
    color: #ff6000;
}

.text-0050ff {
    color: #0050ff;
}

.text-grayB {
    color: #bbb;
}

.box2 .sbUl li .topBox .numBox>div {
    width: 49%;
    vertical-align: top;
}

.box2 .sbUl li .bottomBox {
    padding: 9px 0;
    background-color: #eef3fb;
    border-radius: 0 0 9px 9px;
    -webkit-border-radius: 0 0 9px 9px;
    -moz-border-radius: 0 0 9px 9px;
    -ms-border-radius: 0 0 9px 9px;
    -o-border-radius: 0 0 9px 9px;
    margin-top: 18px;
}

.box4 .calculateBtn {
    display: block;
    margin-top: 40px;
    width: 1402px;
    height: 122px;
    background: url(../images/policySImg1.png) no-repeat center;
}

/* box2 end */

/* box4 start */
.box4 {
    width: 100%;
    height: 752px;
    min-width: 1400px;
    box-sizing: border-box;
    background: url(../images/policySBg1_1.jpg) no-repeat center;
}

.box4 .leftBox {
    width: 685px;
}

.box4 .wjUl li {
    width: 685px;
    height: 99px;
    margin-top: 22px;
}

.box4 .wjUl .li {
    width: 685px;
    left: 0;
    bottom: 0;
    border-radius: 6px;
    box-sizing: border-box;
    padding: 20px 20px;
    /* border: 2px solid #fff; */
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -ms-border-radius: 6px;
    -o-border-radius: 6px;
    background: url(../images/policySLiBg2.png) no-repeat center;
    background-size: 100% 100%;
    box-shadow: 0px 0px 10px 3px #deecfe;
}

.box4 .wjUl .li:nth-child(2n) {
    margin-right: 0;
}

.box4 .wjUl .li .paImg {
    width: 60px;
    height: 60px;
    right: -2px;
    top: -2px;
}

.box4 .wjUl .li .titleA {
    width: 100%;
    height: 24px;
    line-height: 24px;
}

.box4 .wjUl .li .iconDiv p {
    height: 16px;
    line-height: 16px;
    padding-left: 20px;
    margin-right: 15px;
    margin-top: 15px;
}

.box4 .wjUl .li .iconDiv p.icon1 {
    background: url(../images/ps_box4LiIcon2.png) no-repeat left center;
}

.box4 .wjUl .li .iconDiv p.icon2 {
    max-width: 400px;
    background: url(../images/ps_box4LiIcon3.png) no-repeat left center;
}

.box4 .wjUl .li .iconDiv p.icon3 {
    margin-right: 0;
    background: url(../images/ps_box2Icon4.png) no-repeat left center;
}

.box4 .wjUl .li:hover {
    box-shadow: 0px 0px 15px 8px #e7effb;
    background: #fff url(../images/policySLiBg2On.png) no-repeat right bottom;
    background-size: 100% 100%;
    /* transform: translateY(-5px);
    -webkit-transform: translateY(-5px);
    -moz-transform: translateY(-5px);
    -ms-transform: translateY(-5px);
    -o-transform: translateY(-5px); */
}

.box4 .wjUl .li:hover .titleA {
    height: auto;
    white-space: normal;
    /* pre-wrap */
    text-overflow: auto;
    word-break: break-word;
}

.box4 .wjUl .li:hover .titleA {
    color: #0052d9;
}

.box4 .rightBox {
    width: 685px;
    height: 422px;
    background: url(../images/index_wspBg.png) no-repeat center;
}

.box4 .rightBox > .title {
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    color: #fff;
    text-align: center;
    font-weight: bold;
}

.box4 .rightBox .moreTxt {
    display: block;
    width: 50px;
    color: #0052d9;
    background: url(../images/policySIcon10.png) no-repeat right center;
    margin: 13px auto 0;
}
.box4 .hd {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
}

.box4 .hd a {
    position: absolute;
    height: 30px;
    width: 30px;
    top: 146px;
}

.box4 .hd a.prev {
    left: 20px;
    background: url(../images/index_dspArr1.png) no-repeat center;
    background-size: 100% 100%;
}

.box4 .hd a.prev:hover {
    background: url(../images/policySIcon1On.png) no-repeat center;
    background-size: 100% 100%;
}

.box4 .hd a.next {
    right: 20px;
    background: url(../images/index_dspArr2.png) no-repeat center;
    background-size: 100% 100%;
}

.box4 .hd a.next:hover {
    background: url(../images/policySIcon2On.png) no-repeat center;
    background-size: 100% 100%;
}
.box4 .rightBox .tempWrap{
    margin-left: 60px;
}
.box4 .rightBox .videoBox {
    display: flex;
    justify-content: center;
    align-items: center;
}
.box4 .rightBox .videoBox a {
    width: 565px;
    height: 318px;
    border-radius: 9px;
    overflow: hidden;
    position: relative;
}
.box4 .rightBox .videoBox .title{
    position: absolute;
    width: 480px;
    height: 50px;
    line-height: 50px;
    padding: 0 55px 0 10px;
    background: url(../images/index_dspTit.png) no-repeat;
    z-index: 10;
    bottom: 10px;
    left: 10px;
    color: #fff;
    font-size: 18px;
}
.box4 .rightBox .videoBox a video{
    width: 565px;
    height: 318px;
}
.box7 .rightBox{
    width: 685px;
}
.box7 .rightBox .videoBox a {
    width: 328px;
    height: 332px;
    padding: 20px 20px 0;
    box-sizing: border-box;
    border-radius: 9px;
    margin-right: 25px;
    background: url(../images/index_jdBg.png) no-repeat;
    box-shadow: 0px 0px 10px 3px #d9e4f7;
    margin-top: 20px;
}
.box7 .rightBox .videoBox a:nth-of-type(2){
    margin-right: 0px;
}

.box7 .rightBox .videoBox a:hover .liTitle {
    color: #0052d9;
}
.box7 .rightBox .videoBox a:hover{
    margin-top: 10px;
}
.box7 .rightBox .videoBox a .imgSize {
    width: 100%;
    height: 192px;
    overflow: hidden;
}

.box7 .rightBox .videoBox a .imgSize p {
    z-index: 2;
    color: #fff;
    font-weight: bold;
    margin: 50px 30px 0;
    font-size: 18px;
    line-height: 24px;
    height: 72px;
}

.box7 .rightBox .videoBox a:hover .imgSize img {
    transform: scale(1.2);
    -webkit-transform: scale(1.2);
    -moz-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -o-transform: scale(1.2);
    z-index: 1;
}

.box7 .rightBox .videoBox a .imgSize img {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    -webkit-border-radius: 8px;
    -moz-border-radius: 8px;
    -ms-border-radius: 8px;
    -o-border-radius: 8px;
}

.box7 .rightBox .videoBox a .liTitle {
    line-height: 30px;
    height: 60px;
    margin-top: 10px;
}

.box7 .rightBox .videoBox a .linkP {
    max-width: 150px;
    padding-left: 20px;
    height: 16px;
    line-height: 16px;
    background: url(../images/ps_box6IconLink.png) no-repeat left center;
}

.box7 .rightBox .videoBox a .timeP {
    padding-left: 20px;
    height: 16px;
    line-height: 16px;
    background: url(../images/ps_box2Icon4.png) no-repeat left center;
}

/* box4 end */

/* box7 start */
.box7 {
    width: 100%;
    min-width: 1400px;
    box-sizing: border-box;
    padding-top: 40px;
    padding-bottom: 30px;
    background: #fff url(../images/policySBg3_1.jpg) no-repeat center bottom;
}

.box7 .banner2 {
    box-shadow: 0px 0px 16px 0px rgba(0, 82, 217, 0.2);
}

.box7 .entranceImg {
    width: 200px;
    height: 280px;
    overflow: hidden;
    position: relative;
    margin-left: 30px;
    box-shadow: 0px 0px 16px 0px rgba(0, 82, 217, 0.2);
}

.box7 .entranceImg.img1 {
    background: url(../images/policySImg5.png) no-repeat center;
}

.box7 .entranceImg.img2 {
    background: url(../images/policySImg4.png) no-repeat center;
}

.box7 .entranceImg.img3 {
    background: url(../images/policySImg3.png) no-repeat center;
}

.box7 .entranceImg img {
    width: 200px;
    height: 40px;
    left: 0;
    bottom: -40px;
}

.box7 .entranceImg:hover img {
    left: 0;
    bottom: 0;
}

.box7 .sxUl li {
    cursor: pointer;
    padding: 0 17px;
    height: 34px;
    line-height: 34px;
    border-radius: 30px;
    border: 1px solid #dddddd;
    -webkit-border-radius: 30px;
    -moz-border-radius: 30px;
    -ms-border-radius: 30px;
    -o-border-radius: 30px;
    margin-top: 4px;
    background-color: #f0f1f4;
}

.box7 .sxUl li.on {
    color: #0052d9;
    background-color: #dce6f7;
    border: 1px solid #0052d9;
}

.box7 .dybox {
    padding-top: 33px;
    box-sizing: border-box;
    width: 1430px;
}

.box7 .dybox a {
    width: 447px;
    min-height: 80px;
    margin-top: 15px;
    margin-right: 30px;
}

.box7 .dybox a:nth-child(3n) {
    margin-right: 0;
}

.box7 .dybox a:hover {
    box-shadow: 0px 0px 15px 8px #e5ecf7;
}

.box7 .dybox a:hover .aBox {
    border: 2px solid #13b0be;
    background: #f6f9fd url(../images/policySIcon8.png) no-repeat right bottom;
}

.box7 .dybox a:hover .wtTitle {
    color: #0052d9;
    height: auto;
    display: block !important;
    min-height: 60px;
}

.box7 .dybox a .aBox {
    border-radius: 6px;
    border: 2px solid #fff;
    -webkit-border-radius: 6px;
    -moz-border-radius: 6px;
    -ms-border-radius: 6px;
    -o-border-radius: 6px;
    left: 0;
    bottom: 0;
    width: 447px;
    box-sizing: border-box;
    box-shadow: 0px 0px 10px 3px #e1e8f5;
    padding: 8px 25px 8px 0;
    background: #f6f9fd;
}

.box7 .dybox a .wtTitle {
    height: 60px;
    line-height: 30px;
    display: flex;
    align-items: center;
    white-space: normal;
    /* pre-wrap */
    word-break: break-word;
    padding-left: 89px;
    background: url(../images/policySIcon7.png) no-repeat 9px 0;
}

/* box7 end */
@media screen and (min-width: 1920px) {
    .bannerBg {
        background: url(../images/policySBanner.jpg) no-repeat center;
        background-size: 100% 100%;
    }

    .box2 {
        background: url(../images/policySBg2_1.jpg) no-repeat center;
        background-size: 100% 100%;
    }

    .box4 {
        background: url(../images/policySBg1_1.jpg) no-repeat center;
        background-size: 100% 100%;
    }

}
/* åˆ—è¡¨é¡¶éƒ¨ç­›é€‰ã€é¢åŒ…å±‘ */
.selectBox{
    padding-top: 30px;
}
.selectBox .selectModule{
    /* position: relative; */
    width: 447px;
    height: 60px;
    line-height: 60px;
    border-radius: 5px;
    overflow: hidden;
    background: #fff url(../../place/image/selectModule_arrowOff.png) no-repeat 414px;
    font-size: 16px;
    box-shadow: 0px 0px 10px 3px #deecfe;
}
.selectBox .selectModule .title{
    float: left;
    background-color: #f2f8fd;
    width: 120px;
    height: 60px;
    color: #333;
    font-style: italic;
    text-align: center;
}
.selectBox .selectModule.on .title{
    background:linear-gradient(to right, #1a65dd, #2081d4);
    color: #d9d9d9;
}
.selectBox .selectModule.on .name{
    background:url(../../place/image/selectModule_arrowOn.png) no-repeat 295px , linear-gradient(to right, #0052d9, #13b0be);
    color: #fff;
}
.selectBox .selectModule .name{
    float: left;
    width: 317px;
    color: #999;
    padding-left: 10px;
    cursor: pointer;
}
.selectBox .selectModule .name p{
    width: 80%;
}
.selectBox .selectList{
    top: 76px;
    z-index: 2;
}
.selectBox .selectList .arrowIcon{
    background: url(../../place/image/selectModule_ListIcon02.png) no-repeat center;
    width: 14px;
    height: 8px;
    position: absolute;
    top: -8px;
    left: 38px;
}
.selectBox .selectList ul{
    min-width: 426px;
    height: 250px;
    overflow-y: scroll;
    background-color: #fff;
    border-radius: 5px;
    /* border: 1px solid #ff6000; */
    padding: 10px;
    box-shadow: 0px 0px 5px 2px #f3f7fd;
}
.selectBox .selectList ul::-webkit-scrollbar {
	width: 8px;
}

.selectBox .selectList ul::-webkit-scrollbar-thumb {
	width: 8px;
	height: 50px;
	border-radius: 25px;
	background: #eaf2ff;
}
.selectBox .selectList ul li{
    height: 50px;
    line-height: 50px;
    padding-left: 119px;
    padding-right: 25px;
    border-radius: 5px;
    color: #333;
    font-size: 16px;
    white-space: nowrap;
    cursor: pointer;
}
.selectBox .selectList ul li:hover{
    background: url(../../place/image/selectModule_ListIcon.png) no-repeat 24px,linear-gradient(to right, #0052d9, #13b0be);
    color: #fff;
}
.selectBox .selectList ul li.on{
    background: url(../../place/image/selectModule_ListIcon.png) no-repeat 24px,linear-gradient(to right, #0052d9, #13b0be);
    color: #fff;
}
/* é¡¶éƒ¨å¯¼èˆªæ ã€é¢åŒ…å±‘ */
.qdzct{
    display: block;
    position: fixed;
    width: 160px;
    height: 120px;
    top: 45%;
    right: 10px;
    z-index: 999;
}
/* è”åŠž */
.lbOut{
    width: 100%;
    min-width: 1400px;
    height: 276px;
    background: url(../images/lb_bg.jpg) no-repeat top center;
    padding-top: 35px;
}
.lbOut ul{
    height: 212px;
    width: 1422px;
    position: absolute;
    bottom: 30px;
    left: 0;
}

.lbOut ul li{
    /* åˆå§‹å®½é«˜ */
    width: 264px;
    height: 170px;
    /* width: 19.2%;
    height: 86%; */
    background: url(../images/baseEnter.png) no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
    margin-top: 42px;
    box-shadow: 0px 0px 10px 0px rgba(0, 82, 217, 0.14);
    margin-right: 20px;
    position: relative;
}
.lbOut ul li .tips{
    position: absolute;
    width: 49px;
    height: 50px;
    display: block;
    top: 0;
    right: 0;
    background: url(../images/tips.png) no-repeat;
}
.lbOut ul li.on .tips{
    background: url(../images/tipson.png) no-repeat;
}
.lbOut ul li.on{
    /* åˆå§‹æ»‘ä¸Šå®½é«˜ */
    /* width: 217px;   */
    height: 212px;
    /* width: 19.2%;
    height: 110%; */
    background: url(../images/baseEnterOn.png) no-repeat;
    background-size: 100% 100%;
    margin-top: 0px;
}
.lbOut ul li p{
    font-size: 18px;
    text-align: center;
    line-height: 30px;
    height: 60px;
    color: #333;
}
.lbOut ul li.on p{
    color: #fff;
}
.lbOut ul li div a{
    display: none;
    width: 80px;
    height: 30px;
    line-height: 30px;
    background: #fff;
    text-align: center;
    color: #0052d9;
    border-radius: 50px;
    margin: 14px auto 0;
    text-decoration: none;
}
.lbOut ul li.on div a{
    display: block;
    width: 80px;
    height: 30px;
    line-height: 30px;
    background: #fff;
    text-align: center;
    color: #0052d9;
    border-radius: 50px;
    margin: 14px auto 0;
}
.lbOut ul li div a:hover{
    box-shadow: inset 0 0 10px #2478ff;
}
.lbOut ul li.on div a:hover{
    box-shadow: inset 0 0 10px #2478ff;
}
.lbOut ul li .icon1{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon1.png) no-repeat center 19px;
    background-size: 26% ;
}
.lbOut ul li.on .icon1{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon1on.png) no-repeat center 19px;
    background-size: 26% ;
}
.lbOut ul li .icon2{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon2.png) no-repeat center 19px;
    background-size: 26% ;
}
.lbOut ul li.on .icon2{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon2on.png) no-repeat center 19px;
    background-size: 26% ;
}
.lbOut ul li .icon3{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon3.png) no-repeat center 19px;
    background-size: 26% ;
}
.lbOut ul li.on .icon3{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon3on.png) no-repeat center 19px;
    background-size: 26% ;
}
.lbOut ul li .icon4{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon4.png) no-repeat center 19px;
    background-size: 26% ;
}
.lbOut ul li.on .icon4{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon4on.png) no-repeat center 19px;
    background-size: 26% ;
}
.lbOut ul li .icon5{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon5.png) no-repeat center 19px;
    background-size: 26% ;
}
.lbOut ul li.on .icon5{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon5on.png) no-repeat center 19px;
    background-size: 26% ;
}
.lbOut ul li .icon6{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon6.png) no-repeat center 19px;
    background-size: 26% ;
}
.lbOut ul li.on .icon6{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon6on.png) no-repeat center 19px;
    background-size: 26% ;
}
.lbOut ul li .icon7{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon7.png) no-repeat center 19px;
    background-size: 26% ;
}
.lbOut ul li.on .icon7{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon7on.png) no-repeat center 19px;
    background-size: 26% ;
}

/* 添加悬浮效果 */
.lbOut ul li:hover{
    height: 212px;
    background: url(../images/baseEnterOn.png) no-repeat;
    background-size: 100% 100%;
    margin-top: 0px;
}

.lbOut ul li:hover .tips{
    background: url(../images/tipson.png) no-repeat;
}

.lbOut ul li:hover p{
    color: #fff;
}

.lbOut ul li:hover .icon1{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon1on.png) no-repeat center 19px;
    background-size: 26% ;
}

.lbOut ul li:hover .icon2{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon2on.png) no-repeat center 19px;
    background-size: 26% ;
}

.lbOut ul li:hover .icon3{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon3on.png) no-repeat center 19px;
    background-size: 26% ;
}

.lbOut ul li:hover .icon4{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon4on.png) no-repeat center 19px;
    background-size: 26% ;
}

.lbOut ul li:hover .icon5{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon5on.png) no-repeat center 19px;
    background-size: 26% ;
}

.lbOut ul li:hover .icon6{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon6on.png) no-repeat center 19px;
    background-size: 26% ;
}

.lbOut ul li:hover .icon7{
    padding-top: 94px;
    background: url(../../index/indexImg/baseEnterIcon7on.png) no-repeat center 19px;
    background-size: 26% ;
}

.lbOut ul li:hover div a{
    display: block;
}

.lbOut ul li.on div a{
    display: block;
}
/* æ®‹ç–¾äºº start */
.alertBg1,.alertBg2,.alertBg3{
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: url(../../public/images/icons/popUp_bg.png);
    z-index: 20001;
}
.alertCont{
    width: 800px;
    /* height: 480px; */
    background-color: #ffffff;
    margin: 5% auto 0;
    border-radius: 5px;
    overflow: hidden;
}
.alertTitle{
    line-height: 67px;
    color: #ffffff;
    font-size: 20px;
    font-weight: bold;
    background: url(../../place/image/new_cycdPop1.png) no-repeat;
    text-align: center;
    position: relative;
}
.alertClose{
    width: 15px;
    height: 15px;
    position: absolute;
    right: 20px;
    top: 26px;
}
.alertClose:hover{
    cursor: pointer;
}
.alertClose img{
    display: block;
    width: 100%;
}
.alertUl {
    background: url(../../place/image/new_cycdPop2.png) no-repeat bottom center;
    padding-bottom: 30px;
    height: 600px;
}
.alertBtnCont{
    width: 343px;
    margin: 0 auto;
}
.alertBtn{
    width: 160px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    border-radius: 50px;
}
.alertCont .infos{
    padding: 22px 30px;
    line-height: 28px;
    font-size: 16px;
}
.btnCancel{
    border: 1px solid #8db6f2;
    color: #1569e6;
    background-color: #e7f1fc;
}
.btnSubmit{
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
    color: #ffffff;
}
.alertBg1 .zcyw{
    width: 740px;
    height: 150px;
    background: url(../../index/indexImg/popFileBg.png) no-repeat;
    margin: 0 auto 30px;
}
.alertBg1 .titleBox span{
    font-size: 18px;
    color: #fff;
    line-height: 40px;
    margin-top: 20px;
    margin-left: 38px;
}
.alertBg1 .titleBox a{
    line-height: 40px;
    margin-top: 20px;
    margin-left: 30px;
    font-size: 18px;
    font-weight: bold;
    text-decoration: underline;
    color: #0052d9;
}
.alertBg1 .phoneBoxs{
    padding-left: 119px;
    background: url(../../index/indexImg/popPhone.png) no-repeat 80px center;
    font-size: 16px;
    line-height: 24px;
    margin-top: 42px;
}
.alertBg1 .phoneBoxs span{
    font-size: 24px;
    font-weight: bold;
    color: #fe6a1c;
    font-style: italic;
}
/* æ®‹ç–¾äºº end */
/* é€€å½¹å†›äºº start */
.alertBg2 .titlesp{
    font-size: 16px;
    font-weight: bold;
    color: #0052d9;
}
.alertBg2 .zcyw{
    width: 740px;
    height: 280px;
    background: url(../../index/indexImg/popFileBg2.png) no-repeat;
    margin: 0 auto 30px;
}
.alertBg2 .titleBox span{
    font-size: 18px;
    color: #fff;
    line-height: 40px;
    margin-top: 20px;
    margin-left: 38px;
}
.alertBg2 .titleBox a{
    line-height: 40px;
    margin-top: 20px;
    margin-left: 30px;
    font-size: 18px;
    font-weight: bold;
    text-decoration: underline;
    color: #0052d9;
}
.alertBg2 .phoneBoxs{
    width: 300px;
    height: 40px;
    line-height: 40px;
    background:#fff url(../../index/indexImg/popPhone.png) no-repeat 13px center;
    padding-left: 54px;
    border-radius: 50px;
    margin: 30px auto 0;
}
.alertBg2 .phones li{
    width: 218px;
    margin-left: 28px;
    line-height: 30px;
}
.alertBg2 .phones li span{
    color: #fe6a1c;
    font-weight: bold;
}
/* é€€å½¹å†›äºº end */
/* åŠžäº‹æŒ‡å— start */
.alertBg3 .alertCont{
    width: 600px;
    height: 360px;
}
.alertBg3 .alertUl{
    height: 263px;
}
.alertBg3 ul{
    padding: 10px 30px 0;
}
.alertBg3 li{
    padding:20px 0 20px 120px;
    background: url(../../index/indexImg/linkIcon.png) no-repeat 20px center;
    height: 80px;
    border-bottom: 1px dashed #f5f9fd;
}
.alertBg3 li p{
    line-height: 30px;
    height: 30px;
    font-size: 18px;
    margin-bottom: 13px;
}
.alertBg3 li a{
    display: block;
    width: 121px;
    height: 29px;
    line-height: 29px;
    color: #0052d9;
    border: 1px solid #0052d9;
    border-radius: 50px;
    padding-left: 15px;
    background:#fff url(../../entrepreneurship/images/new_arr.png) no-repeat 108px center;
}
/* åŠžäº‹æŒ‡å— end */