{"code": 0, "msg": "操作成功", "obj": [{"baseId": "565de42fc8ee4ac9b68bd7f295ad81a7", "dictTypeNo": "QYZZ", "dictNo": "5567fff623e5415aacb1a66b1690fd4a", "dictParentNo": null, "baseParentId": "0", "baseName": "科技型中小企业", "baseNote": "科技型中小企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 1, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "beceadfa990a417fba30c6670009265b", "dictTypeNo": "QYZZ", "dictNo": "231e12bade264c938de36e5dc98875d1", "dictParentNo": null, "baseParentId": "0", "baseName": "高新技术企业", "baseNote": "高新技术企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 2, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "825c26966ffe4cc5a0876194eecf4c94", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "创新型中小企业", "baseNote": "创新型中小企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 3, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "0ec847a523c04461a0310d829bf34d1d", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "“专精特新”中小企业", "baseNote": "“专精特新”中小企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 4, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "19bbfcda34c440a8a53f8fe13a3cc2e2", "dictTypeNo": "QYZZ", "dictNo": "a50da8d4fffa438b9c7a3836bdb135-4", "dictParentNo": null, "baseParentId": "0", "baseName": "专精特新“小巨人”企业", "baseNote": "专精特新“小巨人”企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 5, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "ddef2730b342434089d85472c5737131", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "重点专精特新“小巨人”企业", "baseNote": "重点专精特新“小巨人”企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 6, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "8d728f694d364272a2fcd4f83c7ddecc", "dictTypeNo": "QYZZ", "dictNo": "a50da8d4fffa438b9c7a3836bdb135-3", "dictParentNo": null, "baseParentId": "0", "baseName": "制造业单项冠军", "baseNote": "制造业单项冠军", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 7, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "4a1a21b102874334921658484a9d1379", "dictTypeNo": "QYZZ", "dictNo": "96f4a57c740b42edad946d311cd207e5", "dictParentNo": null, "baseParentId": "0", "baseName": "瞪羚企业", "baseNote": "瞪羚企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 8, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "1399d3929b344cf0b40f75affc9885ef", "dictTypeNo": "QYZZ", "dictNo": "8f33b6724f76490dab7ea9f95a9f1511", "dictParentNo": null, "baseParentId": "0", "baseName": "“一企一技术”创新企业", "baseNote": "“一企一技术”创新企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 9, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "3fcf1d289c6e43d7bf996e53ad1349b8", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "双软认定企业", "baseNote": "双软认定企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 10, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "a727bd33cd7449d1991b881d300e5045", "dictTypeNo": "QYZZ", "dictNo": "a50da8d4fffa438b9c7a3836bdb135-1", "dictParentNo": null, "baseParentId": "0", "baseName": "知识产权优势企业", "baseNote": "知识产权优势企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 11, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "4bd05d3b0cd94615a99878fdd5a13107", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "知识产权示范企业", "baseNote": "知识产权示范企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 12, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "100bf3a4232d48caa81d84c5d46c8087", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "两化融合贯标企业", "baseNote": "两化融合贯标企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 13, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "6dd18764fd2c43d3864245da9f5ee3f0", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "中小企业“隐形冠军”", "baseNote": "中小企业“隐形冠军”", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 14, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "48b97458ac3d479ea1bf1159ea9b4ec6", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "技术先进型企业", "baseNote": "技术先进型企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 15, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "ebb2d719788d4646b8032d9f77fa9b15", "dictTypeNo": "QYZZ", "dictNo": "a50da8d4fffa438b9c7a3836bdb135-2", "dictParentNo": null, "baseParentId": "0", "baseName": "技术创新示范企业", "baseNote": "技术创新示范企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 16, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "4bf5a261f28544068ec2a7a30f99f934", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "准独角兽企业", "baseNote": "准独角兽企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 17, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "418eba9f6a164e2e9bc39434141aa500", "dictTypeNo": "QYZZ", "dictNo": "a50da8d4fffa438b9c7a3836bdb135f6", "dictParentNo": null, "baseParentId": "0", "baseName": "独角兽企业", "baseNote": "独角兽企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 18, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "ff5613db11194fe895ed58a43ef71973", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "新跨越民营企业", "baseNote": "新跨越民营企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 19, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "12cbb14db5e249ed8c6661c09e91dd87", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "智能制造标杆企业", "baseNote": "智能制造标杆企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 20, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "6a7e6f3127c8481c8b54fb9ad8f7a7e3", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "创新型示范企业", "baseNote": "创新型示范企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 21, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "3ae3873724234176accc76a5fb94eb43", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "大数据应用示范企业", "baseNote": "大数据应用示范企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 22, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "bb0910c1ebe84737ad0ce6d2cf7eb6b7", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "老字号企业", "baseNote": "老字号企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 23, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "c85c3dc8aa6d4780a0439ce773855793", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "知识产权贯标企业", "baseNote": "知识产权贯标企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 24, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "e438cf6980bd41ffabce109a10291c8d", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "农业科技型企业", "baseNote": "农业科技型企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 25, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "faa0ffb65be74a3c8c8f3631e0f431d3", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "农业龙头企业", "baseNote": "农业龙头企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 26, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "5994bd62337644b38fd9425435de250b", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "领军企业", "baseNote": "领军企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 27, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "c6c2ee28071f4757960c7d535cd63e2e", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "上云优秀企业", "baseNote": "上云优秀企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 28, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "fbb50ee9d2e949c1b7bf4151140af1ee", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "先进性服务企业", "baseNote": "先进性服务企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 29, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "a8be6654ac66467c9ed101c869a0cc01", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "知识产权密集型企业", "baseNote": "知识产权密集型企业", "baseCreateTime": "2023-05-04T16:00:01.000+0000", "baseUpdateTime": "2023-05-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 30, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "beceadfa990a417fba30c66700092661", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "科技企业孵化器", "baseNote": "科技企业孵化器", "baseCreateTime": "2023-07-04T16:00:01.000+0000", "baseUpdateTime": "2023-07-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 31, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}, {"baseId": "beceadfa990a417fba30c66700092662", "dictTypeNo": "QYZZ", "dictNo": null, "dictParentNo": null, "baseParentId": "0", "baseName": "科技企业众创空间", "baseNote": "科技企业众创空间", "baseCreateTime": "2023-07-04T16:00:01.000+0000", "baseUpdateTime": "2023-07-04T16:00:00.000+0000", "typeId": "4391d84c157d4586b8d5f38304a9191d", "sort": 32, "cssClass": null, "isDefault": "N", "status": "0", "creator": "", "updateBy": "", "tenantId": "", "isDelete": false, "children": null}]}