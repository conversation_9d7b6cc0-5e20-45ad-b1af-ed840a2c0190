// 公用模块html
headerBar()
footerBar()

// 全局变量
var jobId = getUrlParam('jobId') || '1';
var currentJobData = null;
var matchResults = [];
var filteredResults = [];

// 获取URL参数
function getUrlParam(name) {
    var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    var r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
}

// 页面初始化
$(document).ready(function() {
    loadJobDetail();
    loadMatchResults();
});

// 加载招聘信息详情
function loadJobDetail() {
    ajaxgetData('public/job/postings/' + jobId, {}, function(data) {
        if (data.code == 0 && data.data) {
            currentJobData = data.data;
            renderJobDetail(data.data);
        } else {
            console.error('获取招聘信息失败：', data.msg);
            showToast('获取招聘信息失败', 'error');
        }
    });
}

// 渲染招聘信息详情
function renderJobDetail(job) {
    document.getElementById('jobDetailTitle').textContent = job.jobTitle || '未知职位';
    
    // 格式化薪资显示
    var salaryText = '面议';
    if (job.salaryMin && job.salaryMax) {
        salaryText = '￥' + job.salaryMin + '-' + job.salaryMax + '/' + (job.salaryType || '月');
    } else if (job.salaryMin) {
        salaryText = '￥' + job.salaryMin + '+/' + (job.salaryType || '月');
    }
    document.getElementById('jobDetailSalary').textContent = salaryText;
    
    document.getElementById('jobDetailType').textContent = job.jobType || '--';
    document.getElementById('jobDetailLocation').textContent = job.workLocation || '--';
    document.getElementById('jobDetailCategory').textContent = job.jobCategory || '--';
    
    // 格式化发布时间
    if (job.createTime) {
        var createDate = new Date(job.createTime);
        document.getElementById('jobDetailPublishTime').textContent = createDate.toLocaleDateString();
    } else {
        document.getElementById('jobDetailPublishTime').textContent = '--';
    }
}

// 加载匹配结果
function loadMatchResults() {
    var loadingIndicator = document.getElementById('loadingIndicator');
    var resultsList = document.getElementById('matchResultsList');
    
    loadingIndicator.style.display = 'block';
    resultsList.innerHTML = '';
    
    ajaxgetData('public/job/postings/' + jobId + '/match-workers', {limit: 20}, function(data) {
        loadingIndicator.style.display = 'none';
        
        if (data.code == 0 && data.data) {
            matchResults = data.data;
            filteredResults = [...matchResults];
            renderMatchResults();
            updateMatchCount();
        } else {
            console.error('获取匹配结果失败：', data.msg);
            resultsList.innerHTML = '<div class="no-results"><p>暂无匹配的零工</p></div>';
        }
    });
}

// 渲染匹配结果
function renderMatchResults() {
    var resultsList = document.getElementById('matchResultsList');
    
    if (filteredResults.length === 0) {
        resultsList.innerHTML = '<div class="no-results"><p>暂无匹配的零工</p></div>';
        return;
    }
    
    var html = '';
    filteredResults.forEach(function(item) {
        var worker = item.worker;
        var similarity = item.similarityPercentage || 0;
        
        html += `
            <div class="matchResultItem">
                <div class="workerAvatar">
                    <img src="${worker.profilePhoto || '../public/images/default-avatar.png'}" alt="头像">
                </div>
                <div class="workerMainInfo">
                    <h3 class="workerName">${worker.realName || worker.nickname || '未知'}</h3>
                    <div class="workerTags">
                        ${generateWorkerTags(worker)}
                    </div>
                    <div class="workerStats">
                        <div class="workerStat">
                            <span class="statIcon">📍</span>
                            <span>${worker.currentLocation || '位置未知'}</span>
                        </div>
                        <div class="workerStat">
                            <span class="statIcon">💼</span>
                            <span>${worker.workExperienceYears || 0}年经验</span>
                        </div>
                        <div class="workerStat">
                            <span class="statIcon">⭐</span>
                            <span>${worker.ratingAverage || 0}分</span>
                        </div>
                        <div class="workerStat">
                            <span class="statIcon">✅</span>
                            <span>${worker.completedJobs || 0}个完成</span>
                        </div>
                    </div>
                </div>
                <div class="matchScoreSection">
                    <div class="similarityCircle" style="--percentage: ${similarity}%">
                        <div class="similarityInner">
                            <span class="similarityText">${similarity}%</span>
                        </div>
                    </div>
                    <p class="matchLabel">匹配度</p>
                </div>
                <div class="workerActions">
                    <button class="actionBtn btn-contact" onclick="contactWorker(${worker.workerId})">联系</button>
                    <button class="actionBtn btn-view" onclick="viewWorkerDetail(${worker.workerId})">查看详情</button>
                </div>
            </div>
        `;
    });
    
    resultsList.innerHTML = html;
}

// 生成零工标签
function generateWorkerTags(worker) {
    var tags = [];
    
    if (worker.workCategories) {
        var categories = worker.workCategories.split(',');
        categories.slice(0, 3).forEach(function(cat) {
            tags.push(`<span class="workerTag">${cat.trim()}</span>`);
        });
    }
    
    if (worker.isVerified == 1) {
        tags.push('<span class="workerTag" style="background: #e8f5e8; color: #2e7d32;">已认证</span>');
    }
    
    if (worker.healthCertificate) {
        tags.push('<span class="workerTag" style="background: #fff3e0; color: #f57c00;">健康证</span>');
    }
    
    return tags.join('');
}

// 更新匹配数量显示
function updateMatchCount() {
    var countElement = document.getElementById('matchCount');
    countElement.textContent = `找到 ${filteredResults.length} 个匹配的零工`;
}

// 排序匹配结果
function sortMatches() {
    var sortBy = document.getElementById('sortBy').value;
    
    filteredResults.sort(function(a, b) {
        switch(sortBy) {
            case 'similarity':
                return (b.similarityPercentage || 0) - (a.similarityPercentage || 0);
            case 'rating':
                return (b.worker.ratingAverage || 0) - (a.worker.ratingAverage || 0);
            case 'experience':
                return (b.worker.workExperienceYears || 0) - (a.worker.workExperienceYears || 0);
            case 'completedJobs':
                return (b.worker.completedJobs || 0) - (a.worker.completedJobs || 0);
            default:
                return 0;
        }
    });
    
    renderMatchResults();
}

// 筛选匹配结果
function filterMatches() {
    var minSimilarity = parseInt(document.getElementById('minSimilarity').value) || 0;
    
    filteredResults = matchResults.filter(function(item) {
        return (item.similarityPercentage || 0) >= minSimilarity;
    });
    
    renderMatchResults();
    updateMatchCount();
}

// 刷新匹配结果
function refreshMatches() {
    loadMatchResults();
}

// 联系零工
function contactWorker(workerId) {
    // 获取零工详情
    ajaxgetData('public/job/workers/' + workerId, {}, function(data) {
        if (data.code == 0 && data.data) {
            showContactModal(data.data);
        } else {
            showToast('获取零工信息失败', 'error');
        }
    });
}

// 显示联系模态框
function showContactModal(worker) {
    var modal = document.getElementById('contactModal');
    var contactInfo = document.getElementById('contactInfo');
    
    var infoHtml = `
        <h4>${worker.realName || worker.nickname || '未知'}</h4>
        <p><strong>手机：</strong>${worker.phone || '未提供'}</p>
        <p><strong>邮箱：</strong>${worker.email || '未提供'}</p>
        <p><strong>微信：</strong>${worker.wechat || '未提供'}</p>
        <p><strong>所在地：</strong>${worker.currentLocation || '未知'}</p>
    `;
    
    contactInfo.innerHTML = infoHtml;
    modal.style.display = 'flex';
}

// 关闭联系模态框
function closeContactModal() {
    document.getElementById('contactModal').style.display = 'none';
    document.getElementById('contactMessage').value = '';
}

// 发送消息
function sendMessage() {
    var message = document.getElementById('contactMessage').value.trim();
    if (!message) {
        showToast('请输入消息内容', 'warning');
        return;
    }
    
    // 这里应该调用发送消息的API
    showToast('消息发送成功！', 'success');
    closeContactModal();
}

// 查看零工详情
function viewWorkerDetail(workerId) {
    ajaxgetData('public/job/workers/' + workerId, {}, function(data) {
        if (data.code == 0 && data.data) {
            showWorkerDetailModal(data.data);
        } else {
            showToast('获取零工详情失败', 'error');
        }
    });
}

// 显示零工详情模态框
function showWorkerDetailModal(worker) {
    var modal = document.getElementById('workerDetailModal');
    var content = document.getElementById('workerDetailContent');
    
    var detailHtml = generateWorkerDetailHtml(worker);
    content.innerHTML = detailHtml;
    modal.style.display = 'flex';
}

// 生成零工详情HTML
function generateWorkerDetailHtml(worker) {
    return `
        <div class="worker-detail-content">
            <div class="worker-header">
                <img src="${worker.profilePhoto || '../public/images/default-avatar.png'}" alt="头像" class="worker-detail-avatar">
                <div class="worker-basic-info">
                    <h3>${worker.realName || worker.nickname || '未知'}</h3>
                    <p class="worker-title">${worker.workCategories || '暂无分类'}</p>
                    <div class="worker-rating">
                        <span class="rating-stars">${generateStars(worker.ratingAverage || 0)}</span>
                        <span class="rating-text">${worker.ratingAverage || 0}分 (${worker.ratingCount || 0}评价)</span>
                    </div>
                </div>
            </div>
            
            <div class="worker-sections">
                <div class="worker-section">
                    <h4>基本信息</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">性别：</span>
                            <span class="value">${worker.gender === 'male' ? '男' : worker.gender === 'female' ? '女' : '未知'}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">年龄：</span>
                            <span class="value">${worker.age || '未知'}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">学历：</span>
                            <span class="value">${worker.educationLevel || '未知'}</span>
                        </div>
                        <div class="info-item">
                            <span class="label">工作经验：</span>
                            <span class="value">${worker.workExperienceYears || 0}年</span>
                        </div>
                    </div>
                </div>
                
                <div class="worker-section">
                    <h4>工作信息</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="label">完成工作：</span>
                            <span class="value">${worker.completedJobs || 0}个</span>
                        </div>
                        <div class="info-item">
                            <span class="label">成功率：</span>
                            <span class="value">${worker.successRate || 0}%</span>
                        </div>
                        <div class="info-item">
                            <span class="label">期望薪资：</span>
                            <span class="value">${formatSalaryExpectation(worker)}</span>
                        </div>
                    </div>
                </div>
                
                ${worker.selfIntroduction ? `
                <div class="worker-section">
                    <h4>自我介绍</h4>
                    <p class="intro-text">${worker.selfIntroduction}</p>
                </div>
                ` : ''}
            </div>
        </div>
    `;
}

// 生成星级评分
function generateStars(rating) {
    var stars = '';
    var fullStars = Math.floor(rating);
    var hasHalfStar = rating % 1 >= 0.5;
    
    for (var i = 0; i < fullStars; i++) {
        stars += '⭐';
    }
    if (hasHalfStar) {
        stars += '⭐';
    }
    
    return stars;
}

// 格式化薪资期望
function formatSalaryExpectation(worker) {
    if (worker.salaryExpectationMin && worker.salaryExpectationMax) {
        return `￥${worker.salaryExpectationMin}-${worker.salaryExpectationMax}/${worker.salaryTypePreference || '月'}`;
    } else if (worker.salaryExpectationMin) {
        return `￥${worker.salaryExpectationMin}+/${worker.salaryTypePreference || '月'}`;
    }
    return '面议';
}

// 关闭零工详情模态框
function closeWorkerDetailModal() {
    document.getElementById('workerDetailModal').style.display = 'none';
}
